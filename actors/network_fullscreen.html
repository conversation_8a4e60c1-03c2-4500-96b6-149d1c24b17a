<!DOCTYPE html>
<html lang="ca">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xarxa d'Arquitectes COAC - Pantalla Completa</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            overflow: hidden;
            height: 100vh;
        }
        
        .top-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            z-index: 1000;
            border-bottom: 1px solid #333;
        }
        
        h1 {
            color: #fff;
            margin: 0 0 10px 0;
            font-size: 18px;
            text-align: center;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .search-container {
            position: relative;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #555;
            border-radius: 20px;
            background: #333;
            color: #fff;
            width: 250px;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #333;
            border: 1px solid #555;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }
        
        .search-result {
            padding: 8px 12px;
            cursor: pointer;
            color: #fff;
            border-bottom: 1px solid #555;
            font-size: 12px;
        }
        
        .search-result:hover {
            background: #555;
        }
        
        .controls button {
            padding: 6px 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
        
        .controls button.active {
            background: #28a745;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 5px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
            color: #ccc;
        }
        
        .legend-circle {
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
        
        #network {
            width: 100vw;
            height: 100vh;
            background: #1a1a1a;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            border: 1px solid #555;
            max-width: 200px;
        }
        
        .node {
            cursor: pointer;
            stroke: #333;
            stroke-width: 1px;
        }
        
        .node.highlighted {
            stroke: #ffff00;
            stroke-width: 3px;
        }
        
        .link {
            stroke: #555;
            stroke-opacity: 0.6;
        }
        
        .node-text {
            font-size: 10px;
            fill: #fff;
            text-anchor: middle;
            pointer-events: none;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        
        .zoom-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 1000;
        }
        
        .zoom-btn {
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border: 1px solid #555;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .zoom-btn:hover {
            background: rgba(0, 123, 255, 0.8);
        }
        
        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            border: 1px solid #555;
        }
    </style>
</head>
<body>
    <div class="top-panel">
        <h1>Xarxa Relacional d'Arquitectes COAC</h1>
        
        <div class="controls">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Cercar arquitecte, estudi o projecte..." id="searchInput">
                <div class="search-results" id="searchResults"></div>
            </div>
            
            <button onclick="resetView()">Reset Vista</button>
            <button onclick="toggleLabels()" id="labelBtn">Mostrar Etiquetes</button>
            <button onclick="filterByType('all')" class="active" id="filterAll">Tots</button>
            <button onclick="filterByType('Persona')" id="filterPersona">Persones</button>
            <button onclick="filterByType('Societat')" id="filterSocietat">Societats</button>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-circle" style="background: #ff6b6b;"></div>
                <span>Persones</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #4ecdc4;"></div>
                <span>Societats</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #45b7d1;"></div>
                <span>Entitats</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #96ceb4;"></div>
                <span>Grups</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #feca57;"></div>
                <span>Organitzacions</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #ff9ff3;"></div>
                <span>Projectes</span>
            </div>
        </div>
    </div>
    
    <svg id="network"></svg>
    
    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">+</button>
        <button class="zoom-btn" onclick="zoomOut()">−</button>
        <button class="zoom-btn" onclick="resetZoom()" style="font-size: 12px;">⌂</button>
    </div>
    
    <div class="info-panel">
        <div>Nodes: <span id="nodeCount">0</span></div>
        <div>Connexions: <span id="linkCount">0</span></div>
        <div>Zoom: <span id="zoomLevel">100%</span></div>
    </div>

    <script>
        // Variables globals
        let svg, g, simulation, nodes, links, nodeElements, linkElements, labelElements;
        let showLabels = false;
        let currentFilter = 'all';
        let tooltip;
        let zoom;
        let width, height;
        
        // Configuració de colors per tipus
        const colors = {
            'Persona': '#ff6b6b',
            'Societat': '#4ecdc4',
            'Entitat': '#45b7d1',
            'Grup': '#96ceb4',
            'memberOf': '#feca57',
            'event': '#ff9ff3'
        };

        // Colors per inScheme (borders de persones)
        const inSchemeColors = {
            'Arquitectes i col·laboradors': '#e74c3c',
            'Societats': '#3498db',
            'Entitats': '#9b59b6',
            'Grups': '#2ecc71'
        };
        
        // Inicialitzar
        function init() {
            width = window.innerWidth;
            height = window.innerHeight;
            
            svg = d3.select("#network")
                .attr("width", width)
                .attr("height", height);
            
            // Configurar zoom
            zoom = d3.zoom()
                .scaleExtent([0.1, 10])
                .on("zoom", handleZoom);
            
            svg.call(zoom);
            
            // Grup principal per zoom
            g = svg.append("g");
            
            // Crear tooltip
            tooltip = d3.select("body").append("div")
                .attr("class", "tooltip")
                .style("opacity", 0);
            
            // Configurar cercador
            setupSearch();
            
            // Carregar dades
            d3.json("actors.json").then(function(data) {
                processData(data.actors);
                createVisualization();
                updateInfo();
            });
        }
        
        function handleZoom(event) {
            g.attr("transform", event.transform);
            const scale = Math.round(event.transform.k * 100);
            document.getElementById('zoomLevel').textContent = scale + '%';
        }
        
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');
            
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase().trim();
                
                if (query.length < 2) {
                    searchResults.style.display = 'none';
                    return;
                }
                
                const matches = nodes.filter(node => 
                    node.label.toLowerCase().includes(query)
                ).slice(0, 10);
                
                if (matches.length > 0) {
                    searchResults.innerHTML = matches.map(node => 
                        `<div class="search-result" onclick="highlightNode('${node.id}')">${node.label} (${node.type})</div>`
                    ).join('');
                    searchResults.style.display = 'block';
                } else {
                    searchResults.style.display = 'none';
                }
            });
            
            // Tancar resultats quan es clica fora
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-container')) {
                    searchResults.style.display = 'none';
                }
            });
        }
        
        function processData(actors) {
            nodes = [];
            links = [];
            const nodeMap = new Map();

            // Processar actors
            actors.forEach(actor => {
                // Calcular connexions de l'actor
                let actorConnections = 0;
                if (actor.memberOf) actorConnections += actor.memberOf.length;
                if (actor.events) actorConnections += actor.events.length;

                const actorNode = {
                    id: actor.metadata.idInSource,
                    label: actor.name,
                    type: actor.type,
                    group: actor.type,
                    size: actorConnections + 1, // +1 per evitar size 0
                    inScheme: actor.inScheme ? actor.inScheme.label : null,
                    x: Math.random() * width,
                    y: Math.random() * height
                };
                nodes.push(actorNode);
                nodeMap.set(actor.metadata.idInSource, actorNode);

                // Processar memberOf
                if (actor.memberOf && actor.memberOf.length > 0) {
                    actor.memberOf.forEach(member => {
                        const memberId = `memberOf_${member.slug}`;

                        if (!nodeMap.has(memberId)) {
                            const memberNode = {
                                id: memberId,
                                label: member.label,
                                type: 'memberOf',
                                group: 'memberOf',
                                size: 1,
                                x: Math.random() * width,
                                y: Math.random() * height
                            };
                            nodes.push(memberNode);
                            nodeMap.set(memberId, memberNode);
                        }

                        nodeMap.get(memberId).size++;

                        links.push({
                            source: actor.metadata.idInSource,
                            target: memberId,
                            type: 'memberOf'
                        });
                    });
                }

                // Processar events
                if (actor.events && actor.events.length > 0) {
                    actor.events.forEach(event => {
                        const eventId = `event_${event.slug}`;

                        if (!nodeMap.has(eventId)) {
                            const eventNode = {
                                id: eventId,
                                label: event.label,
                                type: 'event',
                                group: 'event',
                                size: 1,
                                x: Math.random() * width,
                                y: Math.random() * height
                            };
                            nodes.push(eventNode);
                            nodeMap.set(eventId, eventNode);
                        }

                        nodeMap.get(eventId).size++;

                        links.push({
                            source: actor.metadata.idInSource,
                            target: eventId,
                            type: 'event'
                        });
                    });
                }
            });
        }

        function createVisualization() {
            // Crear simulació més suau
            simulation = d3.forceSimulation(nodes)
                .force("link", d3.forceLink(links).id(d => d.id).distance(80).strength(0.1))
                .force("charge", d3.forceManyBody().strength(-50))
                .force("center", d3.forceCenter(width / 2, height / 2))
                .force("collision", d3.forceCollide().radius(d => {
                    if (d.size > 20) return Math.sqrt(d.size) * 6 + 14;
                    if (d.size > 10) return Math.sqrt(d.size) * 5 + 10;
                    if (d.size > 5) return Math.sqrt(d.size) * 4 + 7;
                    return Math.sqrt(d.size) * 4 + 5;
                }))
                .alpha(0.3)
                .alphaDecay(0.02);

            // Crear enllaços
            linkElements = g.append("g")
                .attr("class", "links")
                .selectAll("line")
                .data(links)
                .enter().append("line")
                .attr("class", "link")
                .attr("stroke-width", 1);

            // Crear nodes
            nodeElements = g.append("g")
                .attr("class", "nodes")
                .selectAll("circle")
                .data(nodes)
                .enter().append("circle")
                .attr("class", "node")
                .attr("r", d => {
                    // Escala més dramàtica per nodes amb moltes connexions
                    if (d.size > 20) return Math.sqrt(d.size) * 6 + 12;
                    if (d.size > 10) return Math.sqrt(d.size) * 5 + 8;
                    if (d.size > 5) return Math.sqrt(d.size) * 4 + 5;
                    return Math.sqrt(d.size) * 3 + 3;
                })
                .attr("fill", d => colors[d.type] || '#999')
                .attr("stroke", d => {
                    // Border diferent per persones segons inScheme
                    if (d.type === 'Persona' && d.inScheme) {
                        return inSchemeColors[d.inScheme] || '#333';
                    }
                    return 'none';
                })
                .attr("stroke-width", d => d.type === 'Persona' ? 3 : 0)
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended))
                .on("mouseover", function(event, d) {
                    highlightConnectedNodes(d);
                    showTooltip(event, d);
                })
                .on("mouseout", function(event, d) {
                    resetHighlight();
                    hideTooltip(event, d);
                })
                .on("click", function(event, d) {
                    highlightNode(d.id);
                });

            // Crear etiquetes
            labelElements = g.append("g")
                .attr("class", "labels")
                .selectAll("text")
                .data(nodes)
                .enter().append("text")
                .attr("class", "node-text")
                .text(d => d.size > 5 ? d.label : '')
                .style("opacity", 0);

            // Actualitzar posicions
            simulation.on("tick", ticked);

            // Aturar simulació després d'un temps
            setTimeout(() => {
                simulation.stop();
            }, 5000);
        }

        function ticked() {
            linkElements
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            nodeElements
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            labelElements
                .attr("x", d => d.x)
                .attr("y", d => d.y + 4);
        }

        // Funcions de drag
        function dragstarted(event, d) {
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
            d.x = event.x;
            d.y = event.y;
            ticked();
        }

        function dragended(event, d) {
            d.fx = null;
            d.fy = null;
        }

        // Funcions de tooltip
        function showTooltip(event, d) {
            tooltip.transition()
                .duration(200)
                .style("opacity", .9);
            tooltip.html(`<strong>${d.label}</strong><br/>Tipus: ${d.type}<br/>Connexions: ${d.size - 1}`)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 28) + "px");
        }

        function hideTooltip() {
            tooltip.transition()
                .duration(500)
                .style("opacity", 0);
        }

        // Funcions de control
        function highlightNode(nodeId) {
            // Eliminar highlights anteriors
            nodeElements.classed("highlighted", false);

            // Highlight node seleccionat
            const node = nodes.find(n => n.id === nodeId);
            if (node) {
                nodeElements.filter(d => d.id === nodeId).classed("highlighted", true);

                // Centrar vista en el node
                const transform = d3.zoomIdentity
                    .translate(width / 2 - node.x, height / 2 - node.y)
                    .scale(1.5);

                svg.transition()
                    .duration(750)
                    .call(zoom.transform, transform);
            }

            // Tancar resultats de cerca
            document.getElementById('searchResults').style.display = 'none';
            document.getElementById('searchInput').value = '';
        }

        function resetView() {
            svg.transition()
                .duration(750)
                .call(zoom.transform, d3.zoomIdentity);

            nodeElements.classed("highlighted", false);
        }

        function toggleLabels() {
            showLabels = !showLabels;
            labelElements.style("opacity", showLabels ? 1 : 0);
            document.getElementById('labelBtn').textContent = showLabels ? 'Ocultar Etiquetes' : 'Mostrar Etiquetes';
        }

        function filterByType(type) {
            currentFilter = type;

            // Actualitzar botons
            document.querySelectorAll('.controls button').forEach(btn => btn.classList.remove('active'));
            document.getElementById('filter' + (type === 'all' ? 'All' : type)).classList.add('active');

            nodeElements.style("opacity", d => {
                if (type === 'all') return 1;
                return d.type === type ? 1 : 0.1;
            });

            linkElements.style("opacity", d => {
                if (type === 'all') return 0.6;
                const sourceNode = nodes.find(n => n.id === d.source.id);
                const targetNode = nodes.find(n => n.id === d.target.id);
                return (sourceNode.type === type || targetNode.type === type) ? 0.6 : 0.1;
            });
        }

        // Funcions de zoom
        function zoomIn() {
            svg.transition().call(zoom.scaleBy, 1.5);
        }

        function zoomOut() {
            svg.transition().call(zoom.scaleBy, 1 / 1.5);
        }

        function resetZoom() {
            svg.transition().call(zoom.transform, d3.zoomIdentity);
        }

        function updateInfo() {
            document.getElementById('nodeCount').textContent = nodes.length;
            document.getElementById('linkCount').textContent = links.length;
        }

        // Redimensionar en canviar finestra
        window.addEventListener('resize', function() {
            width = window.innerWidth;
            height = window.innerHeight;
            svg.attr("width", width).attr("height", height);
            simulation.force("center", d3.forceCenter(width / 2, height / 2));
        });

        init();
    </script>
</body>
</html>
