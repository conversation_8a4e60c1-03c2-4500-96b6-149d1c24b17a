#!/usr/bin/env python3
"""
Script per comprovar elements repetits en el fitxer actors.json
"""

import json
from collections import Counter, defaultdict

def check_duplicates():
    print("🔍 Comprovant duplicats en actors.json...")
    
    # Carregar dades
    with open('actors.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    actors = data.get('actors', [])
    total_actors = len(actors)
    print(f"📊 Total actors: {total_actors}")
    
    # Comprovar duplicats per diferents camps
    duplicates_found = False
    
    # 1. Duplicats per slug
    print("\n1️⃣ Comprovant duplicats per SLUG:")
    slugs = [actor.get('slug') for actor in actors if actor.get('slug')]
    slug_counts = Counter(slugs)
    duplicate_slugs = {slug: count for slug, count in slug_counts.items() if count > 1}
    
    if duplicate_slugs:
        duplicates_found = True
        print(f"❌ Trobats {len(duplicate_slugs)} slugs duplicats:")
        for slug, count in duplicate_slugs.items():
            print(f"   - '{slug}': {count} vegades")
    else:
        print("✅ No hi ha slugs duplicats")
    
    # 2. Duplicats per nom
    print("\n2️⃣ Comprovant duplicats per NOM:")
    names = [actor.get('name') for actor in actors if actor.get('name')]
    name_counts = Counter(names)
    duplicate_names = {name: count for name, count in name_counts.items() if count > 1}
    
    if duplicate_names:
        duplicates_found = True
        print(f"❌ Trobats {len(duplicate_names)} noms duplicats:")
        for name, count in sorted(duplicate_names.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   - '{name}': {count} vegades")
        if len(duplicate_names) > 10:
            print(f"   ... i {len(duplicate_names) - 10} més")
    else:
        print("✅ No hi ha noms duplicats")
    
    # 3. Duplicats per idInSource
    print("\n3️⃣ Comprovant duplicats per ID_IN_SOURCE:")
    ids = [actor.get('metadata', {}).get('idInSource') for actor in actors 
           if actor.get('metadata', {}).get('idInSource')]
    id_counts = Counter(ids)
    duplicate_ids = {id_val: count for id_val, count in id_counts.items() if count > 1}
    
    if duplicate_ids:
        duplicates_found = True
        print(f"❌ Trobats {len(duplicate_ids)} idInSource duplicats:")
        for id_val, count in duplicate_ids.items():
            print(f"   - '{id_val}': {count} vegades")
    else:
        print("✅ No hi ha idInSource duplicats")
    
    # 4. Actors completament idèntics
    print("\n4️⃣ Comprovant actors completament idèntics:")
    actor_strings = []
    for i, actor in enumerate(actors):
        # Crear string únic per cada actor (sense metadata que pot variar)
        actor_key = {
            'name': actor.get('name'),
            'slug': actor.get('slug'),
            'inScheme': actor.get('inScheme', {}).get('label') if actor.get('inScheme') else None
        }
        actor_strings.append((json.dumps(actor_key, sort_keys=True), i))
    
    string_counts = Counter([s[0] for s in actor_strings])
    duplicate_actors = {string: count for string, count in string_counts.items() if count > 1}
    
    if duplicate_actors:
        duplicates_found = True
        print(f"❌ Trobats {len(duplicate_actors)} grups d'actors idèntics:")
        for actor_string, count in list(duplicate_actors.items())[:5]:
            actor_data = json.loads(actor_string)
            print(f"   - {actor_data['name']} ({actor_data['slug']}): {count} vegades")
        if len(duplicate_actors) > 5:
            print(f"   ... i {len(duplicate_actors) - 5} grups més")
    else:
        print("✅ No hi ha actors completament idèntics")
    
    # 5. Estadístiques de memberOf i events
    print("\n5️⃣ Estadístiques de memberOf i events:")
    
    # memberOf
    all_memberof = []
    for actor in actors:
        if actor.get('memberOf'):
            for member in actor['memberOf']:
                if member.get('label'):
                    all_memberof.append(member['label'])
    
    memberof_counts = Counter(all_memberof)
    print(f"📈 Total memberOf únics: {len(memberof_counts)}")
    print(f"📈 memberOf més freqüents:")
    for label, count in memberof_counts.most_common(5):
        print(f"   - '{label}': {count} actors")
    
    # events
    all_events = []
    for actor in actors:
        if actor.get('events'):
            for event in actor['events']:
                if event.get('label') and event.get('slug'):
                    all_events.append(event['label'])
    
    event_counts = Counter(all_events)
    print(f"📈 Total events únics: {len(event_counts)}")
    print(f"📈 Events més freqüents:")
    for label, count in event_counts.most_common(5):
        print(f"   - '{label}': {count} actors")
    
    # Resum final
    print(f"\n{'='*50}")
    if duplicates_found:
        print("❌ S'han trobat elements duplicats (veure detalls a dalt)")
    else:
        print("✅ No s'han trobat duplicats significatius")
    
    print(f"📊 Resum:")
    print(f"   - Total actors: {total_actors}")
    print(f"   - Slugs únics: {len(set(slugs))}")
    print(f"   - Noms únics: {len(set(names))}")
    print(f"   - IDs únics: {len(set(ids))}")
    print(f"   - MemberOf únics: {len(memberof_counts)}")
    print(f"   - Events únics: {len(event_counts)}")

if __name__ == "__main__":
    check_duplicates()
