#!/usr/bin/env python3
"""
Neteja els camps de metadades no desitjats
"""

import json
from datetime import datetime

def clean_metadata():
    """Elimina camps de metadades no desitjats"""
    
    print("📂 Carregant fitxer actors_show_true.json...")
    
    try:
        with open('actors_show_true.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ Error carregant fitxer: {e}")
        return
    
    actors = data.get('actors', [])
    print(f"📋 Total actors a netejar: {len(actors)}")
    
    # Camps a eliminar
    fields_to_remove = [
        'updatedAt',
        'version', 
        'createdAt',
        'publishedAt',
        'slugs'
    ]
    
    # Camps d'imatges a eliminar
    image_fields_to_remove = [
        'small',
        'original'
    ]
    
    cleaned_actors = []
    
    for i, actor in enumerate(actors, 1):
        if i % 500 == 0:
            print(f"🧹 Netejant actor {i}/{len(actors)} ({(i/len(actors)*100):.1f}%)")
        
        # <PERSON><PERSON>r còpia de l'actor
        cleaned_actor = actor.copy()
        
        # Netejar metadades
        if 'metadata' in cleaned_actor:
            metadata = cleaned_actor['metadata'].copy()
            
            # Eliminar camps especificats
            for field in fields_to_remove:
                if field in metadata:
                    del metadata[field]
            
            cleaned_actor['metadata'] = metadata
        
        # Netejar portraitMedia si existeix
        if 'portraitMedia' in cleaned_actor and cleaned_actor['portraitMedia']:
            portrait = cleaned_actor['portraitMedia'].copy()
            
            # Eliminar camps d'imatges especificats
            for field in image_fields_to_remove:
                if field in portrait:
                    del portrait[field]
            
            cleaned_actor['portraitMedia'] = portrait
        
        cleaned_actors.append(cleaned_actor)
    
    # Preparar dades finals
    cleaned_data = {
        'metadata': {
            'total_actors': len(cleaned_actors),
            'filter_applied': 'show: true',
            'cleaned_fields': fields_to_remove + image_fields_to_remove,
            'cleaned_at': datetime.now().isoformat(),
            'original_source': data.get('metadata', {}).get('source_api', 'Unknown')
        },
        'actors': cleaned_actors
    }
    
    # Guardar fitxer net
    with open('actors_show_true_clean.json', 'w', encoding='utf-8') as f:
        json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 Actors nets guardats a: actors_show_true_clean.json")
    
    # Crear resum de la neteja
    original_size = len(json.dumps(data, ensure_ascii=False))
    cleaned_size = len(json.dumps(cleaned_data, ensure_ascii=False))
    reduction = ((original_size - cleaned_size) / original_size) * 100
    
    summary = {
        'neteja': {
            'total_actors': len(cleaned_actors),
            'camps_eliminats': fields_to_remove + image_fields_to_remove,
            'mida_original_bytes': original_size,
            'mida_neta_bytes': cleaned_size,
            'reducció_percentatge': round(reduction, 1),
            'data_neteja': cleaned_data['metadata']['cleaned_at']
        },
        'mostra_actors_nets': [
            {
                'name': actor.get('name'),
                'show': actor.get('show'),
                'metadata_keys': list(actor.get('metadata', {}).keys()) if actor.get('metadata') else [],
                'portraitMedia_keys': list(actor.get('portraitMedia', {}).keys()) if actor.get('portraitMedia') else []
            }
            for actor in cleaned_actors[:5]  # Primers 5 actors
        ]
    }
    
    with open('resum_neteja.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"📊 Resum de neteja guardat a: resum_neteja.json")
    
    print(f"\n✅ Neteja completada!")
    print(f"📈 Estadístiques:")
    print(f"   - Actors processats: {len(cleaned_actors)}")
    print(f"   - Camps eliminats: {', '.join(fields_to_remove + image_fields_to_remove)}")
    print(f"   - Reducció de mida: {reduction:.1f}%")
    print(f"   - Fitxer net: actors_show_true_clean.json")

if __name__ == "__main__":
    print("🧹 Netejant metadades dels actors COAC\n")
    clean_metadata()
