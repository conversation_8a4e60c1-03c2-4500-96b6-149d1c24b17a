#!/usr/bin/env python3
"""
Extreu TOTS els actors de COAC amb paginació
"""

import requests
import json
import time
from datetime import datetime

def get_all_actors():
    """Obté tots els actors amb paginació usant els enllaços next"""

    base_url = "https://api.coeli.cat"
    initial_url = "https://api.coeli.cat/coeli/COAC-PRO/Actor/search/onestep"

    headers = {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Accept-Language': 'ca',
        'Authorization': 'Authorization 00a1fd57e3c0a3e203501ba05f742e78b4294b10868eac3c6e1fe82e62cd1b7c',
        'User-Agent': 'PostmanRuntime/7.44.1'
    }

    all_entities = []
    current_url = initial_url
    page_num = 1

    print("🚀 Iniciant extracció de TOTS els actors amb paginació per enllaços...")

    while current_url:
        print(f"📄 Obtenint pàgina {page_num}...")
        print(f"🔗 URL: {current_url}")

        # Body inicial per la primera petició
        body = {
            "conditions": [],
            "sortCondition": {
                "sort": [
                    {
                        "name": "indexName",
                        "order": "ASC"
                    }
                ],
                "group": []
            }
        }

        try:
            # Si és la primera pàgina, usar POST amb body
            if current_url == initial_url:
                response = requests.post(current_url, headers=headers, json=body, timeout=30)
            else:
                # Per pàgines següents, usar GET amb la URL completa
                response = requests.get(current_url, headers=headers, timeout=30)

            if response.status_code != 200:
                print(f"❌ Error {response.status_code}: {response.text}")
                break

            data = response.json()
            entities = data.get('entities', [])
            page_info = data.get('page', {})
            links = data.get('_links', {})

            print(f"📦 Obtingudes {len(entities)} entitats")
            print(f"📊 Total: {page_info.get('total', 0)}, Offset: {page_info.get('offset', 0)}")

            if not entities:
                print("📭 No hi ha més entitats")
                break

            all_entities.extend(entities)

            total = page_info.get('total', 0)
            max_offset = page_info.get('maxOffset', 10000)
            current_offset = page_info.get('offset', 0)

            print(f"📈 Progrés: {len(all_entities)}/{total} actors ({(len(all_entities)/total*100):.1f}%)")

            # Comprovar si hi ha següent pàgina
            next_link = links.get('next')
            if next_link and current_offset < max_offset:
                current_url = f"{base_url}{next_link}"
                page_num += 1
                print(f"➡️  Següent pàgina: {next_link}")
            else:
                print("🏁 No hi ha més pàgines o arribat al maxOffset")
                break

            time.sleep(0.5)  # Pausa entre peticions

        except Exception as e:
            print(f"❌ Error: {e}")
            break

    print(f"🎉 Total actors obtinguts: {len(all_entities)}")
    return all_entities

def extract_actor_data(entity):
    """Extreu les dades específiques d'un actor"""
    extracted = {
        'name': entity.get('name'),
        'show': entity.get('show'),
        'label': entity.get('label'),
        'indexName': entity.get('indexName'),
        'slug': entity.get('slug'),
        'display': entity.get('display'),
        'type': entity.get('type', {}).get('label') if entity.get('type') else None,
        'memberOf': [],
        'events': [],
        'metadata': entity.get('$metadata', {}),
        'portraitMedia': entity.get('portraitMedia')
    }
    
    # Extreure memberOf (companyies)
    if entity.get('memberOf'):
        for member in entity['memberOf']:
            extracted['memberOf'].append({
                'label': member.get('label'),
                'slug': member.get('slug'),
                'href': member.get('href')
            })
    
    # Extreure events (obres)
    if entity.get('events'):
        for event in entity['events']:
            extracted['events'].append({
                'label': event.get('label'),
                'slug': event.get('$slug'),
                'href': event.get('href')
            })
    
    return extracted

def process_and_save_actors():
    """Processa tots els actors i guarda els resultats"""
    
    # Obtenir tots els actors
    all_entities = get_all_actors()
    
    if not all_entities:
        print("❌ No s'han pogut obtenir actors")
        return
    
    print(f"\n📋 Processant {len(all_entities)} actors...")
    
    processed_actors = []
    
    for i, entity in enumerate(all_entities, 1):
        if i % 500 == 0:  # Mostrar progrés cada 500 actors
            print(f"🔄 Processant actor {i}/{len(all_entities)} ({(i/len(all_entities)*100):.1f}%)")
        
        try:
            actor_data = extract_actor_data(entity)
            processed_actors.append(actor_data)
        except Exception as e:
            print(f"❌ Error processant actor {i}: {e}")
            # Afegir actor amb error
            processed_actors.append({
                'name': entity.get('name', f'Error_{i}'),
                'error': str(e)
            })
    
    # Preparar dades finals
    final_data = {
        'metadata': {
            'total_actors': len(processed_actors),
            'extracted_at': datetime.now().isoformat(),
            'source_api': 'https://api.coeli.cat/coeli/COAC-PRO/Actor/search/onestep'
        },
        'actors': processed_actors
    }
    
    # Guardar fitxer principal
    with open('tots_actors_coac.json', 'w', encoding='utf-8') as f:
        json.dump(final_data, f, ensure_ascii=False, indent=2)

    print(f"💾 Tots els actors guardats a: tots_actors_coac.json")
    
    # Crear resum
    actors_with_companies = len([a for a in processed_actors if a.get('memberOf')])
    actors_with_events = len([a for a in processed_actors if a.get('events')])
    actors_with_show_true = len([a for a in processed_actors if a.get('show') == True])
    
    summary = {
        'resum': {
            'total_actors': len(processed_actors),
            'data_extracció': final_data['metadata']['extracted_at'],
            'actors_amb_companyies': actors_with_companies,
            'actors_amb_obres': actors_with_events,
            'actors_visibles': actors_with_show_true
        },
        'mostra_actors': [
            {
                'name': actor.get('name'),
                'show': actor.get('show'),
                'companyies': len(actor.get('memberOf', [])),
                'obres': len(actor.get('events', []))
            }
            for actor in processed_actors[:10]  # Primers 10 actors
        ]
    }
    
    with open('resum_actors.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"📊 Resum guardat a: resum_actors.json")
    
    print(f"\n✅ Extracció completada!")
    print(f"📈 Estadístiques:")
    print(f"   - Total actors: {len(processed_actors)}")
    print(f"   - Actors amb companyies: {actors_with_companies}")
    print(f"   - Actors amb obres: {actors_with_events}")
    print(f"   - Actors visibles (show=true): {actors_with_show_true}")

if __name__ == "__main__":
    print("🚀 Extracció completa d'actors COAC\n")
    process_and_save_actors()
