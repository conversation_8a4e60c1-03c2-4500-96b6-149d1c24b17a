<!DOCTYPE html>
<html lang="ca">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xarxa d'Arquitectes COAC</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            overflow: hidden;
            height: 100vh;
        }

        .container {
            width: 100vw;
            height: 100vh;
            background: #1a1a1a;
            position: relative;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .controls button {
            margin: 0 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-circle {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        #network {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
        }
        
        .node {
            cursor: pointer;
        }
        
        .link {
            stroke: #999;
            stroke-opacity: 0.6;
        }
        
        .node-text {
            font-size: 10px;
            fill: #333;
            text-anchor: middle;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Xarxa Relacional d'Arquitectes COAC</h1>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-circle" style="background: #ff6b6b;"></div>
                <span>Arquitectes (Persona)</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #4ecdc4;"></div>
                <span>Estudis (Societat)</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #45b7d1;"></div>
                <span>Institucions (Entitat)</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #96ceb4;"></div>
                <span>Grups</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #feca57;"></div>
                <span>Organitzacions (memberOf)</span>
            </div>
            <div class="legend-item">
                <div class="legend-circle" style="background: #ff9ff3;"></div>
                <span>Esdeveniments/Projectes</span>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="restartSimulation()">Reiniciar Simulació</button>
            <button onclick="toggleLabels()">Mostrar/Ocultar Etiquetes</button>
            <button onclick="filterByType('all')">Tots</button>
            <button onclick="filterByType('Persona')">Només Persones</button>
            <button onclick="filterByType('Societat')">Només Societats</button>
        </div>
        
        <svg id="network" width="1160" height="800"></svg>
    </div>

    <script>
        // Variables globals
        let svg, simulation, nodes, links, nodeElements, linkElements, labelElements;
        let showLabels = false;
        let currentFilter = 'all';
        let tooltip;
        
        // Configuració de colors per tipus
        const colors = {
            'Persona': '#ff6b6b',
            'Societat': '#4ecdc4', 
            'Entitat': '#45b7d1',
            'Grup': '#96ceb4',
            'memberOf': '#feca57',
            'event': '#ff9ff3'
        };
        
        // Inicialitzar SVG
        svg = d3.select("#network");
        const width = +svg.attr("width");
        const height = +svg.attr("height");
        
        // Crear tooltip
        tooltip = d3.select("body").append("div")
            .attr("class", "tooltip")
            .style("opacity", 0);
        
        // Carregar dades
        d3.json("actors.json").then(function(data) {
            processData(data.actors);
            createVisualization();
        });
        
        function processData(actors) {
            nodes = [];
            links = [];
            const nodeMap = new Map();
            
            // Processar actors
            actors.forEach(actor => {
                const actorNode = {
                    id: actor.metadata.idInSource,
                    label: actor.name,
                    type: actor.type,
                    group: actor.type,
                    size: 1
                };
                nodes.push(actorNode);
                nodeMap.set(actor.metadata.idInSource, actorNode);
                
                // Processar memberOf
                if (actor.memberOf && actor.memberOf.length > 0) {
                    actor.memberOf.forEach(member => {
                        const memberId = `memberOf_${member.slug}`;
                        
                        if (!nodeMap.has(memberId)) {
                            const memberNode = {
                                id: memberId,
                                label: member.label,
                                type: 'memberOf',
                                group: 'memberOf',
                                size: 1
                            };
                            nodes.push(memberNode);
                            nodeMap.set(memberId, memberNode);
                        }
                        
                        // Incrementar mida del node memberOf
                        nodeMap.get(memberId).size++;
                        
                        links.push({
                            source: actor.metadata.idInSource,
                            target: memberId,
                            type: 'memberOf'
                        });
                    });
                }
                
                // Processar events
                if (actor.events && actor.events.length > 0) {
                    actor.events.forEach(event => {
                        const eventId = `event_${event.slug}`;
                        
                        if (!nodeMap.has(eventId)) {
                            const eventNode = {
                                id: eventId,
                                label: event.label,
                                type: 'event',
                                group: 'event',
                                size: 1
                            };
                            nodes.push(eventNode);
                            nodeMap.set(eventId, eventNode);
                        }
                        
                        // Incrementar mida del node event
                        nodeMap.get(eventId).size++;
                        
                        links.push({
                            source: actor.metadata.idInSource,
                            target: eventId,
                            type: 'event'
                        });
                    });
                }
            });
            
            console.log(`Nodes: ${nodes.length}, Links: ${links.length}`);
        }
        
        function createVisualization() {
            // Crear simulació de forces
            simulation = d3.forceSimulation(nodes)
                .force("link", d3.forceLink(links).id(d => d.id).distance(50))
                .force("charge", d3.forceManyBody().strength(-100))
                .force("center", d3.forceCenter(width / 2, height / 2))
                .force("collision", d3.forceCollide().radius(d => Math.sqrt(d.size) * 3 + 2));
            
            // Crear enllaços
            linkElements = svg.append("g")
                .attr("class", "links")
                .selectAll("line")
                .data(links)
                .enter().append("line")
                .attr("class", "link")
                .attr("stroke-width", 1);
            
            // Crear nodes
            nodeElements = svg.append("g")
                .attr("class", "nodes")
                .selectAll("circle")
                .data(nodes)
                .enter().append("circle")
                .attr("class", "node")
                .attr("r", d => Math.sqrt(d.size) * 3 + 2)
                .attr("fill", d => colors[d.type] || '#999')
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended))
                .on("mouseover", function(event, d) {
                    tooltip.transition()
                        .duration(200)
                        .style("opacity", .9);
                    tooltip.html(`<strong>${d.label}</strong><br/>Tipus: ${d.type}<br/>Connexions: ${d.size - 1}`)
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 28) + "px");
                })
                .on("mouseout", function(d) {
                    tooltip.transition()
                        .duration(500)
                        .style("opacity", 0);
                });
            
            // Crear etiquetes (inicialment ocultes)
            labelElements = svg.append("g")
                .attr("class", "labels")
                .selectAll("text")
                .data(nodes)
                .enter().append("text")
                .attr("class", "node-text")
                .text(d => d.size > 3 ? d.label : '') // Només mostrar etiquetes per nodes amb moltes connexions
                .style("opacity", 0);
            
            // Actualitzar posicions en cada tick
            simulation.on("tick", ticked);
        }
        
        function ticked() {
            linkElements
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);
            
            nodeElements
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);
            
            labelElements
                .attr("x", d => d.x)
                .attr("y", d => d.y + 4);
        }
        
        // Funcions de drag
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }
        
        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }
        
        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
        
        // Funcions de control
        function restartSimulation() {
            simulation.alpha(1).restart();
        }
        
        function toggleLabels() {
            showLabels = !showLabels;
            labelElements.style("opacity", showLabels ? 1 : 0);
        }
        
        function filterByType(type) {
            currentFilter = type;
            
            nodeElements.style("opacity", d => {
                if (type === 'all') return 1;
                return d.type === type ? 1 : 0.1;
            });
            
            linkElements.style("opacity", d => {
                if (type === 'all') return 0.6;
                const sourceNode = nodes.find(n => n.id === d.source.id);
                const targetNode = nodes.find(n => n.id === d.target.id);
                return (sourceNode.type === type || targetNode.type === type) ? 0.6 : 0.1;
            });
        }
    </script>
</body>
</html>
