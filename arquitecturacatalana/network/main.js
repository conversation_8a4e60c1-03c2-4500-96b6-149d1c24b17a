/**
 * Punt d'entrada principal per la visualització de xarxa
 */
import { NetworkVisualization } from './visualization.js';

// Funcions de loading (globals)
window.updateProgress = function(percentage, message) {
    const progressEl = document.getElementById('loadingProgress');
    const barEl = document.getElementById('progressBar');
    if (progressEl) progressEl.textContent = `${percentage}% - ${message}`;
    if (barEl) barEl.style.width = `${percentage}%`;
};

window.hideLoading = function() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) loadingEl.style.display = 'none';
};

// Inicialitzar la visualització quan es carregui la pàgina
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 Inicialitzant Arquitectura Catalana Network Visualization...');

    try {
        window.updateProgress(10, "Inicialitzant...");

        // Crear la visualització
        const networkViz = new NetworkVisualization();

        window.updateProgress(30, "Carregant dades...");

        // Inicialitzar la visualització
        await networkViz.init();

        // Fer la instància accessible globalment per debugging
        window.networkViz = networkViz;

        window.updateProgress(100, "Completat!");

        // Amagar loading després d'un moment
        setTimeout(window.hideLoading, 1000);

        console.log('✅ Visualització inicialitzada');

    } catch (error) {
        console.error('❌ Error inicialitzant visualització:', error);
        window.updateProgress(0, "Error en la càrrega");
    }
});

// Gestió de redimensionament de finestra
window.addEventListener('resize', () => {
    if (window.networkViz) {
        // Actualitzar dimensions
        window.networkViz.config.width = window.innerWidth;
        window.networkViz.config.height = window.innerHeight;
        
        // Redimensionar SVG
        if (window.networkViz.svg) {
            window.networkViz.svg
                .attr("width", window.networkViz.config.width)
                .attr("height", window.networkViz.config.height);
        }
        
        // Recentrar simulació
        if (window.networkViz.forceSimulation && window.networkViz.forceSimulation.simulation) {
            window.networkViz.forceSimulation.simulation
                .force("center", d3.forceCenter(
                    window.networkViz.config.width / 2, 
                    window.networkViz.config.height / 2
                ));
        }
    }
});
