/**
 * Punt d'entrada principal per la visualització de xarxa
 */
import { NetworkVisualization } from './visualization.js';

// Inicialitzar la visualització quan es carregui la pàgina
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Inicialitzant Arquitectura Catalana Network Visualization...');
    
    // Crear la visualització
    const networkViz = new NetworkVisualization();
    
    // Fer la instància accessible globalment per debugging
    window.networkViz = networkViz;
    
    console.log('✅ Visualització inicialitzada');
});

// Gestió de redimensionament de finestra
window.addEventListener('resize', () => {
    if (window.networkViz) {
        // Actualitzar dimensions
        window.networkViz.config.width = window.innerWidth;
        window.networkViz.config.height = window.innerHeight;
        
        // Redimensionar SVG
        if (window.networkViz.svg) {
            window.networkViz.svg
                .attr("width", window.networkViz.config.width)
                .attr("height", window.networkViz.config.height);
        }
        
        // Recentrar simulació
        if (window.networkViz.forceSimulation && window.networkViz.forceSimulation.simulation) {
            window.networkViz.forceSimulation.simulation
                .force("center", d3.forceCenter(
                    window.networkViz.config.width / 2, 
                    window.networkViz.config.height / 2
                ));
        }
    }
});
