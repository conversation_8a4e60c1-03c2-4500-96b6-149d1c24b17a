/**
 * Processador de dades per la visualització de xarxa
 */
export class DataProcessor {
    constructor() {
        this.nodes = [];
        this.links = [];
        this.linksByNode = new Map();
    }

    async loadData() {
        try {
            // Carregar dades dels actors COAC
            console.log("Loading actors data...");
            const response = await fetch('../actors/actors.json');
            const data = await response.json();
            
            // El fitxer té estructura: { metadata: {...}, actors: [...] }
            const actorsData = data.actors || data;
            console.log(`Loaded ${actorsData.length} actors from JSON`);
            
            this.convertActorsData(actorsData);
            this.processData();
            
            return { nodes: this.nodes, links: this.links };
        } catch (error) {
            console.error("Error loading actors data:", error);
            // Fallback a dades de mostra si falla
            this.generateSampleData();
            this.processData();
            return { nodes: this.nodes, links: this.links };
        }
    }

    convertActorsData(actorsData) {
        const nodes = [];
        const links = [];
        const memberOfNodes = new Map();
        const eventNodes = new Map();
        
        // Processar cada actor
        actorsData.forEach((actor, index) => {
            // Usar slug com a ID únic, o metadata.idInSource com a fallback
            const actorId = actor.slug || actor.metadata?.idInSource || `actor_${index}`;
            
            // Afegir node de persona
            const personNode = {
                id: actorId,
                name: actor.name || actor.label,
                type: 'person',
                inScheme: actor.inScheme ? actor.inScheme.label : 'Unknown',
                portraitMedia: actor.portraitMedia,
                slug: actor.slug,
                x: Math.random() * 800 + 100, // Posició inicial aleatòria
                y: Math.random() * 600 + 100
            };
            nodes.push(personNode);

            // Processar memberOf (empreses/organitzacions)
            if (actor.memberOf && Array.isArray(actor.memberOf)) {
                actor.memberOf.forEach(member => {
                    if (member.label) {
                        // Usar slug del member si existeix, sinó crear ID basat en label
                        const memberId = member.slug ? `memberOf_${member.slug}` : `memberOf_${member.label.replace(/\s+/g, '_').toLowerCase()}`;
                        
                        if (!memberOfNodes.has(memberId)) {
                            memberOfNodes.set(memberId, {
                                id: memberId,
                                name: member.label,
                                type: 'memberOf',
                                href: member.href,
                                slug: member.slug
                            });
                            nodes.push(memberOfNodes.get(memberId));
                        }
                        
                        // Crear enllaç entre persona i memberOf
                        links.push({
                            id: `${actorId}_${memberId}`,
                            source: actorId,
                            target: memberId,
                            weight: 2,
                            type: 'memberOf'
                        });
                    }
                });
            }

            // Processar events (projectes/obres)
            if (actor.events && Array.isArray(actor.events)) {
                actor.events.forEach(event => {
                    if (event.label && event.slug) { // Filtrar events amb slug null
                        const eventId = `event_${event.slug}`;
                        
                        if (!eventNodes.has(eventId)) {
                            eventNodes.set(eventId, {
                                id: eventId,
                                name: event.label,
                                type: 'event',
                                href: event.href,
                                slug: event.slug
                            });
                            nodes.push(eventNodes.get(eventId));
                        }
                        
                        // Crear enllaç entre persona i event
                        links.push({
                            id: `${actorId}_${eventId}`,
                            source: actorId,
                            target: eventId,
                            weight: 1,
                            type: 'event'
                        });
                    }
                });
            }
        });

        this.nodes = nodes;
        this.links = links;
        
        console.log(`Converted data: ${nodes.length} nodes, ${links.length} links`);
    }

    processData() {
        // Crear mapa d'enllaços per node
        this.linksByNode = new Map();
        
        // Processar enllaços
        this.links.forEach(link => {
            const sourceLinks = this.linksByNode.get(link.source) || [];
            const targetLinks = this.linksByNode.get(link.target) || [];
            
            sourceLinks.push(link);
            targetLinks.push(link);
            
            this.linksByNode.set(link.source, sourceLinks);
            this.linksByNode.set(link.target, targetLinks);
        });
        
        // Calcular graus de connexió
        this.nodes.forEach(node => {
            const connections = this.linksByNode.get(node.id) || [];
            node.connectionCount = connections.length;
        });
    }

    generateSampleData() {
        // Dades de mostra per fallback
        this.nodes = [
            { id: "person1", name: "Arquitecte 1", type: "person", inScheme: "Arquitectes i col·laboradors" },
            { id: "person2", name: "Arquitecte 2", type: "person", inScheme: "Societats" },
            { id: "company1", name: "Empresa 1", type: "memberOf" },
            { id: "project1", name: "Projecte 1", type: "event" }
        ];
        
        this.links = [
            { id: "link1", source: "person1", target: "company1", weight: 2, type: "memberOf" },
            { id: "link2", source: "person1", target: "project1", weight: 1, type: "event" },
            { id: "link3", source: "person2", target: "company1", weight: 2, type: "memberOf" }
        ];
        
        console.log("Using sample data");
    }

    getNodesByType(type) {
        return this.nodes.filter(node => node.type === type);
    }

    getLinksForNode(nodeId) {
        return this.linksByNode.get(nodeId) || [];
    }

    searchNodes(query) {
        if (!query) return [];
        
        const lowerQuery = query.toLowerCase();
        return this.nodes.filter(node => 
            node.name.toLowerCase().includes(lowerQuery)
        );
    }
}
