/**
 * Configurac<PERSON><PERSON> de la visualització de xarxa
 */
export class NetworkConfig {
    constructor() {
        // Dimensions
        this.width = window.innerWidth;
        this.height = window.innerHeight;
        this.margin = { top: 20, right: 20, bottom: 20, left: 20 };

        // Tipus de nodes
        this.nodeTypes = ["person", "memberOf", "event"];
        
        // Colors per tipus de node
        this.colors = {
            'person': '#e74c3c',
            'memberOf': '#3498db', 
            'event': '#f39c12'
        };
        
        // Colors per categories inScheme dels arquitectes
        this.inSchemeColors = {
            'Arquitectes i col·laboradors': '#e74c3c',
            'Societats': '#3498db',
            'Fotògrafs': '#f39c12',
            'Constructors': '#9b59b6',
            'Enginyers': '#2ecc71',
            'Entitats': '#34495e',
            'Grups': '#e67e22'
        };

        // Escales per radius dels nodes
        this.radiusScale = d3.scaleOrdinal()
            .domain(this.nodeTypes)
            .range([8, 6, 5]); // person, memberOf, event

        // Escales per càrrega dels nodes
        this.chargeScale = d3.scaleOrdinal()
            .domain(this.nodeTypes)
            .range([-100, -80, -60]); // person, memberOf, event

        // Configuració de forces - AJUSTADA per nodes més propers
        this.forceConfig = {
            linkDistance: d => 30 + d.weight * 10, // Reduït per apropar nodes
            linkStrength: 0.2, // Augmentat per enllaços més forts
            chargeStrength: d => this.chargeScale(d.type) * 0.2, // Reduït per menys repulsió
            collisionRadius: d => this.radiusScale(d.type) + 1, // Reduït padding
            alpha: 0.3, // Mantingut
            alphaDecay: 0.1, // Mantingut
            velocityDecay: 0.9, // Mantingut
            simulationTimeout: 2000 // No s'usa amb simulació manual
        };

        // Configuració visual
        this.visual = {
            linkStrokeColor: "#999",
            linkStrokeWidth: d => Math.sqrt(d.weight) + 0.5,
            nodeStrokeColor: "#fff",
            nodeStrokeWidth: d => d.type === 'person' ? 3 : 2,
            curvature: 0.3, // Restaurat a valor original per enllaços més corbats
            opacity: {
                default: 1,
                highlighted: 1,
                dimmed: 0.2
            }
        };

        // Configuració d'animacions
        this.animations = {
            transitionDuration: 300,
            highlightDuration: 200,
            tooltipDuration: 200
        };
    }

    // Mètodes per obtenir colors
    getNodeColor(node) {
        if (node.type === 'person' && node.inScheme) {
            return this.inSchemeColors[node.inScheme] || this.colors[node.type];
        }
        return this.colors[node.type] || '#999';
    }

    getNodeStrokeColor(node) {
        if (node.type === 'person' && node.inScheme) {
            return this.inSchemeColors[node.inScheme] || "#fff";
        }
        return "#fff";
    }
}
