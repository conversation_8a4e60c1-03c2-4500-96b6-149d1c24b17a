/**
 * Classe principal de visualització
 */
import { NetworkConfig } from './config.js';
import { DataProcessor } from './dataProcessor.js';
import { ForceSimulation } from './forceSimulation.js';
import { NetworkInteractions } from './interactions.js';

export class NetworkVisualization {
    constructor() {
        this.config = new NetworkConfig();
        this.dataProcessor = new DataProcessor();
        this.forceSimulation = new ForceSimulation(this.config);
        this.interactions = null; // S'inicialitza després de crear la visualització
        
        this.svg = null;
        this.g = null;
        this.linkElements = null;
        this.nodeElements = null;
        
        this.init();
    }

    async init() {
        await this.loadData();
        this.createSVG();
        this.createVisualization();
        this.setupInteractions();
        this.createLegend();
        this.updateInfo();
    }

    async loadData() {
        const data = await this.dataProcessor.loadData();
        console.log(`Data loaded: ${data.nodes.length} nodes, ${data.links.length} links`);
    }

    createSVG() {
        // Crear SVG principal
        this.svg = d3.select("#network")
            .append("svg")
            .attr("width", this.config.width)
            .attr("height", this.config.height)
            .style("background-color", "white");

        // Afegir zoom
        const zoom = d3.zoom()
            .scaleExtent([0.1, 10])
            .on("zoom", (event) => {
                this.g.attr("transform", event.transform);
            });

        this.svg.call(zoom);

        // Grup principal per elements
        this.g = this.svg.append("g");
    }

    createVisualization() {
        // Crear simulació de forces
        this.forceSimulation.create(this.dataProcessor.nodes, this.dataProcessor.links);

        // Crear enllaços amb corbes
        this.linkElements = this.g.append("g")
            .attr("class", "links")
            .selectAll("path")
            .data(this.dataProcessor.links)
            .enter().append("path")
            .attr("class", "link")
            .attr("stroke", this.config.visual.linkStrokeColor)
            .attr("stroke-width", this.config.visual.linkStrokeWidth)
            .attr("fill", "none")
            .style("opacity", 0);

        // Crear nodes
        this.nodeElements = this.g.append("g")
            .attr("class", "nodes")
            .selectAll("circle")
            .data(this.dataProcessor.nodes)
            .enter().append("circle")
            .attr("class", "node")
            .attr("r", d => this.config.radiusScale(d.type))
            .attr("fill", d => this.config.getNodeColor(d))
            .attr("stroke", d => this.config.getNodeStrokeColor(d))
            .attr("stroke-width", this.config.visual.nodeStrokeWidth)
            .style("cursor", "pointer");

        // Configurar tick de la simulació
        this.forceSimulation.onTick(() => this.ticked());

        // Mostrar enllaços amb animació
        this.linkElements.transition()
            .duration(this.config.animations.transitionDuration)
            .style("opacity", this.config.visual.opacity.default);
    }

    setupInteractions() {
        // Inicialitzar gestor d'interaccions
        this.interactions = new NetworkInteractions(this.config, this);

        // Afegir event listeners als nodes
        this.nodeElements
            .on("mouseover", (event, d) => this.interactions.handleMouseOver(event, d))
            .on("mouseout", (event, d) => this.interactions.handleMouseOut(event, d))
            .on("click", (event, d) => this.interactions.handleNodeClick(event, d));

        // Configurar cerca
        this.setupSearch();

        // Click al fons per netejar selecció
        this.svg.on("click", () => {
            this.interactions.activeNodes.clear();
            this.interactions.resetHighlight();
            this.interactions.updateInfo();
        });
    }

    setupSearch() {
        const searchInput = d3.select("#searchInput");
        const searchResults = d3.select("#searchResults");

        searchInput.on("input", () => {
            const query = searchInput.property("value");
            const results = this.interactions.search(query);

            // Mostrar resultats
            if (query.trim() && results.length > 0) {
                const resultsList = results.slice(0, 10).map(node => 
                    `<div class="search-result" data-id="${node.id}">
                        <strong>${node.name}</strong> (${node.type})
                        ${node.inScheme ? `<br><small>${node.inScheme}</small>` : ''}
                    </div>`
                ).join('');
                
                searchResults.html(resultsList).style("display", "block");

                // Click en resultats
                searchResults.selectAll(".search-result")
                    .on("click", (event) => {
                        const nodeId = event.target.closest('.search-result').dataset.id;
                        const node = this.dataProcessor.nodes.find(n => n.id === nodeId);
                        if (node) {
                            this.centerOnNode(node);
                            this.interactions.handleNodeClick(event, node);
                        }
                        searchResults.style("display", "none");
                    });
            } else {
                searchResults.style("display", "none");
            }
        });

        // Amagar resultats quan es clica fora
        d3.select("body").on("click", (event) => {
            if (!event.target.closest("#searchContainer")) {
                searchResults.style("display", "none");
            }
        });
    }

    centerOnNode(node) {
        const transform = d3.zoomIdentity
            .translate(this.config.width / 2 - node.x, this.config.height / 2 - node.y)
            .scale(1.5);

        this.svg.transition()
            .duration(this.config.animations.transitionDuration)
            .call(d3.zoom().transform, transform);
    }

    ticked() {
        // Actualitzar posicions dels enllaços amb corbes
        this.linkElements.attr("d", d => {
            const dx = d.target.x - d.source.x;
            const dy = d.target.y - d.source.y;
            const dr = Math.sqrt(dx * dx + dy * dy) * this.config.visual.curvature;

            return `M${d.source.x},${d.source.y}A${dr},${dr} 0 0,1 ${d.target.x},${d.target.y}`;
        });

        // Actualitzar posicions dels nodes
        this.nodeElements
            .attr("cx", d => d.x)
            .attr("cy", d => d.y);
    }

    createLegend() {
        const legendContainer = d3.select("#typeLegend");
        
        // Llegenda de tipus de nodes
        legendContainer.append("div")
            .style("margin-bottom", "10px")
            .html("<strong>Node Types:</strong>");
            
        legendContainer.selectAll(".legend-item-type")
            .data(this.config.nodeTypes)
            .enter().append("div")
            .attr("class", "legend-item legend-item-type")
            .html(d => `
                <div class="legend-color" style="background-color: ${this.config.colors[d]}; border-color: ${this.config.colors[d]}"></div>
                <span>${d}</span>
            `);
            
        // Llegenda de colors inScheme per persones
        legendContainer.append("div")
            .style("margin-top", "15px")
            .style("margin-bottom", "10px")
            .html("<strong>Person Categories:</strong>");
            
        const inSchemeEntries = Object.entries(this.config.inSchemeColors);
        legendContainer.selectAll(".legend-item-scheme")
            .data(inSchemeEntries)
            .enter().append("div")
            .attr("class", "legend-item legend-item-scheme")
            .html(d => `
                <div class="legend-color" style="background-color: ${d[1]}; border-color: ${d[1]}"></div>
                <span style="font-size: 11px;">${d[0]}</span>
            `);
    }

    updateInfo() {
        if (this.interactions) {
            this.interactions.updateInfo();
        }
    }
}
