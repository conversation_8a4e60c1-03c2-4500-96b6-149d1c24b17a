/**
 * Classe principal de visualització
 */
import { NetworkConfig } from './config.js';
import { DataProcessor } from './dataProcessor.js';
import { ForceSimulation } from './forceSimulation.js';
import { NetworkInteractions } from './interactions.js';

export class NetworkVisualization {
    constructor() {
        this.config = new NetworkConfig();
        this.dataProcessor = new DataProcessor();
        this.forceSimulation = new ForceSimulation(this.config);
        this.interactions = null; // S'inicialitza després de crear la visualització

        // Sistema Canvas multicapa (estil UNESCO)
        this.canvas_edges = null;
        this.canvas_nodes = null;
        this.canvas_hover = null;
        this.canvas_hidden = null;
        this.ctx_edges = null;
        this.ctx_nodes = null;
        this.ctx_hover = null;
        this.ctx_hidden = null;

        // SVG mínim per events
        this.svg = null;
        this.g = null;

        // Canvas scale factor
        this.sf = 2;
        this.total_width = 0;
        this.total_height = 0;

        // No inicialitzar automàticament - es farà des de main.js
    }

    async init() {
        await this.loadData();
        this.setupCanvas();
        this.createVisualization();
        this.setupInteractions();
        this.createLegend();
        this.updateInfo();
    }

    async loadData() {
        const data = await this.dataProcessor.loadData();
        console.log(`Data loaded: ${data.nodes.length} nodes, ${data.links.length} links`);
    }

    setupCanvas() {
        const container = d3.select("#network");

        // Dimensions
        this.total_width = this.config.width;
        this.total_height = this.config.height;

        // Actualitzar config amb dimensions reals per la simulació
        this.config.width = this.total_width;
        this.config.height = this.total_height;

        // Crear els 4 canvas (estil UNESCO)
        this.createCanvasLayers(container);

        // SVG mínim per zoom i events
        this.svg = container.append("svg")
            .attr("width", this.total_width)
            .attr("height", this.total_height)
            .style("position", "absolute")
            .style("top", 0)
            .style("left", 0)
            .style("pointer-events", "all")
            .style("background-color", "white");

        // Rectangle invisible per capturar events
        const mouse_rect = this.svg.append("rect")
            .attr("width", this.total_width)
            .attr("height", this.total_height)
            .style("fill", "none")
            .style("pointer-events", "all");

        // Zoom
        const zoom = d3.zoom()
            .scaleExtent([0.1, 10])
            .on("zoom", (event) => this.handleZoom(event));

        this.svg.call(zoom);

        // Configurar events de mouse
        mouse_rect
            .on("mousemove", (event) => this.handleMouseMove(event))
            .on("mouseout", () => this.handleMouseOut())
            .on("click", (event) => this.handleMouseClick(event));
    }

    createCanvasLayers(container) {
        // Canvas per enllaços (capa de fons)
        this.canvas_edges = container.append("canvas")
            .attr("id", "canvas-edges")
            .style("position", "absolute")
            .style("top", 0)
            .style("left", 0)
            .style("z-index", 1)
            .style("pointer-events", "none");
        this.ctx_edges = this.canvas_edges.node().getContext("2d");
        this.crispyCanvas(this.canvas_edges, this.ctx_edges);

        // Canvas per nodes (capa principal)
        this.canvas_nodes = container.append("canvas")
            .attr("id", "canvas-nodes")
            .style("position", "absolute")
            .style("top", 0)
            .style("left", 0)
            .style("z-index", 2)
            .style("pointer-events", "none");
        this.ctx_nodes = this.canvas_nodes.node().getContext("2d");
        this.crispyCanvas(this.canvas_nodes, this.ctx_nodes);

        // Canvas per hover (capa d'efectes)
        this.canvas_hover = container.append("canvas")
            .attr("id", "canvas-hover")
            .style("position", "absolute")
            .style("top", 0)
            .style("left", 0)
            .style("z-index", 3)
            .style("pointer-events", "none");
        this.ctx_hover = this.canvas_hover.node().getContext("2d");
        this.crispyCanvas(this.canvas_hover, this.ctx_hover);

        // Canvas ocult per detecció (invisible)
        this.canvas_hidden = container.append("canvas")
            .attr("id", "canvas-hidden")
            .style("position", "absolute")
            .style("top", 0)
            .style("left", 0)
            .style("display", "none");
        this.ctx_hidden = this.canvas_hidden.node().getContext("2d", { willReadFrequently: true });
        this.crispyCanvas(this.canvas_hidden, this.ctx_hidden, 1); // SF=1 per detecció
    }

    // Configurar canvas per alta resolució (estil UNESCO)
    crispyCanvas(canvas, ctx, sf = this.sf) {
        canvas
            .attr("width", sf * this.total_width)
            .attr("height", sf * this.total_height)
            .style("width", `${this.total_width}px`)
            .style("height", `${this.total_height}px`);

        ctx.scale(sf, sf);
        // No translate - els nodes ja estan centrats per la simulació
    }

    createVisualization() {
        // Crear simulació de forces (estil UNESCO - manual)
        this.forceSimulation.create(this.dataProcessor.nodes, this.dataProcessor.links);

        // Dibuixar tot després de la simulació manual
        setTimeout(() => {
            this.drawAll();
        }, 100);
    }

    // Dibuixar tots els elements (estil UNESCO)
    drawAll() {
        // SOLUCIÓ: Reposicionar tots els nodes al centre del canvas
        const centerX = this.config.width / 2;
        const centerY = this.config.height / 2;

        // Calcular bounding box dels nodes
        let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
        this.dataProcessor.nodes.forEach(node => {
            minX = Math.min(minX, node.x);
            maxX = Math.max(maxX, node.x);
            minY = Math.min(minY, node.y);
            maxY = Math.max(maxY, node.y);
        });

        // Calcular offset per centrar
        const currentCenterX = (minX + maxX) / 2;
        const currentCenterY = (minY + maxY) / 2;
        const offsetX = centerX - currentCenterX;
        const offsetY = centerY - currentCenterY;

        // Aplicar offset a tots els nodes
        this.dataProcessor.nodes.forEach(node => {
            node.x += offsetX;
            node.y += offsetY;
        });

        console.log("✅ Nodes reposicionats al centre del canvas");

        this.clearAllCanvas();
        this.drawEdges();
        this.drawNodes();
        this.drawHiddenNodes(); // Per detecció

        // Actualitzar progrés final
        if (window.updateProgress) window.updateProgress(100, "Visualització completa!");
        if (window.hideLoading) window.hideLoading();
    }

    // Netejar tots els canvas
    clearAllCanvas() {
        [this.ctx_edges, this.ctx_nodes, this.ctx_hover, this.ctx_hidden].forEach(ctx => {
            ctx.clearRect(-this.total_width/2, -this.total_height/2, this.total_width, this.total_height);
        });
    }

    // Dibuixar enllaços
    drawEdges() {
        this.ctx_edges.strokeStyle = this.config.visual.linkStrokeColor;
        this.ctx_edges.lineWidth = 1;
        this.ctx_edges.globalAlpha = 0; // Ocults per defecte

        this.dataProcessor.links.forEach(link => {
            this.drawSingleEdge(this.ctx_edges, link);
        });

        this.ctx_edges.globalAlpha = 1;
    }

    // Dibuixar un enllaç individual
    drawSingleEdge(ctx, link) {
        const curvature = this.config.visual.curvature;
        const dx = link.target.x - link.source.x;
        const dy = link.target.y - link.source.y;
        const dr = Math.sqrt(dx * dx + dy * dy) * curvature;

        ctx.beginPath();
        ctx.moveTo(link.source.x, link.source.y);
        ctx.quadraticCurveTo(
            (link.source.x + link.target.x) / 2 + dr,
            (link.source.y + link.target.y) / 2 + dr,
            link.target.x,
            link.target.y
        );
        ctx.stroke();
    }

    // Dibuixar nodes
    drawNodes() {
        this.dataProcessor.nodes.forEach(node => {
            this.drawSingleNode(this.ctx_nodes, node);
        });
    }

    // Dibuixar un node individual
    drawSingleNode(ctx, node) {
        const radius = this.config.radiusScale(node.type);

        // Cercle
        ctx.fillStyle = this.config.getNodeColor(node);
        ctx.strokeStyle = this.config.getNodeStrokeColor(node);
        ctx.lineWidth = this.config.visual.nodeStrokeWidth;

        ctx.beginPath();
        ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
    }

    // Dibuixar nodes ocults per detecció (estil UNESCO)
    drawHiddenNodes() {
        this.dataProcessor.nodes.forEach((node, index) => {
            this.drawHiddenNode(this.ctx_hidden, node, index);
        });
    }

    // Dibuixar node ocult amb color únic
    drawHiddenNode(ctx, node, index) {
        const radius = this.config.radiusScale(node.type);
        const color = this.generateUniqueColor(index);

        // Guardar mapatge color -> node
        if (!this.colorToNode) this.colorToNode = new Map();
        this.colorToNode.set(color, node);

        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);
        ctx.fill();
    }

    // Generar color únic per cada node (estil UNESCO)
    generateUniqueColor(index) {
        const r = (index & 0xFF);
        const g = ((index >> 8) & 0xFF);
        const b = ((index >> 16) & 0xFF);
        return `rgb(${r},${g},${b})`;
    }

    // Gestió d'events Canvas
    handleZoom(event) {
        // Aplicar transform a tots els canvas
        const transform = event.transform;
        [this.ctx_edges, this.ctx_nodes, this.ctx_hover].forEach(ctx => {
            ctx.save();
            ctx.setTransform(this.sf, 0, 0, this.sf, 0, 0);
            ctx.scale(transform.k, transform.k);
            ctx.translate(transform.x, transform.y);
        });

        // Redibuixar
        this.drawAll();

        [this.ctx_edges, this.ctx_nodes, this.ctx_hover].forEach(ctx => {
            ctx.restore();
        });
    }

    handleMouseMove(event) {
        const [x, y] = d3.pointer(event);

        // Usar canvas ocult per detecció eficient (estil UNESCO)
        const hoveredNode = this.findNodeAtPixel(x, y);

        if (hoveredNode) {
            this.interactions.handleMouseOver(event, hoveredNode);
        }
    }

    handleMouseOut() {
        this.interactions.handleMouseOut();
    }

    handleMouseClick(event) {
        const [x, y] = d3.pointer(event);
        const clickedNode = this.findNodeAtPixel(x, y);

        if (clickedNode) {
            this.interactions.handleNodeClick(event, clickedNode);
        }
    }

    // Trobar node usant canvas ocult (estil UNESCO)
    findNodeAtPixel(x, y) {
        try {
            const imageData = this.ctx_hidden.getImageData(x, y, 1, 1);
            const [r, g, b] = imageData.data;
            const colorString = `rgb(${r},${g},${b})`;

            return this.colorToNode ? this.colorToNode.get(colorString) : null;
        } catch (e) {
            return null;
        }
    }

    setupInteractions() {
        // Inicialitzar gestor d'interaccions
        this.interactions = new NetworkInteractions(this.config, this);

        // Configurar cerca
        this.setupSearch();

        // Click al fons per netejar selecció (Canvas)
        this.svg.on("click", () => {
            this.interactions.activeNodes.clear();
            this.interactions.resetHighlight();
            this.interactions.updateInfo();
        });
    }

    setupSearch() {
        const searchInput = d3.select("#searchInput");
        const searchResults = d3.select("#searchResults");

        searchInput.on("input", () => {
            const query = searchInput.property("value");
            const results = this.interactions.search(query);

            // Mostrar resultats
            if (query.trim() && results.length > 0) {
                const resultsList = results.slice(0, 10).map(node => 
                    `<div class="search-result" data-id="${node.id}">
                        <strong>${node.name}</strong> (${node.type})
                        ${node.inScheme ? `<br><small>${node.inScheme}</small>` : ''}
                    </div>`
                ).join('');
                
                searchResults.html(resultsList).style("display", "block");

                // Click en resultats
                searchResults.selectAll(".search-result")
                    .on("click", (event) => {
                        const nodeId = event.target.closest('.search-result').dataset.id;
                        const node = this.dataProcessor.nodes.find(n => n.id === nodeId);
                        if (node) {
                            this.centerOnNode(node);
                            this.interactions.handleNodeClick(event, node);
                        }
                        searchResults.style("display", "none");
                    });
            } else {
                searchResults.style("display", "none");
            }
        });

        // Amagar resultats quan es clica fora
        d3.select("body").on("click", (event) => {
            if (!event.target.closest("#searchContainer")) {
                searchResults.style("display", "none");
            }
        });
    }

    centerOnNode(node) {
        const transform = d3.zoomIdentity
            .translate(this.config.width / 2 - node.x, this.config.height / 2 - node.y)
            .scale(1.5);

        this.svg.transition()
            .duration(this.config.animations.transitionDuration)
            .call(d3.zoom().transform, transform);
    }

    // Funció ticked eliminada - usem simulació manual estil UNESCO

    updateNodePositions() {
        // Actualitzar posicions dels enllaços amb corbes
        this.linkElements.attr("d", d => {
            const dx = d.target.x - d.source.x;
            const dy = d.target.y - d.source.y;
            const dr = Math.sqrt(dx * dx + dy * dy) * this.config.visual.curvature;

            return `M${d.source.x},${d.source.y}A${dr},${dr} 0 0,1 ${d.target.x},${d.target.y}`;
        });

        // Actualitzar posicions dels nodes
        this.nodeElements
            .attr("cx", d => d.x)
            .attr("cy", d => d.y);
    }

    createLegend() {
        const legendContainer = d3.select("#typeLegend");
        
        // Llegenda de tipus de nodes
        legendContainer.append("div")
            .style("margin-bottom", "10px")
            .html("<strong>Node Types:</strong>");
            
        legendContainer.selectAll(".legend-item-type")
            .data(this.config.nodeTypes)
            .enter().append("div")
            .attr("class", "legend-item legend-item-type")
            .html(d => `
                <div class="legend-color" style="background-color: ${this.config.colors[d]}; border-color: ${this.config.colors[d]}"></div>
                <span>${d}</span>
            `);
            
        // Llegenda de colors inScheme per persones
        legendContainer.append("div")
            .style("margin-top", "15px")
            .style("margin-bottom", "10px")
            .html("<strong>Person Categories:</strong>");
            
        const inSchemeEntries = Object.entries(this.config.inSchemeColors);
        legendContainer.selectAll(".legend-item-scheme")
            .data(inSchemeEntries)
            .enter().append("div")
            .attr("class", "legend-item legend-item-scheme")
            .html(d => `
                <div class="legend-color" style="background-color: ${d[1]}; border-color: ${d[1]}"></div>
                <span style="font-size: 11px;">${d[0]}</span>
            `);
    }

    updateInfo() {
        if (this.interactions) {
            this.interactions.updateInfo();
        }
    }
}
