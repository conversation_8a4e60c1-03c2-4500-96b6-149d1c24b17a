/**
 * Gest<PERSON>ó de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        console.log("🚀 Creant simulació manual estil UNESCO...");

        // Crear simulació però ATURAR-LA IMMEDIATAMENT (estil UNESCO)
        this.simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links)
                .id(d => d.id)
                .distance(this.config.forceConfig.linkDistance)
                .strength(this.config.forceConfig.linkStrength))
            .force("charge", d3.forceManyBody()
                .strength(this.config.forceConfig.chargeStrength))
            .force("center", d3.forceCenter(0, 0))
            .force("collision", d3.forceCollide()
                .radius(this.config.forceConfig.collisionRadius))
            .stop(); // ATURAR IMMEDIATAMENT com UNESCO

        // Executar ticks MANUALS (estil UNESCO)
        this.runManualSimulation();

        return this.simulation;
    }

    runManualSimulation() {
        console.log("⚙️ Executant simulació manual...");

        // Ticks manuals com UNESCO (300 iteracions)
        const n_ticks = 300; // Restaurat per millor posicionament
        for (let i = 0; i < n_ticks; i++) {
            this.simulation.tick();
        }

        // FIXAR totes les posicions (estil UNESCO)
        const simulationNodes = this.simulation.nodes();

        simulationNodes.forEach(d => {
            d.fx = d.x;
            d.fy = d.y;
        });

        // DESTRUIR completament la simulació
        this.destroySimulation();

        console.log("✅ Simulació manual completada - nodes fixats");
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    destroySimulation() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null);
            // Eliminar totes les forces
            this.simulation.force("link", null);
            this.simulation.force("charge", null);
            this.simulation.force("center", null);
            this.simulation.force("collision", null);
            this.simulation = null; // Eliminar referència
            console.log("💥 Simulació DESTRUÏDA - només navegació manual");
        }
    }

    restart() {
        // NO permetre restart - un cop destruït, destruït
        console.log("⚠️ Restart desactivat - simulació destruïda");
    }

    // Funcions de drag - ESTIL UNESCO (sense simulació)
    dragstarted(event, d) {
        // Només fixar posició inicial
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        // Actualització directa de posició (estil UNESCO)
        d.fx = event.x;
        d.fy = event.y;
        d.x = event.x;
        d.y = event.y;
    }

    dragended(event, d) {
        // Mantenir posició fixa (estil UNESCO)
        // Les posicions fx/fy es mantenen sempre
    }
}
