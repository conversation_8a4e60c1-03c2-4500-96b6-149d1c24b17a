/**
 * G<PERSON><PERSON><PERSON> de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        console.log("🚀 Creant distribució geomètrica equidistant...");

        // DISTRIBUCIÓ GEOMÈTRICA en lloc de simulació física
        this.createGeometricLayout(nodes, links);

        return null; // No simulation needed
    }

    createGeometricLayout(nodes, links) {
        // DISTRIBUCIÓ ORIGAMI - fluida, corba i equidistant
        this.createOrigamiLayout(nodes);

        console.log("✅ Distribució origami completada");
    }

    createZonedOrganicLayout(nodes) {
        const width = this.config.width;
        const height = this.config.height;

        // Agrupar per tipus i subcategories
        const groups = this.groupNodesByTypeAndCategory(nodes);

        // Definir zones per cada tipus
        const zones = {
            person: { x: 0, y: 0, width: width * 0.4, height: height },
            memberOf: { x: width * 0.4, y: 0, width: width * 0.3, height: height },
            event: { x: width * 0.7, y: 0, width: width * 0.3, height: height }
        };

        // Distribuir cada grup en la seva zona
        Object.keys(groups).forEach(type => {
            const zone = zones[type];
            const typeGroups = groups[type];

            this.distributeGroupsInZone(typeGroups, zone);
        });
    }

    createOrigamiLayout(nodes) {
        const width = this.config.width;
        const height = this.config.height;

        // Crear grid d'ones geomètriques
        const waveGrid = this.createWaveGrid(width, height);

        // Barrejar nodes per distribució orgànica
        const shuffledNodes = this.shuffleArray([...nodes]);

        const minDistance = 40; // Distància mínima entre nodes
        const placedNodes = [];

        // Col·locar nodes seguint el grid d'ones
        shuffledNodes.forEach((node, i) => {
            let bestPosition = null;
            let maxDistance = 0;

            // Provar múltiples posicions del grid
            for (let attempt = 0; attempt < 50; attempt++) {
                const gridIndex = (i * 7 + attempt) % waveGrid.length; // Distribució més orgànica
                const gridPos = waveGrid[gridIndex];

                // Afegir variació orgànica petita
                const x = gridPos.x + (Math.random() - 0.5) * 12;
                const y = gridPos.y + (Math.random() - 0.5) * 12;

                // Assegurar límits
                const clampedX = Math.max(25, Math.min(width - 25, x));
                const clampedY = Math.max(25, Math.min(height - 25, y));

                // Calcular distància mínima amb nodes ja col·locats
                let minDistToOthers = Infinity;
                for (const placedNode of placedNodes) {
                    const dx = clampedX - placedNode.x;
                    const dy = clampedY - placedNode.y;
                    const dist = Math.sqrt(dx * dx + dy * dy);
                    minDistToOthers = Math.min(minDistToOthers, dist);
                }

                // Si està prou lluny i és millor que l'anterior
                if (minDistToOthers >= minDistance && minDistToOthers > maxDistance) {
                    bestPosition = { x: clampedX, y: clampedY };
                    maxDistance = minDistToOthers;
                }
            }

            // Si hem trobat una bona posició
            if (bestPosition) {
                node.x = bestPosition.x;
                node.y = bestPosition.y;
            } else {
                // Fallback: posició aleatòria segura
                let safeX, safeY, safeAttempts = 0;
                do {
                    safeX = 50 + Math.random() * (width - 100);
                    safeY = 50 + Math.random() * (height - 100);
                    safeAttempts++;
                } while (safeAttempts < 30 && placedNodes.some(p => {
                    const dx = safeX - p.x;
                    const dy = safeY - p.y;
                    return Math.sqrt(dx * dx + dy * dy) < minDistance;
                }));

                node.x = safeX;
                node.y = safeY;
            }

            // Fixar posició
            node.fx = node.x;
            node.fy = node.y;
            placedNodes.push(node);
        });
    }

    createWaveGrid(width, height) {
        const grid = [];
        const spacing = 30; // Espai entre punts del grid

        // Crear grid base
        for (let x = spacing; x < width - spacing; x += spacing) {
            for (let y = spacing; y < height - spacing; y += spacing) {
                // Aplicar ones geomètriques
                const wave1 = Math.sin(x * 0.01) * 40; // Ona horitzontal
                const wave2 = Math.cos(y * 0.008) * 30; // Ona vertical
                const wave3 = Math.sin((x + y) * 0.006) * 20; // Ona diagonal

                const finalX = x + wave1 + wave3;
                const finalY = y + wave2 + wave3;

                // Només afegir si està dins dels límits
                if (finalX >= 25 && finalX <= width - 25 &&
                    finalY >= 25 && finalY <= height - 25) {
                    grid.push({ x: finalX, y: finalY });
                }
            }
        }

        return grid;
    }

    generateWavePatterns(width, height, nodeCount) {
        // Crear múltiples ones sinusoidals que s'entrecreuen
        return [
            { // Ona horitzontal principal
                amplitude: height * 0.15,
                frequency: 0.008,
                phase: 0,
                direction: 'horizontal',
                centerY: height * 0.3
            },
            { // Ona horitzontal secundària
                amplitude: height * 0.12,
                frequency: 0.012,
                phase: Math.PI,
                direction: 'horizontal',
                centerY: height * 0.7
            },
            { // Ona vertical
                amplitude: width * 0.1,
                frequency: 0.01,
                phase: Math.PI/2,
                direction: 'vertical',
                centerX: width * 0.5
            }
        ];
    }

    getWavePosition(index, waves, width, height) {
        const progress = index / 11237; // Total nodes aproximat

        let x = progress * width;
        let y = height / 2;

        // Aplicar cada ona
        waves.forEach(wave => {
            if (wave.direction === 'horizontal') {
                y += wave.amplitude * Math.sin(wave.frequency * x + wave.phase);
                y = Math.max(0, Math.min(height, wave.centerY + (y - height/2) * 0.5));
            } else if (wave.direction === 'vertical') {
                const verticalProgress = (index % 100) / 100;
                y = verticalProgress * height;
                x += wave.amplitude * Math.sin(wave.frequency * y + wave.phase);
                x = Math.max(0, Math.min(width, wave.centerX + (x - width/2) * 0.3));
            }
        });

        return { x, y };
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    arrangeRemainingInSpiral(remainingNodes, centerX, centerY) {
        const spiralSpacing = 30;
        let angle = 0;
        let radius = 100;

        remainingNodes.forEach((node, i) => {
            node.x = centerX + radius * Math.cos(angle);
            node.y = centerY + radius * Math.sin(angle);
            node.fx = node.x;
            node.fy = node.y;

            // Incrementar angle i radius per espiral
            angle += 0.5;
            radius += 0.8;
        });
    }

    runManualSimulation() {
        console.log("⚙️ Executant simulació manual...");

        if (window.updateProgress) window.updateProgress(80, "Calculant posicions...");

        // Ticks manuals optimitzats per càrrega ràpida
        const n_ticks = 100; // Reduït per càrrega més ràpida
        for (let i = 0; i < n_ticks; i++) {
            this.simulation.tick();

            // Actualitzar progrés cada 20 ticks
            if (i % 20 === 0 && window.updateProgress) {
                const progress = 80 + Math.floor((i / n_ticks) * 15);
                window.updateProgress(progress, `Simulant... ${Math.floor((i / n_ticks) * 100)}%`);
            }
        }

        // FIXAR totes les posicions (estil UNESCO)
        const simulationNodes = this.simulation.nodes();

        simulationNodes.forEach(d => {
            d.fx = d.x;
            d.fy = d.y;
        });

        // DESTRUIR completament la simulació
        this.destroySimulation();

        if (window.updateProgress) window.updateProgress(95, "Finalitzant simulació...");

        console.log("✅ Simulació manual completada - nodes fixats");
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    destroySimulation() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null);
            // Eliminar totes les forces
            this.simulation.force("link", null);
            this.simulation.force("charge", null);
            this.simulation.force("center", null);
            this.simulation.force("collision", null);
            this.simulation = null; // Eliminar referència
            console.log("💥 Simulació DESTRUÏDA - només navegació manual");
        }
    }

    restart() {
        // NO permetre restart - un cop destruït, destruït
        console.log("⚠️ Restart desactivat - simulació destruïda");
    }

    // Funcions de drag - ESTIL UNESCO (sense simulació)
    dragstarted(event, d) {
        // Només fixar posició inicial
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        // Actualització directa de posició (estil UNESCO)
        d.fx = event.x;
        d.fy = event.y;
        d.x = event.x;
        d.y = event.y;
    }

    dragended(event, d) {
        // Mantenir posició fixa (estil UNESCO)
        // Les posicions fx/fy es mantenen sempre
    }
}
