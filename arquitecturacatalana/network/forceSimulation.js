/**
 * Gest<PERSON>ó de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        // Crear simulació de forces - TOTALMENT ESTÀTICA
        this.simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links)
                .id(d => d.id)
                .distance(this.config.forceConfig.linkDistance)
                .strength(this.config.forceConfig.linkStrength))
            .force("charge", d3.forceManyBody()
                .strength(this.config.forceConfig.chargeStrength))
            .force("center", d3.forceCenter(this.config.width / 2, this.config.height / 2))
            .force("collision", d3.forceCollide()
                .radius(this.config.forceConfig.collisionRadius))
            .alpha(this.config.forceConfig.alpha)
            .alphaDecay(this.config.forceConfig.alphaDecay)
            .velocityDecay(this.config.forceConfig.velocityDecay);

        // Aturar completament després del timeout configurat
        setTimeout(() => {
            this.stop();
        }, this.config.forceConfig.simulationTimeout);

        // Aturar també quan l'alpha sigui molt baix
        this.simulation.on("tick", () => {
            if (this.simulation.alpha() < 0.01) {
                this.stop();
            }
        });

        return this.simulation;
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    restart() {
        if (this.simulation) {
            this.simulation.restart();
        }
    }

    // Funcions de drag - OPTIMITZADES per rendiment
    dragstarted(event, d) {
        // Només reactivar si la simulació està aturada
        if (!event.active && this.simulation.alpha() < 0.01) {
            this.simulation.alphaTarget(0.1).restart();
        }
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }

    dragended(event, d) {
        if (!event.active) this.simulation.alphaTarget(0);
        // Alliberar posició fixa després d'un petit delay
        setTimeout(() => {
            d.fx = null;
            d.fy = null;
        }, 100);
    }
}
