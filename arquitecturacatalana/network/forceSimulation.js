/**
 * G<PERSON><PERSON><PERSON> de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        console.log("🚀 Creant distribució geomètrica equidistant...");

        // DISTRIBUCIÓ GEOMÈTRICA en lloc de simulació física
        this.createGeometricLayout(nodes, links);

        return null; // No simulation needed
    }

    createGeometricLayout(nodes, links) {
        // DISTRIBUCIÓ PER ZONES amb subcategories orgàniques
        this.createZonedOrganicLayout(nodes);

        console.log("✅ Distribució per zones orgànica completada");
    }

    createZonedOrganicLayout(nodes) {
        const width = this.config.width;
        const height = this.config.height;

        // Agrupar per tipus i subcategories
        const groups = this.groupNodesByTypeAndCategory(nodes);

        // Definir zones per cada tipus
        const zones = {
            person: { x: 0, y: 0, width: width * 0.4, height: height },
            memberOf: { x: width * 0.4, y: 0, width: width * 0.3, height: height },
            event: { x: width * 0.7, y: 0, width: width * 0.3, height: height }
        };

        // Distribuir cada grup en la seva zona
        Object.keys(groups).forEach(type => {
            const zone = zones[type];
            const typeGroups = groups[type];

            this.distributeGroupsInZone(typeGroups, zone);
        });
    }

    groupNodesByTypeAndCategory(nodes) {
        const groups = { person: {}, memberOf: {}, event: {} };

        nodes.forEach(node => {
            const type = node.type;
            let category = 'default';

            // Extreure categoria per persones (inScheme.label)
            if (type === 'person' && node.inScheme && node.inScheme.label) {
                category = node.inScheme.label;
            }
            // Per memberOf i events usar el label directament
            else if (type !== 'person') {
                category = node.label ? node.label.substring(0, 10) : 'default';
            }

            if (!groups[type][category]) {
                groups[type][category] = [];
            }
            groups[type][category].push(node);
        });

        return groups;
    }

    distributeGroupsInZone(typeGroups, zone) {
        const categories = Object.keys(typeGroups);
        const categoryHeight = zone.height / categories.length;

        categories.forEach((category, categoryIndex) => {
            const categoryNodes = typeGroups[category];
            const categoryZone = {
                x: zone.x,
                y: zone.y + categoryIndex * categoryHeight,
                width: zone.width,
                height: categoryHeight
            };

            this.arrangeNodesInZoneOrganically(categoryNodes, categoryZone);
        });
    }

    arrangeNodesInZoneOrganically(nodes, zone) {
        const padding = 20;
        const nodeSpacing = 35; // Més espai per evitar solapament

        const availableWidth = zone.width - 2 * padding;
        const availableHeight = zone.height - 2 * padding;

        const cols = Math.max(1, Math.floor(availableWidth / nodeSpacing));
        const rows = Math.max(1, Math.ceil(nodes.length / cols));

        nodes.forEach((node, i) => {
            const col = i % cols;
            const row = Math.floor(i / cols);

            // Posició base
            const x = zone.x + padding + col * nodeSpacing;
            const y = zone.y + padding + row * (availableHeight / rows);

            // Variació orgànica
            const organicVariation = 12;
            const randomX = (Math.random() - 0.5) * organicVariation;
            const randomY = (Math.random() - 0.5) * organicVariation;

            node.x = x + randomX;
            node.y = y + randomY;
            node.fx = node.x;
            node.fy = node.y;
        });
    }

    arrangeRemainingInSpiral(remainingNodes, centerX, centerY) {
        const spiralSpacing = 30;
        let angle = 0;
        let radius = 100;

        remainingNodes.forEach((node, i) => {
            node.x = centerX + radius * Math.cos(angle);
            node.y = centerY + radius * Math.sin(angle);
            node.fx = node.x;
            node.fy = node.y;

            // Incrementar angle i radius per espiral
            angle += 0.5;
            radius += 0.8;
        });
    }

    runManualSimulation() {
        console.log("⚙️ Executant simulació manual...");

        if (window.updateProgress) window.updateProgress(80, "Calculant posicions...");

        // Ticks manuals optimitzats per càrrega ràpida
        const n_ticks = 100; // Reduït per càrrega més ràpida
        for (let i = 0; i < n_ticks; i++) {
            this.simulation.tick();

            // Actualitzar progrés cada 20 ticks
            if (i % 20 === 0 && window.updateProgress) {
                const progress = 80 + Math.floor((i / n_ticks) * 15);
                window.updateProgress(progress, `Simulant... ${Math.floor((i / n_ticks) * 100)}%`);
            }
        }

        // FIXAR totes les posicions (estil UNESCO)
        const simulationNodes = this.simulation.nodes();

        simulationNodes.forEach(d => {
            d.fx = d.x;
            d.fy = d.y;
        });

        // DESTRUIR completament la simulació
        this.destroySimulation();

        if (window.updateProgress) window.updateProgress(95, "Finalitzant simulació...");

        console.log("✅ Simulació manual completada - nodes fixats");
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    destroySimulation() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null);
            // Eliminar totes les forces
            this.simulation.force("link", null);
            this.simulation.force("charge", null);
            this.simulation.force("center", null);
            this.simulation.force("collision", null);
            this.simulation = null; // Eliminar referència
            console.log("💥 Simulació DESTRUÏDA - només navegació manual");
        }
    }

    restart() {
        // NO permetre restart - un cop destruït, destruït
        console.log("⚠️ Restart desactivat - simulació destruïda");
    }

    // Funcions de drag - ESTIL UNESCO (sense simulació)
    dragstarted(event, d) {
        // Només fixar posició inicial
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        // Actualització directa de posició (estil UNESCO)
        d.fx = event.x;
        d.fy = event.y;
        d.x = event.x;
        d.y = event.y;
    }

    dragended(event, d) {
        // Mantenir posició fixa (estil UNESCO)
        // Les posicions fx/fy es mantenen sempre
    }
}
