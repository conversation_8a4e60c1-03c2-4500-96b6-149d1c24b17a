/**
 * G<PERSON><PERSON>ó de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        console.log("🚀 Creant distribució geomètrica equidistant...");

        // DISTRIBUCIÓ GEOMÈTRICA en lloc de simulació física
        this.createGeometricLayout(nodes, links);

        return null; // No simulation needed
    }

    createGeometricLayout(nodes, links) {
        const centerX = this.config.width / 2;
        const centerY = this.config.height / 2;

        // Separar nodes per tipus
        const personNodes = nodes.filter(d => d.type === 'person');
        const memberOfNodes = nodes.filter(d => d.type === 'memberOf');
        const eventNodes = nodes.filter(d => d.type === 'event');

        // Distribució circular per cada tipus
        this.arrangeInCircles(personNodes, centerX, centerY, 200, 0); // Cercle interior
        this.arrangeInCircles(memberOfNodes, centerX, centerY, 350, Math.PI/3); // Cercle mitjà
        this.arrangeInCircles(eventNodes, centerX, centerY, 500, Math.PI/6); // Cercle exterior

        console.log("✅ Distribució geomètrica completada");
    }

    arrangeInCircles(nodes, centerX, centerY, radius, angleOffset) {
        const angleStep = (2 * Math.PI) / nodes.length;

        nodes.forEach((node, i) => {
            const angle = i * angleStep + angleOffset;
            node.x = centerX + radius * Math.cos(angle);
            node.y = centerY + radius * Math.sin(angle);

            // Fixar posicions per evitar moviment
            node.fx = node.x;
            node.fy = node.y;
        });
    }

    runManualSimulation() {
        console.log("⚙️ Executant simulació manual...");

        if (window.updateProgress) window.updateProgress(80, "Calculant posicions...");

        // Ticks manuals optimitzats per càrrega ràpida
        const n_ticks = 100; // Reduït per càrrega més ràpida
        for (let i = 0; i < n_ticks; i++) {
            this.simulation.tick();

            // Actualitzar progrés cada 20 ticks
            if (i % 20 === 0 && window.updateProgress) {
                const progress = 80 + Math.floor((i / n_ticks) * 15);
                window.updateProgress(progress, `Simulant... ${Math.floor((i / n_ticks) * 100)}%`);
            }
        }

        // FIXAR totes les posicions (estil UNESCO)
        const simulationNodes = this.simulation.nodes();

        simulationNodes.forEach(d => {
            d.fx = d.x;
            d.fy = d.y;
        });

        // DESTRUIR completament la simulació
        this.destroySimulation();

        if (window.updateProgress) window.updateProgress(95, "Finalitzant simulació...");

        console.log("✅ Simulació manual completada - nodes fixats");
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    destroySimulation() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null);
            // Eliminar totes les forces
            this.simulation.force("link", null);
            this.simulation.force("charge", null);
            this.simulation.force("center", null);
            this.simulation.force("collision", null);
            this.simulation = null; // Eliminar referència
            console.log("💥 Simulació DESTRUÏDA - només navegació manual");
        }
    }

    restart() {
        // NO permetre restart - un cop destruït, destruït
        console.log("⚠️ Restart desactivat - simulació destruïda");
    }

    // Funcions de drag - ESTIL UNESCO (sense simulació)
    dragstarted(event, d) {
        // Només fixar posició inicial
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        // Actualització directa de posició (estil UNESCO)
        d.fx = event.x;
        d.fy = event.y;
        d.x = event.x;
        d.y = event.y;
    }

    dragended(event, d) {
        // Mantenir posició fixa (estil UNESCO)
        // Les posicions fx/fy es mantenen sempre
    }
}
