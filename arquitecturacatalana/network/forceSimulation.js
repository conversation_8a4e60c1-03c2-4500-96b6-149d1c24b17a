/**
 * Gest<PERSON>ó de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        // Crear simulació de forces - TOTALMENT ESTÀTICA
        this.simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links)
                .id(d => d.id)
                .distance(this.config.forceConfig.linkDistance)
                .strength(this.config.forceConfig.linkStrength))
            .force("charge", d3.forceManyBody()
                .strength(this.config.forceConfig.chargeStrength))
            .force("center", d3.forceCenter(this.config.width / 2, this.config.height / 2))
            .force("collision", d3.forceCollide()
                .radius(this.config.forceConfig.collisionRadius))
            .alpha(this.config.forceConfig.alpha)
            .alphaDecay(this.config.forceConfig.alphaDecay)
            .velocityDecay(this.config.forceConfig.velocityDecay);

        // Aturar completament després del timeout configurat
        setTimeout(() => {
            this.stop();
        }, this.config.forceConfig.simulationTimeout);

        return this.simulation;
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    restart() {
        if (this.simulation) {
            this.simulation.restart();
        }
    }

    // Funcions de drag - DESACTIVADES per mantenir estàtic
    dragstarted(event, d) {
        // No fer res - mantenir estàtic
        return;
    }

    dragged(event, d) {
        // No fer res - mantenir estàtic
        return;
    }

    dragended(event, d) {
        // No fer res - mantenir estàtic
        return;
    }
}
