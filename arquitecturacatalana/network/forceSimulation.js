/**
 * G<PERSON><PERSON><PERSON> de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        console.log("🚀 Creant distribució geomètrica equidistant...");

        // DISTRIBUCIÓ GEOMÈTRICA en lloc de simulació física
        this.createGeometricLayout(nodes, links);

        return null; // No simulation needed
    }

    createGeometricLayout(nodes, links) {
        // DISTRIBUCIÓ ORIGAMI - fluida, corba i equidistant
        this.createOrigamiLayout(nodes);

        console.log("✅ Distribució origami completada");
    }

    createZonedOrganicLayout(nodes) {
        const width = this.config.width;
        const height = this.config.height;

        // Agrupar per tipus i subcategories
        const groups = this.groupNodesByTypeAndCategory(nodes);

        // Definir zones per cada tipus
        const zones = {
            person: { x: 0, y: 0, width: width * 0.4, height: height },
            memberOf: { x: width * 0.4, y: 0, width: width * 0.3, height: height },
            event: { x: width * 0.7, y: 0, width: width * 0.3, height: height }
        };

        // Distribuir cada grup en la seva zona
        Object.keys(groups).forEach(type => {
            const zone = zones[type];
            const typeGroups = groups[type];

            this.distributeGroupsInZone(typeGroups, zone);
        });
    }

    createOrigamiLayout(nodes) {
        const width = this.config.width;
        const height = this.config.height;
        const centerX = width / 2;
        const centerY = height / 2;

        // Barrejar nodes
        const shuffledNodes = this.shuffleArray([...nodes]);

        // Crear múltiples ones/corbes que s'entrecreuen
        const waves = this.generateWavePatterns(width, height, shuffledNodes.length);

        const minDistance = 35; // Distància mínima entre nodes
        const placedNodes = [];

        shuffledNodes.forEach((node, i) => {
            let placed = false;
            let attempts = 0;

            while (!placed && attempts < 30) {
                // Obtenir posició de les ones
                const wavePosition = this.getWavePosition(i, waves, width, height);

                // Afegir variació orgànica suau
                const organicX = wavePosition.x + (Math.random() - 0.5) * 15;
                const organicY = wavePosition.y + (Math.random() - 0.5) * 15;

                // Assegurar que està dins dels límits
                const x = Math.max(30, Math.min(width - 30, organicX));
                const y = Math.max(30, Math.min(height - 30, organicY));

                // Comprovar distància amb altres nodes
                const tooClose = placedNodes.some(placedNode => {
                    const dx = x - placedNode.x;
                    const dy = y - placedNode.y;
                    return Math.sqrt(dx * dx + dy * dy) < minDistance;
                });

                if (!tooClose) {
                    node.x = x;
                    node.y = y;
                    node.fx = node.x;
                    node.fy = node.y;
                    placedNodes.push(node);
                    placed = true;
                }

                attempts++;
            }

            // Si no s'ha pogut col·locar, usar posició aleatòria segura
            if (!placed) {
                let safeX, safeY;
                let safeAttempts = 0;

                do {
                    safeX = 50 + Math.random() * (width - 100);
                    safeY = 50 + Math.random() * (height - 100);
                    safeAttempts++;
                } while (safeAttempts < 20 && placedNodes.some(p => {
                    const dx = safeX - p.x;
                    const dy = safeY - p.y;
                    return Math.sqrt(dx * dx + dy * dy) < minDistance;
                }));

                node.x = safeX;
                node.y = safeY;
                node.fx = node.x;
                node.fy = node.y;
                placedNodes.push(node);
            }
        });
    }

    generateWavePatterns(width, height, nodeCount) {
        // Crear múltiples ones sinusoidals que s'entrecreuen
        return [
            { // Ona horitzontal principal
                amplitude: height * 0.15,
                frequency: 0.008,
                phase: 0,
                direction: 'horizontal',
                centerY: height * 0.3
            },
            { // Ona horitzontal secundària
                amplitude: height * 0.12,
                frequency: 0.012,
                phase: Math.PI,
                direction: 'horizontal',
                centerY: height * 0.7
            },
            { // Ona vertical
                amplitude: width * 0.1,
                frequency: 0.01,
                phase: Math.PI/2,
                direction: 'vertical',
                centerX: width * 0.5
            }
        ];
    }

    getWavePosition(index, waves, width, height) {
        const progress = index / 11237; // Total nodes aproximat

        let x = progress * width;
        let y = height / 2;

        // Aplicar cada ona
        waves.forEach(wave => {
            if (wave.direction === 'horizontal') {
                y += wave.amplitude * Math.sin(wave.frequency * x + wave.phase);
                y = Math.max(0, Math.min(height, wave.centerY + (y - height/2) * 0.5));
            } else if (wave.direction === 'vertical') {
                const verticalProgress = (index % 100) / 100;
                y = verticalProgress * height;
                x += wave.amplitude * Math.sin(wave.frequency * y + wave.phase);
                x = Math.max(0, Math.min(width, wave.centerX + (x - width/2) * 0.3));
            }
        });

        return { x, y };
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    arrangeRemainingInSpiral(remainingNodes, centerX, centerY) {
        const spiralSpacing = 30;
        let angle = 0;
        let radius = 100;

        remainingNodes.forEach((node, i) => {
            node.x = centerX + radius * Math.cos(angle);
            node.y = centerY + radius * Math.sin(angle);
            node.fx = node.x;
            node.fy = node.y;

            // Incrementar angle i radius per espiral
            angle += 0.5;
            radius += 0.8;
        });
    }

    runManualSimulation() {
        console.log("⚙️ Executant simulació manual...");

        if (window.updateProgress) window.updateProgress(80, "Calculant posicions...");

        // Ticks manuals optimitzats per càrrega ràpida
        const n_ticks = 100; // Reduït per càrrega més ràpida
        for (let i = 0; i < n_ticks; i++) {
            this.simulation.tick();

            // Actualitzar progrés cada 20 ticks
            if (i % 20 === 0 && window.updateProgress) {
                const progress = 80 + Math.floor((i / n_ticks) * 15);
                window.updateProgress(progress, `Simulant... ${Math.floor((i / n_ticks) * 100)}%`);
            }
        }

        // FIXAR totes les posicions (estil UNESCO)
        const simulationNodes = this.simulation.nodes();

        simulationNodes.forEach(d => {
            d.fx = d.x;
            d.fy = d.y;
        });

        // DESTRUIR completament la simulació
        this.destroySimulation();

        if (window.updateProgress) window.updateProgress(95, "Finalitzant simulació...");

        console.log("✅ Simulació manual completada - nodes fixats");
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    destroySimulation() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null);
            // Eliminar totes les forces
            this.simulation.force("link", null);
            this.simulation.force("charge", null);
            this.simulation.force("center", null);
            this.simulation.force("collision", null);
            this.simulation = null; // Eliminar referència
            console.log("💥 Simulació DESTRUÏDA - només navegació manual");
        }
    }

    restart() {
        // NO permetre restart - un cop destruït, destruït
        console.log("⚠️ Restart desactivat - simulació destruïda");
    }

    // Funcions de drag - ESTIL UNESCO (sense simulació)
    dragstarted(event, d) {
        // Només fixar posició inicial
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        // Actualització directa de posició (estil UNESCO)
        d.fx = event.x;
        d.fy = event.y;
        d.x = event.x;
        d.y = event.y;
    }

    dragended(event, d) {
        // Mantenir posició fixa (estil UNESCO)
        // Les posicions fx/fy es mantenen sempre
    }
}
