/**
 * Gestió de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        // Crear simulació NOMÉS per posicionament inicial
        this.simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links)
                .id(d => d.id)
                .distance(this.config.forceConfig.linkDistance)
                .strength(this.config.forceConfig.linkStrength))
            .force("charge", d3.forceManyBody()
                .strength(this.config.forceConfig.chargeStrength))
            .force("center", d3.forceCenter(this.config.width / 2, this.config.height / 2))
            .force("collision", d3.forceCollide()
                .radius(this.config.forceConfig.collisionRadius))
            .alpha(this.config.forceConfig.alpha)
            .alphaDecay(this.config.forceConfig.alphaDecay)
            .velocityDecay(this.config.forceConfig.velocityDecay);

        // Aturar DEFINITIVAMENT després del posicionament
        setTimeout(() => {
            this.destroySimulation();
        }, this.config.forceConfig.simulationTimeout);

        // Aturar també quan l'alpha sigui molt baix
        this.simulation.on("tick", () => {
            if (this.simulation.alpha() < 0.01) {
                this.destroySimulation();
            }
        });

        return this.simulation;
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    destroySimulation() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null);
            // Eliminar totes les forces
            this.simulation.force("link", null);
            this.simulation.force("charge", null);
            this.simulation.force("center", null);
            this.simulation.force("collision", null);
            this.simulation = null; // Eliminar referència
            console.log("💥 Simulació DESTRUÏDA - només navegació manual");
        }
    }

    restart() {
        // NO permetre restart - un cop destruït, destruït
        console.log("⚠️ Restart desactivat - simulació destruïda");
    }

    // Funcions de drag - SENSE SIMULACIÓ
    dragstarted(event, d) {
        // NO reactivar simulació - només drag manual
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        // Actualització manual de posició
        d.fx = event.x;
        d.fy = event.y;
        d.x = event.x;
        d.y = event.y;
    }

    dragended(event, d) {
        // Mantenir posició fixa - NO alliberar
        // d.fx i d.fy es mantenen per evitar que es mogui
    }
}
