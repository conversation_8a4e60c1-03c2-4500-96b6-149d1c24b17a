/**
 * G<PERSON><PERSON>ó de la simulació de forces
 */
export class ForceSimulation {
    constructor(config) {
        this.config = config;
        this.simulation = null;
    }

    create(nodes, links) {
        console.log("🚀 Creant distribució geomètrica equidistant...");

        // DISTRIBUCIÓ GEOMÈTRICA en lloc de simulació física
        this.createGeometricLayout(nodes, links);

        return null; // No simulation needed
    }

    createGeometricLayout(nodes, links) {
        // DISTRIBUCIÓ EN ESPIRAL ORGÀNICA amb nodes ben separats
        this.createOrganicSpiralLayout(nodes);

        console.log("✅ Distribució espiral orgànica completada");
    }

    createZonedOrganicLayout(nodes) {
        const width = this.config.width;
        const height = this.config.height;

        // Agrupar per tipus i subcategories
        const groups = this.groupNodesByTypeAndCategory(nodes);

        // Definir zones per cada tipus
        const zones = {
            person: { x: 0, y: 0, width: width * 0.4, height: height },
            memberOf: { x: width * 0.4, y: 0, width: width * 0.3, height: height },
            event: { x: width * 0.7, y: 0, width: width * 0.3, height: height }
        };

        // Distribuir cada grup en la seva zona
        Object.keys(groups).forEach(type => {
            const zone = zones[type];
            const typeGroups = groups[type];

            this.distributeGroupsInZone(typeGroups, zone);
        });
    }

    createOrganicSpiralLayout(nodes) {
        const centerX = this.config.width / 2;
        const centerY = this.config.height / 2;

        // Barrejar nodes per evitar agrupacions per tipus
        const shuffledNodes = this.shuffleArray([...nodes]);

        // Paràmetres de l'espiral
        let angle = 0;
        let radius = 50;
        const angleIncrement = 0.3; // Més petit = més dens
        const radiusIncrement = 1.2; // Més gran = més separat
        const minDistance = 40; // Distància mínima entre nodes

        const placedNodes = [];

        shuffledNodes.forEach((node, i) => {
            let placed = false;
            let attempts = 0;

            while (!placed && attempts < 50) {
                // Calcular posició en espiral
                const x = centerX + radius * Math.cos(angle);
                const y = centerY + radius * Math.sin(angle);

                // Afegir variació orgànica
                const organicX = x + (Math.random() - 0.5) * 20;
                const organicY = y + (Math.random() - 0.5) * 20;

                // Comprovar si està prou lluny d'altres nodes
                const tooClose = placedNodes.some(placedNode => {
                    const dx = organicX - placedNode.x;
                    const dy = organicY - placedNode.y;
                    return Math.sqrt(dx * dx + dy * dy) < minDistance;
                });

                if (!tooClose) {
                    node.x = organicX;
                    node.y = organicY;
                    node.fx = node.x;
                    node.fy = node.y;
                    placedNodes.push(node);
                    placed = true;
                } else {
                    // Si està massa a prop, avançar en l'espiral
                    angle += angleIncrement;
                    radius += radiusIncrement;
                }

                attempts++;
            }

            // Si no s'ha pogut col·locar, posar-lo lluny
            if (!placed) {
                node.x = centerX + (Math.random() - 0.5) * this.config.width;
                node.y = centerY + (Math.random() - 0.5) * this.config.height;
                node.fx = node.x;
                node.fy = node.y;
            }

            // Avançar en l'espiral per al següent node
            angle += angleIncrement;
            radius += radiusIncrement;
        });
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    arrangeRemainingInSpiral(remainingNodes, centerX, centerY) {
        const spiralSpacing = 30;
        let angle = 0;
        let radius = 100;

        remainingNodes.forEach((node, i) => {
            node.x = centerX + radius * Math.cos(angle);
            node.y = centerY + radius * Math.sin(angle);
            node.fx = node.x;
            node.fy = node.y;

            // Incrementar angle i radius per espiral
            angle += 0.5;
            radius += 0.8;
        });
    }

    runManualSimulation() {
        console.log("⚙️ Executant simulació manual...");

        if (window.updateProgress) window.updateProgress(80, "Calculant posicions...");

        // Ticks manuals optimitzats per càrrega ràpida
        const n_ticks = 100; // Reduït per càrrega més ràpida
        for (let i = 0; i < n_ticks; i++) {
            this.simulation.tick();

            // Actualitzar progrés cada 20 ticks
            if (i % 20 === 0 && window.updateProgress) {
                const progress = 80 + Math.floor((i / n_ticks) * 15);
                window.updateProgress(progress, `Simulant... ${Math.floor((i / n_ticks) * 100)}%`);
            }
        }

        // FIXAR totes les posicions (estil UNESCO)
        const simulationNodes = this.simulation.nodes();

        simulationNodes.forEach(d => {
            d.fx = d.x;
            d.fy = d.y;
        });

        // DESTRUIR completament la simulació
        this.destroySimulation();

        if (window.updateProgress) window.updateProgress(95, "Finalitzant simulació...");

        console.log("✅ Simulació manual completada - nodes fixats");
    }

    onTick(callback) {
        if (this.simulation) {
            this.simulation.on("tick", callback);
        }
    }

    stop() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null); // Eliminar listener de tick
            console.log("🛑 Simulació aturada - nodes completament estàtics");
        }
    }

    destroySimulation() {
        if (this.simulation) {
            this.simulation.stop();
            this.simulation.on("tick", null);
            // Eliminar totes les forces
            this.simulation.force("link", null);
            this.simulation.force("charge", null);
            this.simulation.force("center", null);
            this.simulation.force("collision", null);
            this.simulation = null; // Eliminar referència
            console.log("💥 Simulació DESTRUÏDA - només navegació manual");
        }
    }

    restart() {
        // NO permetre restart - un cop destruït, destruït
        console.log("⚠️ Restart desactivat - simulació destruïda");
    }

    // Funcions de drag - ESTIL UNESCO (sense simulació)
    dragstarted(event, d) {
        // Només fixar posició inicial
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        // Actualització directa de posició (estil UNESCO)
        d.fx = event.x;
        d.fy = event.y;
        d.x = event.x;
        d.y = event.y;
    }

    dragended(event, d) {
        // Mantenir posició fixa (estil UNESCO)
        // Les posicions fx/fy es mantenen sempre
    }
}
