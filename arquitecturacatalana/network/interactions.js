/**
 * Gestió d'interaccions de la visualització
 */
export class NetworkInteractions {
    constructor(config, visualization) {
        this.config = config;
        this.visualization = visualization;
        this.activeNodes = new Set();
        this.searchActiveNodes = new Set();
        this.tooltip = null;
        this.setupTooltip();
    }

    setupTooltip() {
        this.tooltip = d3.select("body").append("div")
            .attr("class", "tooltip")
            .style("position", "absolute")
            .style("padding", "10px")
            .style("background", "rgba(0, 0, 0, 0.8)")
            .style("color", "white")
            .style("border-radius", "5px")
            .style("pointer-events", "none")
            .style("opacity", 0)
            .style("font-size", "12px")
            .style("z-index", "1000");
    }

    // Gestió d'esdeveniments del ratolí - RESTAURADA
    handleMouseOver(event, d) {
        // Sempre mostrar connectors en mouseover si no hi ha nodes actius
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.highlightConnectedNodes(d);
        }
        this.showTooltip(event, d);
    }

    handleMouseOut(event, d) {
        // Només amagar si no hi ha nodes actius
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.resetHighlight();
        }
        this.hideTooltip();
    }

    handleNodeClick(event, d) {
        event.stopPropagation();

        if (this.activeNodes.has(d.id)) {
            // Si el node ja està actiu, desactivar-lo
            this.activeNodes.delete(d.id);
            if (this.activeNodes.size === 0) {
                this.resetHighlight();
            } else {
                this.highlightActiveNodes();
            }
        } else {
            // Si el node no està actiu, afegir-lo als actius
            // Si ja hi ha nodes actius, expandir la selecció
            this.activeNodes.add(d.id);
            this.highlightActiveNodes();
        }

        this.updateInfo();
    }

    // Funcions de highlight
    highlightConnectedNodes(node) {
        const connectedNodeIds = new Set([node.id]);
        const connectedLinks = this.visualization.dataProcessor.getLinksForNode(node.id);

        connectedLinks.forEach(link => {
            connectedNodeIds.add(link.source.id || link.source);
            connectedNodeIds.add(link.target.id || link.target);
        });

        // Usar Canvas per highlight
        this.drawHighlighted(connectedNodeIds, connectedLinks);
    }

    // Dibuixar elements destacats (Canvas)
    drawHighlighted(connectedNodeIds, connectedLinks) {
        // Netejar canvas hover
        this.visualization.ctx_hover.clearRect(
            -this.visualization.total_width/2,
            -this.visualization.total_height/2,
            this.visualization.total_width,
            this.visualization.total_height
        );

        // Dibuixar enllaços connectats
        this.visualization.ctx_hover.strokeStyle = "#fff";
        this.visualization.ctx_hover.lineWidth = 2;
        this.visualization.ctx_hover.globalAlpha = 0.8;

        connectedLinks.forEach(link => {
            this.visualization.drawSingleEdge(this.visualization.ctx_hover, link);
        });

        this.visualization.ctx_hover.globalAlpha = 1;
    }

    highlightActiveNodes() {
        const connectedNodeIds = new Set();
        const connectedLinkIds = new Set();
        
        this.activeNodes.forEach(nodeId => {
            connectedNodeIds.add(nodeId);
            const links = this.visualization.dataProcessor.getLinksForNode(nodeId);
            links.forEach(link => {
                connectedNodeIds.add(link.source.id || link.source);
                connectedNodeIds.add(link.target.id || link.target);
                connectedLinkIds.add(link.id);
            });
        });

        this.applyHighlight(connectedNodeIds, connectedLinkIds);
    }

    highlightSearchNodes(nodeIds) {
        this.searchActiveNodes = new Set(nodeIds);
        const connectedNodeIds = new Set();
        const connectedLinkIds = new Set();
        
        nodeIds.forEach(nodeId => {
            connectedNodeIds.add(nodeId);
            const links = this.visualization.dataProcessor.getLinksForNode(nodeId);
            links.forEach(link => {
                connectedNodeIds.add(link.source.id || link.source);
                connectedNodeIds.add(link.target.id || link.target);
                connectedLinkIds.add(link.id);
            });
        });

        this.applyHighlight(connectedNodeIds, connectedLinkIds);
    }

    applyHighlight(nodeIds, linkIds) {
        // Highlight nodes - apagar els que no estan connectats
        this.visualization.nodeElements
            .style("opacity", d => nodeIds.has(d.id) ?
                this.config.visual.opacity.highlighted :
                this.config.visual.opacity.dimmed)
            .attr("stroke-width", d => nodeIds.has(d.id) ?
                (d.type === 'person' ? 4 : 3) :
                this.config.visual.nodeStrokeWidth(d));

        // Mostrar només els enllaços connectats
        this.visualization.linkElements
            .style("opacity", d => linkIds.has(d.id) ?
                this.config.visual.opacity.highlighted : 0)
            .attr("stroke-width", d => linkIds.has(d.id) ?
                this.config.visual.linkStrokeWidth(d) + 1 :
                this.config.visual.linkStrokeWidth(d));
    }

    resetHighlight() {
        // Netejar completament el canvas hover
        this.visualization.ctx_hover.save();
        this.visualization.ctx_hover.setTransform(1, 0, 0, 1, 0, 0);
        this.visualization.ctx_hover.clearRect(0, 0,
            this.visualization.ctx_hover.canvas.width,
            this.visualization.ctx_hover.canvas.height);
        this.visualization.ctx_hover.restore();
    }

    // Tooltip
    showTooltip(event, d) {
        this.tooltip.transition()
            .duration(this.config.animations.tooltipDuration)
            .style("opacity", 0.9);
            
        let tooltipContent = `<strong>${d.name}</strong><br/>Type: ${d.type}`;
        
        if (d.type === 'person' && d.inScheme) {
            tooltipContent += `<br/>Category: ${d.inScheme}`;
        }
        
        tooltipContent += `<br/>Connections: ${d.connectionCount || 0}`;
        
        this.tooltip.html(tooltipContent)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 28) + "px");
    }

    hideTooltip() {
        this.tooltip.transition()
            .duration(this.config.animations.tooltipDuration)
            .style("opacity", 0);
    }

    // Cerca
    search(query) {
        if (!query.trim()) {
            this.searchActiveNodes.clear();
            this.resetHighlight();
            return [];
        }

        const results = this.visualization.dataProcessor.searchNodes(query);
        const resultIds = results.map(node => node.id);
        
        if (resultIds.length > 0) {
            this.highlightSearchNodes(resultIds);
        } else {
            this.resetHighlight();
        }

        return results;
    }

    clearSearch() {
        this.searchActiveNodes.clear();
        if (this.activeNodes.size === 0) {
            this.resetHighlight();
        } else {
            this.highlightActiveNodes();
        }
    }

    // Actualitzar informació
    updateInfo() {
        const totalNodes = this.visualization.dataProcessor.nodes.length;
        const totalLinks = this.visualization.dataProcessor.links.length;

        d3.select("#nodeCount").text(totalNodes);
        d3.select("#linkCount").text(totalLinks);
        d3.select("#activeCount").text(this.activeNodes.size);
        d3.select("#searchCount").text(this.searchActiveNodes.size);
    }
}
