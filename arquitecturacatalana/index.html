<!DOCTYPE html>
<html lang="ca">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arquitectura Catalana - Network Visualization</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
            overflow: hidden;
        }
        
        .top-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        h1 {
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        
        .controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .search-container {
            position: relative;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            width: 250px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            border-color: #4285f4;
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .search-result {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }
        
        .search-result:hover {
            background-color: #f8f9fa;
        }
        
        .search-result:last-child {
            border-bottom: none;
        }
        
        .legend-container {
            display: flex;
            flex-direction: column;
            gap: 3px;
            max-width: 300px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 11px;
            color: #333;
            margin-bottom: 2px;
        }

        .legend-color {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 1px solid;
            flex-shrink: 0;
        }
        
        button {
            padding: 8px 16px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background: #3367d6;
        }
        
        #network {
            width: 100vw;
            height: 100vh;
            background: white;
            padding-top: 80px;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            stroke-width: 3px;
        }
        
        .link {
            stroke: #999;
            stroke-opacity: 0.6;
            fill: none;
        }
        
        .temp-label {
            font-family: 'Segoe UI', sans-serif;
            font-size: 11px;
            fill: #333;
            pointer-events: none;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        
        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            font-size: 12px;
            color: #666;
            max-width: 300px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="top-panel">
        <h1>Arquitectura Catalana</h1>
        
        <div class="controls">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search architects, companies, projects..." id="searchInput">
                <div class="search-results" id="searchResults"></div>
            </div>
            
            <div class="legend-container">
                <div id="typeLegend"></div>
            </div>
            
            <button onclick="resetView()">Reset View</button>
        </div>
    </div>
    
    <!-- Loading indicator -->
    <div id="loading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                            background: rgba(0,0,0,0.8); color: white; padding: 30px; border-radius: 10px;
                            font-size: 18px; z-index: 1000; text-align: center; min-width: 300px;">
        <div>🚀 Carregant Arquitectura Catalana...</div>
        <div id="loadingProgress" style="margin-top: 15px; font-size: 16px; font-weight: bold;">0%</div>
        <div style="width: 100%; background: rgba(255,255,255,0.2); height: 8px; border-radius: 4px; margin-top: 10px;">
            <div id="progressBar" style="width: 0%; background: #4285f4; height: 100%; border-radius: 4px; transition: width 0.3s;"></div>
        </div>
    </div>

    <div id="network"></div>
    
    <div class="tooltip" id="tooltip"></div>
    
    <div class="info-panel" id="infoPanel">
        <div class="info-item"><strong>Total Nodes:</strong> <span id="nodeCount">0</span></div>
        <div class="info-item"><strong>Total Links:</strong> <span id="linkCount">0</span></div>
        <div class="info-item"><strong>Visible Nodes:</strong> <span id="visibleNodes">0</span></div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script type="module" src="network/main.js"></script>
</body>
</html>
