#!/usr/bin/env python3
"""
Filtra només els actors amb show: true
"""

import json
from datetime import datetime

def filter_show_true():
    """Filtra actors amb show: true"""
    
    print("📂 Carregant fitxer tots_actors_coac.json...")
    
    try:
        with open('tots_actors_coac.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ Error carregant fitxer: {e}")
        return
    
    all_actors = data.get('actors', [])
    print(f"📋 Total actors al fitxer: {len(all_actors)}")
    
    # Filtrar només els que tenen show: true
    filtered_actors = [actor for actor in all_actors if actor.get('show') == True]
    
    print(f"✅ Actors amb show: true: {len(filtered_actors)}")
    print(f"📊 Percentatge: {(len(filtered_actors)/len(all_actors)*100):.1f}%")
    
    # Crear fitxer filtrat
    filtered_data = {
        'metadata': {
            'total_actors': len(filtered_actors),
            'filtered_from': len(all_actors),
            'filter_criteria': 'show: true',
            'filtered_at': datetime.now().isoformat(),
            'original_source': data.get('metadata', {}).get('source_api', 'Unknown')
        },
        'actors': filtered_actors
    }
    
    # Guardar fitxer filtrat
    with open('actors_show_true.json', 'w', encoding='utf-8') as f:
        json.dump(filtered_data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 Actors filtrats guardats a: actors_show_true.json")
    
    # Crear resum
    actors_with_companies = len([a for a in filtered_actors if a.get('memberOf')])
    actors_with_events = len([a for a in filtered_actors if a.get('events')])
    
    summary = {
        'resum': {
            'total_actors_filtrats': len(filtered_actors),
            'total_actors_original': len(all_actors),
            'percentatge_filtrat': round((len(filtered_actors)/len(all_actors)*100), 1),
            'actors_amb_companyies': actors_with_companies,
            'actors_amb_obres': actors_with_events,
            'data_filtratge': filtered_data['metadata']['filtered_at']
        },
        'mostra_actors': [
            {
                'name': actor.get('name'),
                'show': actor.get('show'),
                'companyies': len(actor.get('memberOf', [])),
                'obres': len(actor.get('events', []))
            }
            for actor in filtered_actors[:10]  # Primers 10 actors
        ]
    }
    
    with open('resum_show_true.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"📊 Resum guardat a: resum_show_true.json")
    
    print(f"\n✅ Filtratge completat!")
    print(f"📈 Estadístiques:")
    print(f"   - Actors originals: {len(all_actors)}")
    print(f"   - Actors amb show: true: {len(filtered_actors)}")
    print(f"   - Actors amb companyies: {actors_with_companies}")
    print(f"   - Actors amb obres: {actors_with_events}")
    
    # Mostrar alguns exemples
    if filtered_actors:
        print(f"\n🎯 Exemples d'actors amb show: true:")
        for i, actor in enumerate(filtered_actors[:5]):
            name = actor.get('name', 'Sense nom')
            companies = len(actor.get('memberOf', []))
            events = len(actor.get('events', []))
            print(f"   {i+1}. {name} - Companyies: {companies}, Obres: {events}")

if __name__ == "__main__":
    print("🚀 Filtrant actors amb show: true\n")
    filter_show_true()
