// UNESCO Network Visualization
// Adaptat del codi original de createConstellationVisual.js

class UNESCONetworkVisualization {
    constructor() {
        // Configuració bàsica
        this.width = window.innerWidth;
        this.height = window.innerHeight - 80; // Espai per al panel superior
        this.margin = { top: 20, right: 20, bottom: 20, left: 20 };
        
        // Variables de dades
        this.nodes = [];
        this.links = [];
        this.nodeById = new Map();
        this.linksByNode = new Map();
        
        // Variables d'estat
        this.activeNodes = new Set();
        this.searchActiveNodes = new Set();
        this.currentFilter = 'all';
        
        // Elements DOM
        this.svg = null;
        this.g = null;
        this.simulation = null;
        this.nodeElements = null;
        this.linkElements = null;
        this.tooltip = null;
        this.zoom = null;
        
        // Configuració visual
        this.nodeTypes = ["element", "project", "ngo", "whc", "concept", "country", "region"];
        this.colors = {
            'element': '#000000',
            'project': '#E4650B', 
            'ngo': '#616161',
            'whc': '#101420',
            'concept': '#08977F',
            'country': '#7EB852',
            'region': '#7EB852'
        };
        
        // Escales
        this.radiusScale = d3.scaleOrdinal()
            .domain(this.nodeTypes)
            .range([8, 7, 7, 7, 3, 6, 8])
            .unknown(4);
            
        this.chargeScale = d3.scaleOrdinal()
            .domain(this.nodeTypes)
            .range([-80, -30, -50, -50, -10, -30, -10])
            .unknown(-20);
    }
    
    init() {
        this.createSVG();
        this.createTooltip();
        this.setupZoom();
        this.setupSearch();
        this.createLegend();
        this.loadData();
        
        // Redimensionar en canviar la finestra
        window.addEventListener('resize', () => this.handleResize());
    }
    
    createSVG() {
        const container = d3.select("#network");
        
        this.svg = container.append("svg")
            .attr("width", this.width)
            .attr("height", this.height)
            .style("background", "white");
            
        this.g = this.svg.append("g")
            .attr("class", "main-group");
    }
    
    createTooltip() {
        this.tooltip = d3.select("#tooltip");
    }
    
    setupZoom() {
        this.zoom = d3.zoom()
            .scaleExtent([0.3, 6])
            .on("zoom", (event) => {
                this.g.attr("transform", event.transform);
            });
            
        this.svg.call(this.zoom);
        
        // Click fora dels nodes per reset
        this.svg.on("click", (event) => {
            if (event.target.tagName === 'svg') {
                this.resetActiveState();
            }
        });
    }
    
    setupSearch() {
        const searchInput = d3.select("#searchInput");
        const searchResults = d3.select("#searchResults");
        
        searchInput.on("input", () => {
            const query = searchInput.property("value").toLowerCase().trim();
            
            if (query.length < 2) {
                searchResults.style("display", "none");
                return;
            }
            
            const matches = this.nodes.filter(node => 
                node.name && node.name.toLowerCase().includes(query)
            ).slice(0, 10);
            
            if (matches.length > 0) {
                searchResults.html(matches.map(node => 
                    `<div class="search-result" onclick="networkViz.highlightNode('${node.id}')">${node.name} (${node.type})</div>`
                ).join(''));
                searchResults.style("display", "block");
            } else {
                searchResults.style("display", "none");
            }
        });
        
        // Tancar resultats quan es clica fora
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                searchResults.style("display", "none");
            }
        });
    }
    
    createLegend() {
        const legendContainer = d3.select("#typeLegend");
        
        legendContainer.selectAll(".legend-item")
            .data(this.nodeTypes)
            .enter().append("div")
            .attr("class", "legend-item")
            .html(d => `
                <div class="legend-color" style="background-color: ${this.colors[d]}; border-color: ${this.colors[d]}"></div>
                <span>${d}</span>
            `);
    }
    
    async loadData() {
        try {
            // Aquí carregaríem les dades reals
            // Per ara, creem dades de mostra
            this.generateSampleData();
            this.processData();
            this.createVisualization();
            this.updateInfo();
        } catch (error) {
            console.error("Error loading data:", error);
        }
    }
    
    generateSampleData() {
        // Generar dades de mostra basades en l'estructura UNESCO
        const sampleNodes = [];
        const sampleLinks = [];
        
        // Crear nodes de diferents tipus
        for (let i = 0; i < 100; i++) {
            const type = this.nodeTypes[Math.floor(Math.random() * this.nodeTypes.length)];
            sampleNodes.push({
                id: `node_${i}`,
                name: `${type.charAt(0).toUpperCase() + type.slice(1)} ${i + 1}`,
                type: type,
                degree: Math.floor(Math.random() * 10) + 1
            });
        }
        
        // Crear enllaços aleatoris
        for (let i = 0; i < 150; i++) {
            const source = sampleNodes[Math.floor(Math.random() * sampleNodes.length)];
            const target = sampleNodes[Math.floor(Math.random() * sampleNodes.length)];
            
            if (source.id !== target.id) {
                sampleLinks.push({
                    id: `link_${i}`,
                    source: source.id,
                    target: target.id,
                    weight: Math.floor(Math.random() * 3) + 1
                });
            }
        }
        
        this.nodes = sampleNodes;
        this.links = sampleLinks;
    }
    
    processData() {
        // Crear mapes per accés ràpid
        this.nodeById.clear();
        this.linksByNode.clear();
        
        this.nodes.forEach(node => {
            this.nodeById.set(node.id, node);
            this.linksByNode.set(node.id, []);
        });
        
        // Processar enllaços
        this.links.forEach(link => {
            const sourceLinks = this.linksByNode.get(link.source) || [];
            const targetLinks = this.linksByNode.get(link.target) || [];
            
            sourceLinks.push(link);
            targetLinks.push(link);
            
            this.linksByNode.set(link.source, sourceLinks);
            this.linksByNode.set(link.target, targetLinks);
        });
        
        // Calcular graus de connexió
        this.nodes.forEach(node => {
            const connections = this.linksByNode.get(node.id) || [];
            node.connectionCount = connections.length;
        });
    }

    createVisualization() {
        // Crear simulació de forces
        this.simulation = d3.forceSimulation(this.nodes)
            .force("link", d3.forceLink(this.links)
                .id(d => d.id)
                .distance(d => 60 + d.weight * 20)
                .strength(0.3))
            .force("charge", d3.forceManyBody()
                .strength(d => this.chargeScale(d.type)))
            .force("center", d3.forceCenter(this.width / 2, this.height / 2))
            .force("collision", d3.forceCollide()
                .radius(d => this.radiusScale(d.type) + 2))
            .alpha(0.8)
            .alphaDecay(0.02);

        // Crear enllaços amb corbes
        this.linkElements = this.g.append("g")
            .attr("class", "links")
            .selectAll("path")
            .data(this.links)
            .enter().append("path")
            .attr("class", "link")
            .attr("stroke", "#999")
            .attr("stroke-width", d => Math.sqrt(d.weight) + 0.5)
            .attr("fill", "none")
            .style("opacity", 0);

        // Crear nodes
        this.nodeElements = this.g.append("g")
            .attr("class", "nodes")
            .selectAll("circle")
            .data(this.nodes)
            .enter().append("circle")
            .attr("class", "node")
            .attr("r", d => this.radiusScale(d.type))
            .attr("fill", d => this.colors[d.type] || '#999')
            .attr("stroke", "#fff")
            .attr("stroke-width", 2)
            .style("cursor", "pointer")
            .call(d3.drag()
                .on("start", (event, d) => this.dragstarted(event, d))
                .on("drag", (event, d) => this.dragged(event, d))
                .on("end", (event, d) => this.dragended(event, d)))
            .on("mouseover", (event, d) => this.handleMouseOver(event, d))
            .on("mouseout", (event, d) => this.handleMouseOut(event, d))
            .on("click", (event, d) => this.handleNodeClick(event, d));

        // Actualitzar posicions en cada tick
        this.simulation.on("tick", () => this.ticked());
    }

    ticked() {
        // Actualitzar posicions dels enllaços amb corbes
        this.linkElements.attr("d", d => {
            const dx = d.target.x - d.source.x;
            const dy = d.target.y - d.source.y;
            const dr = Math.sqrt(dx * dx + dy * dy) * 0.3; // Curvatura

            return `M${d.source.x},${d.source.y}A${dr},${dr} 0 0,1 ${d.target.x},${d.target.y}`;
        });

        // Actualitzar posicions dels nodes
        this.nodeElements
            .attr("cx", d => d.x)
            .attr("cy", d => d.y);
    }

    // Funcions de drag
    dragstarted(event, d) {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }

    dragended(event, d) {
        if (!event.active) this.simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }

    // Gestió d'esdeveniments del ratolí
    handleMouseOver(event, d) {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.highlightConnectedNodes(d);
        }
        this.showTooltip(event, d);
    }

    handleMouseOut(event, d) {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.resetHighlight();
        }
        this.hideTooltip();
    }

    handleNodeClick(event, d) {
        event.stopPropagation();

        if (this.activeNodes.has(d.id)) {
            // Si el node ja està actiu, el desactivem
            this.activeNodes.delete(d.id);
        } else {
            // Afegim el node als actius
            this.activeNodes.add(d.id);

            // Afegim també els seus nodes connectats
            const connections = this.linksByNode.get(d.id) || [];
            connections.forEach(link => {
                const connectedId = link.source === d.id ? link.target : link.source;
                this.activeNodes.add(connectedId);
            });
        }

        this.updateActiveVisualization();
    }

    highlightConnectedNodes(selectedNode) {
        // Trobar nodes connectats
        const connectedNodeIds = new Set([selectedNode.id]);
        const connections = this.linksByNode.get(selectedNode.id) || [];

        connections.forEach(link => {
            const connectedId = link.source === selectedNode.id ? link.target : link.source;
            connectedNodeIds.add(connectedId);
        });

        // Aplicar opacitat als nodes
        this.nodeElements.style("opacity", d => connectedNodeIds.has(d.id) ? 1 : 0.2);

        // Només mostrar enllaços que connecten amb el node seleccionat
        this.linkElements.style("opacity", d =>
            (d.source.id === selectedNode.id || d.target.id === selectedNode.id) ? 0.8 : 0
        );

        // Mostrar labels dels nodes connectats
        this.showConnectedLabels(connectedNodeIds);
    }

    showConnectedLabels(connectedNodeIds) {
        // Eliminar labels anteriors
        this.g.selectAll(".temp-label").remove();

        // Afegir labels per nodes connectats
        this.g.selectAll(".temp-label")
            .data(this.nodes.filter(d => connectedNodeIds.has(d.id)))
            .enter().append("text")
            .attr("class", "temp-label")
            .attr("x", d => d.x + this.radiusScale(d.type) + 5)
            .attr("y", d => d.y + 4)
            .text(d => d.name)
            .style("font-size", "11px")
            .style("fill", "#333")
            .style("pointer-events", "none")
            .style("text-shadow", "1px 1px 2px rgba(255,255,255,0.8)");
    }

    resetHighlight() {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.nodeElements.style("opacity", 1);
            this.linkElements.style("opacity", 0);
            // Eliminar labels temporals
            this.g.selectAll(".temp-label").remove();
        }
    }

    updateActiveVisualization() {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            // Estat normal
            this.nodeElements.style("opacity", 1);
            this.linkElements.style("opacity", 0);
            this.g.selectAll(".temp-label").remove();
        } else {
            // Mostrar només nodes actius
            const allActiveNodes = new Set([...this.activeNodes, ...this.searchActiveNodes]);

            this.nodeElements.style("opacity", d => allActiveNodes.has(d.id) ? 1 : 0.1);

            // Mostrar enllaços entre nodes actius
            this.linkElements.style("opacity", d => {
                const sourceActive = allActiveNodes.has(d.source.id);
                const targetActive = allActiveNodes.has(d.target.id);
                return (sourceActive && targetActive) ? 0.8 : 0;
            });
        }
    }

    resetActiveState() {
        this.activeNodes.clear();
        this.searchActiveNodes.clear();
        this.updateActiveVisualization();
    }

    highlightNode(nodeId) {
        // Afegir node a la cerca activa
        this.searchActiveNodes.add(nodeId);

        // Afegir nodes connectats
        const selectedNode = this.nodeById.get(nodeId);
        if (selectedNode) {
            const connections = this.linksByNode.get(nodeId) || [];
            connections.forEach(link => {
                const connectedId = link.source === nodeId ? link.target : link.source;
                this.searchActiveNodes.add(connectedId);
            });

            this.updateActiveVisualization();

            // Centrar vista en el node
            const transform = d3.zoomIdentity
                .translate(this.width / 2 - selectedNode.x, this.height / 2 - selectedNode.y)
                .scale(1.5);

            this.svg.transition()
                .duration(750)
                .call(this.zoom.transform, transform);
        }

        // Tancar resultats de cerca
        d3.select('#searchResults').style("display", "none");
    }

    showTooltip(event, d) {
        this.tooltip.transition()
            .duration(200)
            .style("opacity", 0.9);
        this.tooltip.html(`<strong>${d.name}</strong><br/>Type: ${d.type}<br/>Connections: ${d.connectionCount}`)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 28) + "px");
    }

    hideTooltip() {
        this.tooltip.transition()
            .duration(500)
            .style("opacity", 0);
    }

    updateInfo() {
        d3.select("#nodeCount").text(this.nodes.length);
        d3.select("#linkCount").text(this.links.length);

        // Actualitzar nodes visibles
        const visibleNodes = this.nodes.filter(node => {
            const opacity = this.nodeElements.filter(d => d.id === node.id).style("opacity");
            return parseFloat(opacity) > 0.5;
        }).length;
        d3.select("#visibleNodes").text(visibleNodes);
    }

    handleResize() {
        this.width = window.innerWidth;
        this.height = window.innerHeight - 80;

        this.svg
            .attr("width", this.width)
            .attr("height", this.height);

        // Actualitzar forces de la simulació
        this.simulation
            .force("center", d3.forceCenter(this.width / 2, this.height / 2))
            .alpha(0.3)
            .restart();
    }
}

// Funció global per reset
function resetView() {
    if (window.networkViz) {
        window.networkViz.resetActiveState();

        // Reset zoom
        window.networkViz.svg.transition()
            .duration(750)
            .call(window.networkViz.zoom.transform, d3.zoomIdentity);
    }
}

// Inicialitzar la visualització
document.addEventListener('DOMContentLoaded', function() {
    window.networkViz = new UNESCONetworkVisualization();
    window.networkViz.init();
});
