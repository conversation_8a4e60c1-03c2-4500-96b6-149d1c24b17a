// UNESCO Network Visualization
// Adaptat del codi original de createConstellationVisual.js

class UNESCONetworkVisualization {
    constructor() {
        // Configuració bàsica
        this.width = window.innerWidth;
        this.height = window.innerHeight - 80; // Espai per al panel superior
        this.margin = { top: 20, right: 20, bottom: 20, left: 20 };
        
        // Variables de dades
        this.nodes = [];
        this.links = [];
        this.nodeById = new Map();
        this.linksByNode = new Map();
        
        // Variables d'estat
        this.activeNodes = new Set();
        this.searchActiveNodes = new Set();
        this.currentFilter = 'all';
        
        // Elements DOM
        this.svg = null;
        this.g = null;
        this.simulation = null;
        this.nodeElements = null;
        this.linkElements = null;
        this.tooltip = null;
        this.zoom = null;
        
        // Configuració visual adaptada per COAC actors
        this.nodeTypes = ["person", "memberOf", "event"];
        this.colors = {
            'person': '#e74c3c',
            'memberOf': '#3498db',
            'event': '#f39c12'
        };

        // Colors per inScheme dels actors
        this.inSchemeColors = {
            'Arquitectes i col·laboradors': '#e74c3c',
            'Societats': '#3498db',
            'Fotògrafs': '#f39c12',
            'Constructors': '#9b59b6',
            'Enginyers': '#2ecc71',
            'Entitats': '#34495e',
            'Grups': '#e67e22'
        };
        
        // Escales adaptades per COAC
        this.radiusScale = d3.scaleOrdinal()
            .domain(this.nodeTypes)
            .range([8, 6, 5]) // person, memberOf, event
            .unknown(4);

        this.chargeScale = d3.scaleOrdinal()
            .domain(this.nodeTypes)
            .range([-100, -50, -30]) // person, memberOf, event
            .unknown(-40);
    }
    
    init() {
        this.createSVG();
        this.createTooltip();
        this.setupZoom();
        this.setupSearch();
        this.createLegend();
        this.loadData();
        
        // Redimensionar en canviar la finestra
        window.addEventListener('resize', () => this.handleResize());
    }
    
    createSVG() {
        const container = d3.select("#network");
        
        this.svg = container.append("svg")
            .attr("width", this.width)
            .attr("height", this.height)
            .style("background", "white");
            
        this.g = this.svg.append("g")
            .attr("class", "main-group");
    }
    
    createTooltip() {
        this.tooltip = d3.select("#tooltip");
    }
    
    setupZoom() {
        this.zoom = d3.zoom()
            .scaleExtent([0.3, 6])
            .on("zoom", (event) => {
                this.g.attr("transform", event.transform);
            });
            
        this.svg.call(this.zoom);
        
        // Click fora dels nodes per reset
        this.svg.on("click", (event) => {
            if (event.target.tagName === 'svg') {
                this.resetActiveState();
            }
        });
    }
    
    setupSearch() {
        const searchInput = d3.select("#searchInput");
        const searchResults = d3.select("#searchResults");
        
        searchInput.on("input", () => {
            const query = searchInput.property("value").toLowerCase().trim();
            
            if (query.length < 2) {
                searchResults.style("display", "none");
                return;
            }
            
            const matches = this.nodes.filter(node => 
                node.name && node.name.toLowerCase().includes(query)
            ).slice(0, 10);
            
            if (matches.length > 0) {
                searchResults.html(matches.map(node => 
                    `<div class="search-result" onclick="networkViz.highlightNode('${node.id}')">${node.name} (${node.type})</div>`
                ).join(''));
                searchResults.style("display", "block");
            } else {
                searchResults.style("display", "none");
            }
        });
        
        // Tancar resultats quan es clica fora
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                searchResults.style("display", "none");
            }
        });
    }
    
    createLegend() {
        const legendContainer = d3.select("#typeLegend");

        // Llegenda de tipus de nodes
        legendContainer.append("div")
            .style("margin-bottom", "10px")
            .html("<strong>Node Types:</strong>");

        legendContainer.selectAll(".legend-item-type")
            .data(this.nodeTypes)
            .enter().append("div")
            .attr("class", "legend-item legend-item-type")
            .html(d => `
                <div class="legend-color" style="background-color: ${this.colors[d]}; border-color: ${this.colors[d]}"></div>
                <span>${d}</span>
            `);

        // Llegenda de colors inScheme per persones
        legendContainer.append("div")
            .style("margin-top", "15px")
            .style("margin-bottom", "10px")
            .html("<strong>Person Categories:</strong>");

        const inSchemeEntries = Object.entries(this.inSchemeColors);
        legendContainer.selectAll(".legend-item-scheme")
            .data(inSchemeEntries)
            .enter().append("div")
            .attr("class", "legend-item legend-item-scheme")
            .html(d => `
                <div class="legend-color" style="background-color: ${d[1]}; border-color: ${d[1]}"></div>
                <span style="font-size: 11px;">${d[0]}</span>
            `);
    }
    
    async loadData() {
        try {
            // Carregar dades dels actors
            const response = await fetch('../actors/actors.json');
            const actorsData = await response.json();
            this.convertActorsData(actorsData);
            this.processData();
            this.createVisualization();
            this.updateInfo();
        } catch (error) {
            console.error("Error loading data:", error);
            // Fallback a dades de mostra si falla
            this.generateSampleData();
            this.processData();
            this.createVisualization();
            this.updateInfo();
        }
    }

    convertActorsData(actorsData) {
        const nodes = [];
        const links = [];
        const memberOfNodes = new Map();
        const eventNodes = new Map();

        // Processar cada actor
        actorsData.forEach(actor => {
            // Afegir node de persona
            const personNode = {
                id: actor.id,
                name: actor.name,
                type: 'person',
                inScheme: actor.inScheme ? actor.inScheme.label : 'Unknown',
                portraitMedia: actor.portraitMedia
            };
            nodes.push(personNode);

            // Processar memberOf (empreses/organitzacions)
            if (actor.memberOf && Array.isArray(actor.memberOf)) {
                actor.memberOf.forEach(member => {
                    if (member.label) {
                        const memberId = `memberOf_${member.label.replace(/\s+/g, '_')}`;

                        if (!memberOfNodes.has(memberId)) {
                            memberOfNodes.set(memberId, {
                                id: memberId,
                                name: member.label,
                                type: 'memberOf',
                                href: member.href
                            });
                            nodes.push(memberOfNodes.get(memberId));
                        }

                        // Crear enllaç entre persona i memberOf
                        links.push({
                            id: `${actor.id}_${memberId}`,
                            source: actor.id,
                            target: memberId,
                            weight: 2,
                            type: 'memberOf'
                        });
                    }
                });
            }

            // Processar events (projectes/obres)
            if (actor.events && Array.isArray(actor.events)) {
                actor.events.forEach(event => {
                    if (event.label && event.slug) { // Filtrar events amb slug null
                        const eventId = `event_${event.slug}`;

                        if (!eventNodes.has(eventId)) {
                            eventNodes.set(eventId, {
                                id: eventId,
                                name: event.label,
                                type: 'event',
                                href: event.href,
                                slug: event.slug
                            });
                            nodes.push(eventNodes.get(eventId));
                        }

                        // Crear enllaç entre persona i event
                        links.push({
                            id: `${actor.id}_${eventId}`,
                            source: actor.id,
                            target: eventId,
                            weight: 1,
                            type: 'event'
                        });
                    }
                });
            }
        });

        this.nodes = nodes;
        this.links = links;

        console.log(`Converted data: ${nodes.length} nodes, ${links.length} links`);
        console.log(`Person nodes: ${nodes.filter(n => n.type === 'person').length}`);
        console.log(`MemberOf nodes: ${nodes.filter(n => n.type === 'memberOf').length}`);
        console.log(`Event nodes: ${nodes.filter(n => n.type === 'event').length}`);
    }
    
    generateSampleData() {
        // Generar dades de mostra basades en l'estructura UNESCO
        const sampleNodes = [];
        const sampleLinks = [];
        
        // Crear nodes de diferents tipus
        for (let i = 0; i < 100; i++) {
            const type = this.nodeTypes[Math.floor(Math.random() * this.nodeTypes.length)];
            sampleNodes.push({
                id: `node_${i}`,
                name: `${type.charAt(0).toUpperCase() + type.slice(1)} ${i + 1}`,
                type: type,
                degree: Math.floor(Math.random() * 10) + 1
            });
        }
        
        // Crear enllaços aleatoris
        for (let i = 0; i < 150; i++) {
            const source = sampleNodes[Math.floor(Math.random() * sampleNodes.length)];
            const target = sampleNodes[Math.floor(Math.random() * sampleNodes.length)];
            
            if (source.id !== target.id) {
                sampleLinks.push({
                    id: `link_${i}`,
                    source: source.id,
                    target: target.id,
                    weight: Math.floor(Math.random() * 3) + 1
                });
            }
        }
        
        this.nodes = sampleNodes;
        this.links = sampleLinks;
    }
    
    processData() {
        // Crear mapes per accés ràpid
        this.nodeById.clear();
        this.linksByNode.clear();
        
        this.nodes.forEach(node => {
            this.nodeById.set(node.id, node);
            this.linksByNode.set(node.id, []);
        });
        
        // Processar enllaços
        this.links.forEach(link => {
            const sourceLinks = this.linksByNode.get(link.source) || [];
            const targetLinks = this.linksByNode.get(link.target) || [];
            
            sourceLinks.push(link);
            targetLinks.push(link);
            
            this.linksByNode.set(link.source, sourceLinks);
            this.linksByNode.set(link.target, targetLinks);
        });
        
        // Calcular graus de connexió
        this.nodes.forEach(node => {
            const connections = this.linksByNode.get(node.id) || [];
            node.connectionCount = connections.length;
        });
    }

    createVisualization() {
        // Crear simulació de forces
        this.simulation = d3.forceSimulation(this.nodes)
            .force("link", d3.forceLink(this.links)
                .id(d => d.id)
                .distance(d => 60 + d.weight * 20)
                .strength(0.3))
            .force("charge", d3.forceManyBody()
                .strength(d => this.chargeScale(d.type)))
            .force("center", d3.forceCenter(this.width / 2, this.height / 2))
            .force("collision", d3.forceCollide()
                .radius(d => this.radiusScale(d.type) + 2))
            .alpha(0.8)
            .alphaDecay(0.02);

        // Crear enllaços amb corbes
        this.linkElements = this.g.append("g")
            .attr("class", "links")
            .selectAll("path")
            .data(this.links)
            .enter().append("path")
            .attr("class", "link")
            .attr("stroke", "#999")
            .attr("stroke-width", d => Math.sqrt(d.weight) + 0.5)
            .attr("fill", "none")
            .style("opacity", 0);

        // Crear nodes
        this.nodeElements = this.g.append("g")
            .attr("class", "nodes")
            .selectAll("circle")
            .data(this.nodes)
            .enter().append("circle")
            .attr("class", "node")
            .attr("r", d => this.radiusScale(d.type))
            .attr("fill", d => {
                if (d.type === 'person' && d.inScheme) {
                    return this.inSchemeColors[d.inScheme] || this.colors[d.type];
                }
                return this.colors[d.type] || '#999';
            })
            .attr("stroke", d => {
                if (d.type === 'person' && d.inScheme) {
                    return this.inSchemeColors[d.inScheme] || "#fff";
                }
                return "#fff";
            })
            .attr("stroke-width", d => d.type === 'person' ? 3 : 2)
            .style("cursor", "pointer")
            .call(d3.drag()
                .on("start", (event, d) => this.dragstarted(event, d))
                .on("drag", (event, d) => this.dragged(event, d))
                .on("end", (event, d) => this.dragended(event, d)))
            .on("mouseover", (event, d) => this.handleMouseOver(event, d))
            .on("mouseout", (event, d) => this.handleMouseOut(event, d))
            .on("click", (event, d) => this.handleNodeClick(event, d));

        // Actualitzar posicions en cada tick
        this.simulation.on("tick", () => this.ticked());
    }

    ticked() {
        // Actualitzar posicions dels enllaços amb corbes
        this.linkElements.attr("d", d => {
            const dx = d.target.x - d.source.x;
            const dy = d.target.y - d.source.y;
            const dr = Math.sqrt(dx * dx + dy * dy) * 0.3; // Curvatura

            return `M${d.source.x},${d.source.y}A${dr},${dr} 0 0,1 ${d.target.x},${d.target.y}`;
        });

        // Actualitzar posicions dels nodes
        this.nodeElements
            .attr("cx", d => d.x)
            .attr("cy", d => d.y);
    }

    // Funcions de drag
    dragstarted(event, d) {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }

    dragended(event, d) {
        if (!event.active) this.simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }

    // Gestió d'esdeveniments del ratolí
    handleMouseOver(event, d) {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.highlightConnectedNodes(d);
        }
        this.showTooltip(event, d);
    }

    handleMouseOut(event, d) {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.resetHighlight();
        }
        this.hideTooltip();
    }

    handleNodeClick(event, d) {
        event.stopPropagation();

        if (this.activeNodes.has(d.id)) {
            // Si el node ja està actiu, el desactivem
            this.activeNodes.delete(d.id);
        } else {
            // Afegim el node als actius
            this.activeNodes.add(d.id);

            // Afegim també els seus nodes connectats
            const connections = this.linksByNode.get(d.id) || [];
            connections.forEach(link => {
                const connectedId = link.source === d.id ? link.target : link.source;
                this.activeNodes.add(connectedId);
            });
        }

        this.updateActiveVisualization();
    }

    highlightConnectedNodes(selectedNode) {
        // Trobar nodes connectats
        const connectedNodeIds = new Set([selectedNode.id]);
        const connections = this.linksByNode.get(selectedNode.id) || [];

        connections.forEach(link => {
            const connectedId = link.source === selectedNode.id ? link.target : link.source;
            connectedNodeIds.add(connectedId);
        });

        // Aplicar opacitat als nodes
        this.nodeElements.style("opacity", d => connectedNodeIds.has(d.id) ? 1 : 0.2);

        // Només mostrar enllaços que connecten amb el node seleccionat
        this.linkElements.style("opacity", d =>
            (d.source.id === selectedNode.id || d.target.id === selectedNode.id) ? 0.8 : 0
        );

        // Mostrar labels dels nodes connectats
        this.showConnectedLabels(connectedNodeIds);
    }

    showConnectedLabels(connectedNodeIds) {
        // Eliminar labels anteriors
        this.g.selectAll(".temp-label").remove();

        // Afegir labels per nodes connectats
        this.g.selectAll(".temp-label")
            .data(this.nodes.filter(d => connectedNodeIds.has(d.id)))
            .enter().append("text")
            .attr("class", "temp-label")
            .attr("x", d => d.x + this.radiusScale(d.type) + 5)
            .attr("y", d => d.y + 4)
            .text(d => d.name)
            .style("font-size", "11px")
            .style("fill", "#333")
            .style("pointer-events", "none")
            .style("text-shadow", "1px 1px 2px rgba(255,255,255,0.8)");
    }

    resetHighlight() {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            this.nodeElements.style("opacity", 1);
            this.linkElements.style("opacity", 0);
            // Eliminar labels temporals
            this.g.selectAll(".temp-label").remove();
        }
    }

    updateActiveVisualization() {
        if (this.activeNodes.size === 0 && this.searchActiveNodes.size === 0) {
            // Estat normal
            this.nodeElements.style("opacity", 1);
            this.linkElements.style("opacity", 0);
            this.g.selectAll(".temp-label").remove();
        } else {
            // Mostrar només nodes actius
            const allActiveNodes = new Set([...this.activeNodes, ...this.searchActiveNodes]);

            this.nodeElements.style("opacity", d => allActiveNodes.has(d.id) ? 1 : 0.1);

            // Mostrar enllaços entre nodes actius
            this.linkElements.style("opacity", d => {
                const sourceActive = allActiveNodes.has(d.source.id);
                const targetActive = allActiveNodes.has(d.target.id);
                return (sourceActive && targetActive) ? 0.8 : 0;
            });
        }
    }

    resetActiveState() {
        this.activeNodes.clear();
        this.searchActiveNodes.clear();
        this.updateActiveVisualization();
    }

    highlightNode(nodeId) {
        // Afegir node a la cerca activa
        this.searchActiveNodes.add(nodeId);

        // Afegir nodes connectats
        const selectedNode = this.nodeById.get(nodeId);
        if (selectedNode) {
            const connections = this.linksByNode.get(nodeId) || [];
            connections.forEach(link => {
                const connectedId = link.source === nodeId ? link.target : link.source;
                this.searchActiveNodes.add(connectedId);
            });

            this.updateActiveVisualization();

            // Centrar vista en el node
            const transform = d3.zoomIdentity
                .translate(this.width / 2 - selectedNode.x, this.height / 2 - selectedNode.y)
                .scale(1.5);

            this.svg.transition()
                .duration(750)
                .call(this.zoom.transform, transform);
        }

        // Tancar resultats de cerca
        d3.select('#searchResults').style("display", "none");
    }

    showTooltip(event, d) {
        this.tooltip.transition()
            .duration(200)
            .style("opacity", 0.9);

        let tooltipContent = `<strong>${d.name}</strong><br/>Type: ${d.type}`;

        if (d.type === 'person' && d.inScheme) {
            tooltipContent += `<br/>Category: ${d.inScheme}`;
        }

        tooltipContent += `<br/>Connections: ${d.connectionCount || 0}`;

        this.tooltip.html(tooltipContent)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 28) + "px");
    }

    hideTooltip() {
        this.tooltip.transition()
            .duration(500)
            .style("opacity", 0);
    }

    updateInfo() {
        d3.select("#nodeCount").text(this.nodes.length);
        d3.select("#linkCount").text(this.links.length);

        // Actualitzar nodes visibles
        const visibleNodes = this.nodes.filter(node => {
            const opacity = this.nodeElements.filter(d => d.id === node.id).style("opacity");
            return parseFloat(opacity) > 0.5;
        }).length;
        d3.select("#visibleNodes").text(visibleNodes);
    }

    handleResize() {
        this.width = window.innerWidth;
        this.height = window.innerHeight - 80;

        this.svg
            .attr("width", this.width)
            .attr("height", this.height);

        // Actualitzar forces de la simulació
        this.simulation
            .force("center", d3.forceCenter(this.width / 2, this.height / 2))
            .alpha(0.3)
            .restart();
    }
}

// Funció global per reset
function resetView() {
    if (window.networkViz) {
        window.networkViz.resetActiveState();

        // Reset zoom
        window.networkViz.svg.transition()
            .duration(750)
            .call(window.networkViz.zoom.transform, d3.zoomIdentity);
    }
}

// Inicialitzar la visualització
document.addEventListener('DOMContentLoaded', function() {
    window.networkViz = new UNESCONetworkVisualization();
    window.networkViz.init();
});
