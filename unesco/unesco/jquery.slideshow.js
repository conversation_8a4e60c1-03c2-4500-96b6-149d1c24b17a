/**
 * jQuery slideshow 1.0 -
 *
 * Copyright (c) 2009 <PERSON> (<EMAIL>)
 *
 * Licence:
 *
 * jQuery slideshow is licensed under a Creative Commons
 * Attribution-Noncommercial-No Derivative Works 3.0 Unported
 *
 * License (http://creativecommons.org/licenses/by-nc-nd/3.0/).
 *
 * You are free:
 *
 * 	- to copy, distribute and transmit the work
 *
 * Under the following conditions:
 *
 * 	- Attribution. You must attribute the work in the manner specified by the
 *    author or licensor (but not in any way that suggests that they endorse
 *    you or your use of the work).
 *
 * 	- Noncommercial. You may not use this work for commercial purposes.
 *
 * 	- No Derivative Works. You may not alter, transform, or build upon this work.
 *
 *
 *  For any reuse or distribution, you must make clear to others the license terms
 *  of this work. The best way to do this is with a link to this web page.
 *
 *  Any of the above conditions can be waived if you get permission from the
 *  copyright holder.
 *
 *  Nothing in this license impairs or restricts the author's moral rights.
 *
 *
 * $Date: 2010-01-01 10:28:07 -0500 (Fri, 01 Jan 2010) $
 * $Rev: 1 $
 *
 **/

(function ($) {

	$.fn.slideshow = function (settings) {
		var $this = $(this);
		if ($this.length == 0) return $this;

		var instance = $this.data('slideshow');

		if (!instance) {
			instance = setup.apply(this, arguments);
			$this.data('slideshow', instance);
		}
		return instance;
	};
	
	var defaults = {
		elements: ">dl",
		box:">dt",
		img: ">img:not(.img-photo-subject)",
		others: {elements:">dd", display:true},
		mode: "gallery",		//gallery,carousel,slideshow
		slide:  {mode: "pager"},//pager,carousel
		page: undefined,		//nb per page or ajax url
		row :undefined,			//nb per row
		options: {
			slider: {enabled:false, min:5, max:10, step:1},	//display properties scroller
			switcher: false	//display mode switcher
		},
		thumbnail: undefined,
		selected: 0,
		css: {},
		link: {disable:true}
    };

	var setup = function (settings) {

		this.settings = $.extend(true, defaults, settings || {});

		$.extend(this, slideshow);

		var $this = this,
			settings = $this.settings,
			$elements = $(settings.elements, $this),
			styles = settings.styles
		;

		if (settings.mode == "slideshow") {
			
			var $clip = $('<div id="slideshow-clip"/>'),
				$slide = $("#slideshow").css({position: "relative", left:0}),
				classes = $slide.attr('class')
			;
			
			$container = $('<div id="slideshow-container" class="'+classes+'"/>').removeClass("script_hide")
			
			$slide.before($container).attr('class', "script_hide");
			
			$container.append($slide);
			
			if (settings.slide.mode == "carousel") {
				
				$container.append($clip);
				
				$clip.append($slide);
				
				$slide.removeClass("script_hide");
				
				this._first = $("#slideshow").children(":first-child");

				$container.append($('<span id="slide_next"/>').bind('click', {obj:$this}, function (event) {
					event.data.obj.slide('next');
				})).append($('<span id="slide_prev"/>').bind('click', {obj:$this}, function (event) {
					event.data.obj.slide('prev');
				}));
			}
			
			settings.options.slider.enable = false;
		}
		else {
			$this.removeClass("script_hide");
			$container = $this;
		}
		
		this._length = $elements.length;
		
		$("#slideshow").width(2);

		$elements.each(function (index, element) {

			var $element = $(element),
				$box = $(settings.box, $element);
				$img = $(settings.img, $box),
				$others = $(settings.others.elements, $element)
			;

			if (settings.mode == "slideshow") {
					
				var selected = settings.selected,
					height = settings.css.height
				;
				
				if (selected == index) {
					var $clone = $element.clone().attr('id', "slideshow-focus").removeClass("slideshow-item"),
						$box_clone = $(settings.box, $clone).removeClass("slideshow-box script_progress").css({'line-height':height, height:height}),
						$img_clone = $(settings.img, $box_clone).removeClass("slideshow-img script_hide").css({'vertical-align':"middle"}),
						$others_clone = $(settings.others.elements, $clone).removeClass("slideshow-hide")
					;
					/*$img_clone.bind('click', function (event) {
						event.preventDefault();
						event.stopPropagation();
					});*/
					$("#slideshow-clip").before($clone).removeClass("script_hide");

					if (parseInt($img_clone.css('height')) > parseInt(height))
						$img_clone.height(height);
				}
			}

			$img.bind('load', [$element, $box, $img], function (event) {
				var $item = event.data[0],
					$parent = event.data[1],
					$el = event.data[2]
				;
				$el.removeClass("script_hide");
				$parent.removeClass("script_progress");
				
				if (settings.mode == "carousel" || settings.slide.mode == "carousel") {
					$("#slideshow").width($("#slideshow").width() + $item.outerWidth(true) + 2);
				} else {
					$el.css('margin', "0 auto");
				}

				$el.unbind('load');
			});
			$img.attr('src', $img.attr('src'));

			$img.bind('click', [$element], function (event) {

				event.preventDefault();
				event.stopPropagation();

				var $el = func_focus(event.data[0], settings);

				if (settings.slide.mode == "carousel") {
					
					var height = settings.css.height,
						$clone = $element.clone().attr('id', "slideshow-focus").addClass("auto_height").removeClass("slideshow-item"),
						$box_clone = $(settings.box, $clone).removeClass("slideshow-box script_progress").css({'line-height': height, height: height}),
						$img_clone = $(settings.img, $box_clone).removeClass("slideshow-img script_hide").css({'vertical-align':"middle"}),
						$others_clone = $(settings.others.elements, $clone).removeClass("slideshow-hide")
					;
					/*$img_clone.bind('click', function (event) {
						event.preventDefault();
						event.stopPropagation();
					});*/
					$("#slideshow-focus").replaceWith($clone);

					if (parseInt($img_clone.css('height')) > parseInt(height))
						$img_clone.height(height);
					
					update_star();
					update_photo_subject();
				}
				else {
					if (settings.thumbnail) {
						
					}
					else {
						this.popup = $(document).popup({
							html: $el,
							modal: true,
							mode:"center"
						});
					}
				}
			});
		});
		
		var $options = $('<div id="slideshow-options" class="slideshow-options"/>'),
			settings_options = settings.options,
			options_slider = settings_options.slider
		;

		if (options_slider.enable) {
			this._range = options_slider.max + options_slider.min;
			$slider = $('<div id="slideshow-slider"/>').slider({
				min: options_slider.min,
				max: options_slider.max,
				step: options_slider.step,
				value: options_slider.value || options_slider.min,
				change: function (event, ui) {
					$this.resize($(this).slider("option", "value"));
				}
			});
			$this.resize(options_slider.value || options_slider.min);
			$options.append($slider);
		};
		
		if (settings_options.download) {
			var $download = $('<a class="download" href="#">Download</a>').bind('click', function () {
				var href = $("#slideshow-focus>dt"+settings.img);
				href = href[0].src.replace(/thumb/, "src").replace(/\-BIG/, "");
				$(this).attr('href', href);
				$(this).attr('download','');
				$(this).attr('target','_blank');
				
			});
			$options.append($download);
		}
/*		
		if (settings_options.toggleInfo) {
			var $viewText = $('<span class="viewText" href="#">View Text</span>').bind('click', [settings, $elements], function (event) {
				var $others = $(event.data[0].others.elements, event.data[1]);
				$this.toggleDisplay($others);
			});
			$options.prepend($viewText);
		}
*/
/*	
		var $viewSwitch = $('<span class="viewSlide" href="#">View Slideshow</span>').bind('click', [settings, this], function (event) {
			
		});
		$options.prepend($viewSwitch);
*/
		update_star();
		update_photo_subject();
		$container.prepend($options);//.disableSelection();
	}

	var slideshow = {

		version : "beta",

		resize : function (value) {

			var settings = this.settings,
				width = Math.floor(this.width() / (this._range - value)),
				height = width * settings.ratio
			;
			/*var settings = this.settings,
				width = Math.round(this.width() * value / 100),
				height = width * settings.ratio
			;
			*/
			$(".slideshow-box").each(function (index, element) {

				var $box = $(element),
					$img = $(settings.img, $box),
					$others = $(settings.others.elements, $box.parent())
				;

				$box.width(width - 6);	// moins les marges, moins bordure
				$img.height(height - 6);	// moins les marges, moins bordure
				
				$others.width(width - 6 - 10);
			});

			return this;
		},

		slide : function (direction) {
			var offset = 0,
				$clip = $("#slideshow-clip"),
				$element = $("#slideshow"),
				clip_width = $clip.width(),
				element_width = $element.width(),
				$first = this._first
			;

			while ($first.is("*") && offset < clip_width) {
				var width = $first.outerWidth(true);
				if (offset + width > clip_width)
					break;
				offset += width;
				$first = eval("$first."+direction+"()");
			}

			this._first = $first;

			if (direction == 'prev' && (Math.abs(parseInt($element.css('left'))) + offset + clip_width) >= 0) {
				offset = 0;
				this._first = $("#slideshow").children(":first-child");
				//$("#slide_prev").addClass("slide_prev_off");
			}
			else if (direction == 'next' && (Math.abs(parseInt($element.css('left'))) + offset + clip_width) >= element_width) {
				offset = "-"+(element_width - clip_width);
				this._first = $("#slideshow").children(":last-child");
				//$("#slide_next").addClass("slide_next_off");
			}
			else {
				offset = (direction == 'next' ? "-=" : "+=")+offset;
				//$("#slide_next, #slide_prev").removeClass("slide_next_off slide_prev_off");
			}
			$element.animate({
				left: offset+"px"
			}, "normal", "swing", function() {
				//alert("complete");
			});
		},
		
		toggleDisplay : function (elements) {
			if (elements.hasClass("slideshow-hide"))
				elements.removeClass("slideshow-hide");
			else
				elements.addClass("slideshow-hide");
		}
	}
	
    
	var func_focus = function (element, settings) {

		var $el = element.clone(true).attr('id', "slideshow-focus").removeClass(),
			$box = $(settings.box, $el).removeClass().css({width:"auto",background:"#000"}),//.disableSelection(),
			$img = $(settings.img, $box).removeClass().css({height:"auto", display:"block", margin:"0 auto"}),
			$others = $(settings.others.elements, $el).removeClass(),
			$prev = element.prev(".slideshow-item"),
			$next = element.next(".slideshow-item")
		;

		if ($prev.length == 1) {
			$el.append($('<span class="slideshow_prev"></span>').bind('click', {}, function (event) {
				$element = func_focus($prev, settings);
				$(document).popup().setContent($element).positionning($element);
			}));
		}
		
		if ($next.length == 1) {
			$el.append($('<span class="slideshow_next"></span>').bind('click', {}, function (event) {
				$element = func_focus($next, settings);
				$(document).popup().setContent($element).positionning($element);
			}));
		}
		
		return $el;
	}

	function update_star(){
		$('[href="#star"]').unbind('click');
		$('[href="#star"]').click(function(){
			var note = $(this).attr('data-note');
			var ref  = $(this).attr('data-ref');
			var scope = $(this).closest('#diaporama');
			$.ajax({
				url: image_star_url ,
				data: {note:note, ref:ref},
				success:function(){
					for(var i=1 ; i<=5 ; i++) {
						var color = i<=note ? 'yellow' : 'DimGray';
						scope.find('[href="#star"][data-ref="'+ref+'"][data-note="'+i+'"]').css('color', color);
					}
				}
			});
		});
	}

	function update_photo_subject() {
		$('[href="#photoSubject"]').each(function() {
			var img = $(this).children('img').first();
			var imgSrc = img.attr('src').replace('-checked.png', '.png');
			if ($(this).attr('data-checked') == 1) {
				imgSrc = imgSrc.replace('.png', '-checked.png');
			}
			img.attr('src', imgSrc);
		});
		$('.img-photo-subject').off('click');
		$('.img-photo-subject').off('load');
		$('[href="#photoSubject"]').off('click');
		$('[href="#photoSubject"]').on('click', function(e) {
			e.preventDefault();
			var link    = $(this);
			var subject = link.attr('data-subject');
			var ref     = link.attr('data-ref');
			$.ajax({
				url: image_subject_url ,
				data: { subject: subject, ref: ref },
				dataType: 'json',
				success: function(data) {
					if (data.success) {
						var img = link.children('img').first();
						var imgSrc = img.attr('src');
						if (imgSrc.indexOf('-checked.png') >= 0) {
							imgSrc = imgSrc.replace('-checked.png', '.png');
							$('.subject-link-'+subject+'-'+ref).attr('data-checked', '0');
						} else {
							imgSrc = imgSrc.replace('.png', '-checked.png');
							$('.subject-link-'+subject+'-'+ref).attr('data-checked', '1');
						}
						img.attr('src', imgSrc);
					}
				}
			});
		});
	}
})(jQuery);
