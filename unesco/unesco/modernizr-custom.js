/*! modernizr 3.6.0 (Custom Build) | MIT *
 * https://modernizr.com/download/?-applicationcache-audio-backgroundsize-borderimage-borderradius-boxshadow-canvas-canvastext-cssanimations-csscolumns-cssgradients-cssreflections-csstransforms-csstransforms3d-csstransitions-flexbox-fontface-generatedcontent-geolocation-hashchange-history-hsla-indexeddb-inlinesvg-input-inputtypes-localstorage-multiplebgs-opacity-postmessage-rgba-sessionstorage-smil-svg-svgclippaths-textshadow-video-webgl-websockets-websqldatabase-webworkers-addtest-domprefixes-hasevent-prefixed-prefixes-setclasses-testallprops-testprop-teststyles !*/
!function(e,t,n){function r(e,t){return typeof e===t}function o(){var e,t,n,o,a,s,i;for(var l in T)if(T.hasOwnProperty(l)){if(e=[],t=T[l],t.name&&(e.push(t.name.toLowerCase()),t.options&&t.options.aliases&&t.options.aliases.length))for(n=0;n<t.options.aliases.length;n++)e.push(t.options.aliases[n].toLowerCase());for(o=r(t.fn,"function")?t.fn():t.fn,a=0;a<e.length;a++)s=e[a],i=s.split("."),1===i.length?Modernizr[i[0]]=o:(!Modernizr[i[0]]||Modernizr[i[0]]instanceof Boolean||(Modernizr[i[0]]=new Boolean(Modernizr[i[0]])),Modernizr[i[0]][i[1]]=o),_.push((o?"":"no-")+i.join("-"))}}function a(e){return e.replace(/([a-z])-([a-z])/g,function(e,t,n){return t+n.toUpperCase()}).replace(/^-/,"")}function s(e,t){return!!~(""+e).indexOf(t)}function i(e){var t=P.className,n=Modernizr._config.classPrefix||"";if(N&&(t=t.baseVal),Modernizr._config.enableJSClass){var r=new RegExp("(^|\\s)"+n+"no-js(\\s|$)");t=t.replace(r,"$1"+n+"js$2")}Modernizr._config.enableClasses&&(t+=" "+n+e.join(" "+n),N?P.className.baseVal=t:P.className=t)}function l(e,t){if("object"==typeof e)for(var n in e)R(e,n)&&l(n,e[n]);else{e=e.toLowerCase();var r=e.split("."),o=Modernizr[r[0]];if(2==r.length&&(o=o[r[1]]),"undefined"!=typeof o)return Modernizr;t="function"==typeof t?t():t,1==r.length?Modernizr[r[0]]=t:(!Modernizr[r[0]]||Modernizr[r[0]]instanceof Boolean||(Modernizr[r[0]]=new Boolean(Modernizr[r[0]])),Modernizr[r[0]][r[1]]=t),i([(t&&0!=t?"":"no-")+r.join("-")]),Modernizr._trigger(e,t)}return Modernizr}function d(){return"function"!=typeof t.createElement?t.createElement(arguments[0]):N?t.createElementNS.call(t,"http://www.w3.org/2000/svg",arguments[0]):t.createElement.apply(t,arguments)}function c(){var e=t.body;return e||(e=d(N?"svg":"body"),e.fake=!0),e}function u(e,n,r,o){var a,s,i,l,u="modernizr",f=d("div"),p=c();if(parseInt(r,10))for(;r--;)i=d("div"),i.id=o?o[r]:u+(r+1),f.appendChild(i);return a=d("style"),a.type="text/css",a.id="s"+u,(p.fake?p:f).appendChild(a),p.appendChild(f),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(t.createTextNode(e)),f.id=u,p.fake&&(p.style.background="",p.style.overflow="hidden",l=P.style.overflow,P.style.overflow="hidden",P.appendChild(p)),s=n(f,e),p.fake?(p.parentNode.removeChild(p),P.style.overflow=l,P.offsetHeight):f.parentNode.removeChild(f),!!s}function f(e,t){return function(){return e.apply(t,arguments)}}function p(e,t,n){var o;for(var a in e)if(e[a]in t)return n===!1?e[a]:(o=t[e[a]],r(o,"function")?f(o,n||t):o);return!1}function g(e){return e.replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()}).replace(/^ms-/,"-ms-")}function m(t,n,r){var o;if("getComputedStyle"in e){o=getComputedStyle.call(e,t,n);var a=e.console;if(null!==o)r&&(o=o.getPropertyValue(r));else if(a){var s=a.error?"error":"log";a[s].call(a,"getComputedStyle returning null, its possible modernizr test results are inaccurate")}}else o=!n&&t.currentStyle&&t.currentStyle[r];return o}function v(t,r){var o=t.length;if("CSS"in e&&"supports"in e.CSS){for(;o--;)if(e.CSS.supports(g(t[o]),r))return!0;return!1}if("CSSSupportsRule"in e){for(var a=[];o--;)a.push("("+g(t[o])+":"+r+")");return a=a.join(" or "),u("@supports ("+a+") { #modernizr { position: absolute; } }",function(e){return"absolute"==m(e,null,"position")})}return n}function h(e,t,o,i){function l(){u&&(delete D.style,delete D.modElem)}if(i=r(i,"undefined")?!1:i,!r(o,"undefined")){var c=v(e,o);if(!r(c,"undefined"))return c}for(var u,f,p,g,m,h=["modernizr","tspan","samp"];!D.style&&h.length;)u=!0,D.modElem=d(h.shift()),D.style=D.modElem.style;for(p=e.length,f=0;p>f;f++)if(g=e[f],m=D.style[g],s(g,"-")&&(g=a(g)),D.style[g]!==n){if(i||r(o,"undefined"))return l(),"pfx"==t?g:!0;try{D.style[g]=o}catch(y){}if(D.style[g]!=m)return l(),"pfx"==t?g:!0}return l(),!1}function y(e,t,n,o,a){var s=e.charAt(0).toUpperCase()+e.slice(1),i=(e+" "+G.join(s+" ")+s).split(" ");return r(t,"string")||r(t,"undefined")?h(i,t,o,a):(i=(e+" "+A.join(s+" ")+s).split(" "),p(i,t,n))}function b(e,t,r){return y(e,n,n,t,r)}function x(e,t){var n=e.deleteDatabase(t);n.onsuccess=function(){l("indexeddb.deletedatabase",!0)},n.onerror=function(){l("indexeddb.deletedatabase",!1)}}var T=[],w={_version:"3.6.0",_config:{classPrefix:"",enableClasses:!0,enableJSClass:!0,usePrefixes:!0},_q:[],on:function(e,t){var n=this;setTimeout(function(){t(n[e])},0)},addTest:function(e,t,n){T.push({name:e,fn:t,options:n})},addAsyncTest:function(e){T.push({name:null,fn:e})}},Modernizr=function(){};Modernizr.prototype=w,Modernizr=new Modernizr,Modernizr.addTest("svg",!!t.createElementNS&&!!t.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect),Modernizr.addTest("websqldatabase","openDatabase"in e),Modernizr.addTest("postmessage","postMessage"in e),Modernizr.addTest("sessionstorage",function(){var e="modernizr";try{return sessionStorage.setItem(e,e),sessionStorage.removeItem(e),!0}catch(t){return!1}}),Modernizr.addTest("history",function(){var t=navigator.userAgent;return-1===t.indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone")||"file:"===location.protocol?e.history&&"pushState"in e.history:!1});var S=w._config.usePrefixes?" -webkit- -moz- -o- -ms- ".split(" "):["",""];w._prefixes=S,Modernizr.addTest("webworkers","Worker"in e),Modernizr.addTest("localstorage",function(){var e="modernizr";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(t){return!1}});var C=!1;try{C="WebSocket"in e&&2===e.WebSocket.CLOSING}catch(k){}Modernizr.addTest("websockets",C);var _=[];Modernizr.addTest("geolocation","geolocation"in navigator),Modernizr.addTest("applicationcache","applicationCache"in e);var P=t.documentElement,E="Moz O ms Webkit",A=w._config.usePrefixes?E.toLowerCase().split(" "):[];w._domPrefixes=A;var R;!function(){var e={}.hasOwnProperty;R=r(e,"undefined")||r(e.call,"undefined")?function(e,t){return t in e&&r(e.constructor.prototype[t],"undefined")}:function(t,n){return e.call(t,n)}}();var z="CSS"in e&&"supports"in e.CSS,O="supportsCSS"in e;Modernizr.addTest("supports",z||O);var $={}.toString;Modernizr.addTest("svgclippaths",function(){return!!t.createElementNS&&/SVGClipPath/.test($.call(t.createElementNS("http://www.w3.org/2000/svg","clipPath")))}),Modernizr.addTest("smil",function(){return!!t.createElementNS&&/SVGAnimate/.test($.call(t.createElementNS("http://www.w3.org/2000/svg","animate")))});var N="svg"===P.nodeName.toLowerCase();w._l={},w.on=function(e,t){this._l[e]||(this._l[e]=[]),this._l[e].push(t),Modernizr.hasOwnProperty(e)&&setTimeout(function(){Modernizr._trigger(e,Modernizr[e])},0)},w._trigger=function(e,t){if(this._l[e]){var n=this._l[e];setTimeout(function(){var e,r;for(e=0;e<n.length;e++)(r=n[e])(t)},0),delete this._l[e]}},Modernizr._q.push(function(){w.addTest=l});var L=function(){function e(e,t){var o;return e?(t&&"string"!=typeof t||(t=d(t||"div")),e="on"+e,o=e in t,!o&&r&&(t.setAttribute||(t=d("div")),t.setAttribute(e,""),o="function"==typeof t[e],t[e]!==n&&(t[e]=n),t.removeAttribute(e)),o):!1}var r=!("onblur"in t.documentElement);return e}();w.hasEvent=L,Modernizr.addTest("hashchange",function(){return L("hashchange",e)===!1?!1:t.documentMode===n||t.documentMode>7}),Modernizr.addTest("audio",function(){var e=d("audio"),t=!1;try{t=!!e.canPlayType,t&&(t=new Boolean(t),t.ogg=e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),t.mp3=e.canPlayType('audio/mpeg; codecs="mp3"').replace(/^no$/,""),t.opus=e.canPlayType('audio/ogg; codecs="opus"')||e.canPlayType('audio/webm; codecs="opus"').replace(/^no$/,""),t.wav=e.canPlayType('audio/wav; codecs="1"').replace(/^no$/,""),t.m4a=(e.canPlayType("audio/x-m4a;")||e.canPlayType("audio/aac;")).replace(/^no$/,""))}catch(n){}return t}),Modernizr.addTest("cssgradients",function(){for(var e,t="background-image:",n="gradient(linear,left top,right bottom,from(#9f9),to(white));",r="",o=0,a=S.length-1;a>o;o++)e=0===o?"to ":"",r+=t+S[o]+"linear-gradient("+e+"left top, #9f9, white);";Modernizr._config.usePrefixes&&(r+=t+"-webkit-"+n);var s=d("a"),i=s.style;return i.cssText=r,(""+i.backgroundImage).indexOf("gradient")>-1}),Modernizr.addTest("hsla",function(){var e=d("a").style;return e.cssText="background-color:hsla(120,40%,100%,.5)",s(e.backgroundColor,"rgba")||s(e.backgroundColor,"hsla")}),Modernizr.addTest("inlinesvg",function(){var e=d("div");return e.innerHTML="<svg/>","http://www.w3.org/2000/svg"==("undefined"!=typeof SVGRect&&e.firstChild&&e.firstChild.namespaceURI)}),Modernizr.addTest("opacity",function(){var e=d("a").style;return e.cssText=S.join("opacity:.55;"),/^0.55$/.test(e.opacity)}),Modernizr.addTest("video",function(){var e=d("video"),t=!1;try{t=!!e.canPlayType,t&&(t=new Boolean(t),t.ogg=e.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),t.h264=e.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),t.webm=e.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,""),t.vp9=e.canPlayType('video/webm; codecs="vp9"').replace(/^no$/,""),t.hls=e.canPlayType('application/x-mpegURL; codecs="avc1.42E01E"').replace(/^no$/,""))}catch(n){}return t}),Modernizr.addTest("webgl",function(){var t=d("canvas"),n="probablySupportsContext"in t?"probablySupportsContext":"supportsContext";return n in t?t[n]("webgl")||t[n]("experimental-webgl"):"WebGLRenderingContext"in e}),Modernizr.addTest("multiplebgs",function(){var e=d("a").style;return e.cssText="background:url(https://),url(https://),red url(https://)",/(url\s*\(.*?){3}/.test(e.background)}),Modernizr.addTest("canvas",function(){var e=d("canvas");return!(!e.getContext||!e.getContext("2d"))}),Modernizr.addTest("canvastext",function(){return Modernizr.canvas===!1?!1:"function"==typeof d("canvas").getContext("2d").fillText}),Modernizr.addTest("rgba",function(){var e=d("a").style;return e.cssText="background-color:rgba(150,255,150,.5)",(""+e.backgroundColor).indexOf("rgba")>-1});var B=d("input"),I="autocomplete autofocus list placeholder max min multiple pattern required step".split(" "),V={};Modernizr.input=function(t){for(var n=0,r=t.length;r>n;n++)V[t[n]]=!!(t[n]in B);return V.list&&(V.list=!(!d("datalist")||!e.HTMLDataListElement)),V}(I);var j="search tel url email datetime date month week time datetime-local number range color".split(" "),W={};Modernizr.inputtypes=function(e){for(var r,o,a,s=e.length,i="1)",l=0;s>l;l++)B.setAttribute("type",r=e[l]),a="text"!==B.type&&"style"in B,a&&(B.value=i,B.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(r)&&B.style.WebkitAppearance!==n?(P.appendChild(B),o=t.defaultView,a=o.getComputedStyle&&"textfield"!==o.getComputedStyle(B,null).WebkitAppearance&&0!==B.offsetHeight,P.removeChild(B)):/^(search|tel)$/.test(r)||(a=/^(url|email)$/.test(r)?B.checkValidity&&B.checkValidity()===!1:B.value!=i)),W[e[l]]=!!a;return W}(j);var M=w.testStyles=u,q=function(){var e=navigator.userAgent,t=e.match(/w(eb)?osbrowser/gi),n=e.match(/windows phone/gi)&&e.match(/iemobile\/([0-9])+/gi)&&parseFloat(RegExp.$1)>=9;return t||n}();q?Modernizr.addTest("fontface",!1):M('@font-face {font-family:"font";src:url("https://")}',function(e,n){var r=t.getElementById("smodernizr"),o=r.sheet||r.styleSheet,a=o?o.cssRules&&o.cssRules[0]?o.cssRules[0].cssText:o.cssText||"":"",s=/src/i.test(a)&&0===a.indexOf(n.split(" ")[0]);Modernizr.addTest("fontface",s)}),M('#modernizr{font:0/0 a}#modernizr:after{content:":)";visibility:hidden;font:7px/1 a}',function(e){Modernizr.addTest("generatedcontent",e.offsetHeight>=6)});var G=w._config.usePrefixes?E.split(" "):[];w._cssomPrefixes=G;var U=function(t){var r,o=S.length,a=e.CSSRule;if("undefined"==typeof a)return n;if(!t)return!1;if(t=t.replace(/^@/,""),r=t.replace(/-/g,"_").toUpperCase()+"_RULE",r in a)return"@"+t;for(var s=0;o>s;s++){var i=S[s],l=i.toUpperCase()+"_"+r;if(l in a)return"@-"+i.toLowerCase()+"-"+t}return!1};w.atRule=U;var H={elem:d("modernizr")};Modernizr._q.push(function(){delete H.elem});var D={style:H.elem.style};Modernizr._q.unshift(function(){delete D.style});var F=w.testProp=function(e,t,r){return h([e],n,t,r)};Modernizr.addTest("textshadow",F("textShadow","1px 1px")),w.testAllProps=y,w.testAllProps=b,Modernizr.addTest("cssreflections",b("boxReflect","above",!0)),Modernizr.addTest("flexbox",b("flexBasis","1px",!0)),Modernizr.addTest("boxshadow",b("boxShadow","1px 1px",!0)),Modernizr.addTest("csstransforms3d",function(){return!!b("perspective","1px",!0)}),Modernizr.addTest("backgroundsize",b("backgroundSize","100%",!0)),Modernizr.addTest("csstransforms",function(){return-1===navigator.userAgent.indexOf("Android 2.")&&b("transform","scale(1)",!0)}),function(){Modernizr.addTest("csscolumns",function(){var e=!1,t=b("columnCount");try{e=!!t,e&&(e=new Boolean(e))}catch(n){}return e});for(var e,t,n=["Width","Span","Fill","Gap","Rule","RuleColor","RuleStyle","RuleWidth","BreakBefore","BreakAfter","BreakInside"],r=0;r<n.length;r++)e=n[r].toLowerCase(),t=b("column"+n[r]),("breakbefore"===e||"breakafter"===e||"breakinside"==e)&&(t=t||b(n[r])),Modernizr.addTest("csscolumns."+e,t)}(),Modernizr.addTest("csstransitions",b("transition","all",!0)),Modernizr.addTest("borderradius",b("borderRadius","0px",!0)),Modernizr.addTest("cssanimations",b("animationName","a",!0)),Modernizr.addTest("borderimage",b("borderImage","url() 1",!0));var J=w.prefixed=function(e,t,n){return 0===e.indexOf("@")?U(e):(-1!=e.indexOf("-")&&(e=a(e)),t?y(e,t,n):y(e,"pfx"))};Modernizr.addAsyncTest(function(){var t;try{t=J("indexedDB",e)}catch(n){}if(t){var r="modernizr-"+Math.random(),o=t.open(r);o.onerror=function(){o.error&&"InvalidStateError"===o.error.name?l("indexeddb",!1):(l("indexeddb",!0),x(t,r))},o.onsuccess=function(){l("indexeddb",!0),x(t,r)}}else l("indexeddb",!1)}),o(),i(_),delete w.addTest,delete w.addAsyncTest;for(var Z=0;Z<Modernizr._q.length;Z++)Modernizr._q[Z]();e.Modernizr=Modernizr}(window,document);