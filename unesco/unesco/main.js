/*!
 * Main user defined javascript
 * https://ich.unesco.org
 *
 * Copyright 2003, 2014 UNESCO
 *
 * Date: 2014-01-01
 */
let desktopWidth = 992

$(document).ready(function() {
    /* Todo: a ameliorer pour prendre en compe le chargement des images qui bougent le viewport.
    var viewport_height = $(window).height();

    if ($('#body').height() < viewport_height) {
        $("#page-main").innerHeight(viewport_height - ($("#header").outerHeight(true) + $("#footer").outerHeight(true)) - 71);
    }*/

//	$("#connect-link").click(function (event) {
//	    event.stopPropagation();
//	    $("#form-login").click(function (event) {event.stopPropagation();}).animate({top: "+=100px", opacity: "toggle"}, 500, function () {
//	        $(document).one("click", function (event) {
//                console.log('youpii');
//	            $("#form-login").animate({top: "-=100px", opacity: "toggle"}, 500);
//	        });
//	    });
//	});

	$(".login-link").click(function (event) {
	    event.stopPropagation();
	    $("#login-menu").click(function (event) {event.stopPropagation();}).animate({opacity: "toggle"}, 500, function () {
	        $(document).one("click", function (event) {
	            $("#login-menu").animate({opacity: "toggle"}, 500);
	        });
	    });
	});

    //TODO utiliser data-msg avec le message voulu par Hugues
    if ($('#login-form-test').length) {
        $("#login-form-test").submit(function(event) {
            var identifiant = $("#login_id").val();
            var pattern = /^([a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+(\.[a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+)*|"((([ \t]*\r\n)?[ \t]+)?([\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*(([ \t]*\r\n)?[ \t]+)?")@(([a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.)+([a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.?$/i;
            if (pattern.test(identifiant)) {
                alert("Vous devez utiliser votre identifiant et non votre adresse e-mail");
                event.preventDefault();
            }
        });
    }

    menu.region = $('.sub-menu-list', '.sub-menu-countries').filter(':first').show();

    $.fn.hasOverflow = function() {
        var $this = $(this),
            $children = $this.children()
        width = $this.width(), // height = $this.height(),
            maxWidth = 0 //, maxHeight = 0
        ;

        $children.each(function() {
            maxWidth += $(this).outerWidth(true);
            //maxHeight += $(this).outerHeight(true);
        });

        return (maxWidth > width); // || maxHeight > height);
    };

    $('.ellipsis').each(function() {
        if (!$(this).hasOverflow()) $(this).removeClass('ellipsis');
    });

    var current_tab = [];

    $.each($("#event-tabs .tab"), function(i, element) {
        if (i === 0) current_tab = [$(element), 0];
        $(element).click(function() {
            var $this = $(this);
            if (!$this.hasClass('selected')) {
                $this.addClass('selected');
                current_tab[0].removeClass('selected');
                $nextAll = $("#event-tabs").nextAll();
                $nextAll.eq(current_tab[1]).addClass('hide')
                $nextAll.eq(i).removeClass('hide');
                current_tab = [$this, i];
            }
        });
    });

    $("#sizedown").click(function() {
        var $element = $("#page-main");
        if ($element.css('font-size') === '10px')
            $(this).addClass('disabled');
        else {
            $element.css('font-size', "-=1");
            $("#sizeup").removeClass('disabled');
        }
    });

    $("#sizeup").click(function() {
        var $element = $("#page-main");
        if ($element.css('font-size') === '20px')
            $(this).addClass('disabled');
        else {
            $element.css('font-size', "+=1");
            $("#sizedown").removeClass('disabled');
        }
    });

    $('#print').click(function() {
        window.print();
        return false;
    });
});

// define select & autocomplete handlers
function set_country_autocomplete() {
    $('.combobox-2[name="country-page"]').on('change', function() {
        var optionSelected = $("option:selected", this);
        window.location.href = this.value;
    });

    $('.country-search select option').on('click', function() {
        window.location.href = $(this).attr('data-value');
    });
    if (typeof country_list !== 'undefined' && country_list.length != 0) {
        $('.country-search').autocomplete({
            source: country_list,
            select: function(a, b) {
                window.location.href = b.item.data;
            }
        });
        $.ui.autocomplete.filter = function(array, term) {
            var matcher = new RegExp("^" + $.ui.autocomplete.escapeRegex(term), "i");
            return $.grep(array, function(value) {
                return matcher.test(value.label || value.value || value);
            });
        };
    }

}

function waiterShow() {
    if ($('#genWaiter').length) {
        $('#genWaiter').show();
    }
}

function waiterHide() {
    if ($('#genWaiter').length) {
        $('#genWaiter').hide();
    }
}

/*@
 * Menu
 */
const MOBILE_MAX_WIDTH = 770;
var windowWidth = window.innerWidth ? window.innerWidth : $(window).width();
var is_mobile = windowWidth < MOBILE_MAX_WIDTH;

var menu = {
    visible: false,
    selected: $("#dummy"),
    label: $("#dummy"),
    region: $("#dummy"),
    timeout: null,
    _timeout: function (_this) {
        menu.label.focus();
        menu.selected.removeClass('visible');
        menu.selected = $('+div', _this).addClass('visible');
        if (is_mobile === true) {
            menu.visible = true;
        }
    },
    _func: function(event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        if (menu.visible) {
            if (event.type == "mouseenter") { // || event.type == "focusin") {
                _this = this;
                menu.timeout = window.setTimeout(function() { menu._timeout(_this); }, 200);
            }
            if (event.type == "click") {
                menu.visible = false;
                menu.selected.removeClass('visible');
            //    menu.label.focusOut();
            }
        } else {
            if (event.type == "click") {
                menu.visible = true;
                menu.selected = $('+div', this).addClass('visible');
                //menu.label.focusOut();
            }
            $("#main, #footer, .wrapper_header").one('click', function(event) {
                menu.visible = false;
                menu.selected.removeClass('visible');
                menu.label.removeClass('bg-color-blue_2');
            });
        }

        if (is_mobile === true && menu.label[0] && $(this).text() != menu.label[0].text)
            menu._timeout(this);

        if (is_mobile) {
            set_country_autocomplete();
        }
        
        return false;
    }
};

$(document).ready(function() {
    $(document).on('click', 'a.menu-label', menu._func);
    if (is_mobile === false) {
    //    $(document).on('mouseenter', 'a.menu-label', menu._func);
        //$(document).on('focus', 'a.menu-label', menu._func);
        $(document).on('mouseout', 'a.menu-label', function() {
            window.clearTimeout(menu.timeout)
        });
    }

    $(document).on('mouseenter', 'a.sub-menu-region', function(event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        if (event.type == "mouseenter") {
            menu.region.hide();
            menu.region = $(this).next().show();
        }
    });
});

/*@
 * Links
 */

var link = {
    timer: null
}
$(document).ready(function() {
    $(document).on('mouseenter', 'a.country', function(event) {
        $this = $(this);
        link.timer = setTimeout(function() { $this.addClass('underline'); }, 300);
    });

    $(document).on('mouseout', 'a.country', function(event) {
        clearTimeout(link.timer);
        $(this).removeClass('underline');
    });
});

/*@
 * Fancybox - Default config
 * Init fancybox openings
 */
var _fbConfig = {
    padding: 5,
    autoSize: true,
    fitToView: true,
    tpl: {
        closeBtn: '<a class="fancybox-item fancybox-close" href="javascript:;">' + _closeTxt + ' <span></span></a>',
        next: '<a title="' + _nextTxt + '" class="fancybox-nav fancybox-next" href="javascript:;"><span></span></a>',
        prev: '<a title="' + _prevTxt + '" class="fancybox-nav fancybox-prev" href="javascript:;"><span></span></a>'
    },
    helpers: {
        title: null,
    },
    beforeLoad: function() {
        var el = $(this.element);
        var title = el.data('title');
        if (title) {
            this.title = title;
        }
    },
    beforeClose: function() {
        if (typeof windowLock == 'undefined')
            windowLock = false;

        if (windowLock)
            if (confirm($label['confirm_without_saving']))
                return true;
            else return false;

        return true;
    }
};

if ($(window).width() > 650) {
    _fbConfig.margin = 100;
}

// Image popup config
var popupConfig = {
    padding: 10,
    width: 800,
    topRatio: 0.2,
    tpl: {
        closeBtn: '<a class="fancybox-item fancybox-close" href="javascript:;">' + _closeTxt + ' <span></span></a>',
        next: '<a title="' + _nextTxt + '" class="fancybox-nav fancybox-next" href="javascript:;"><span></span></a>',
        prev: '<a title="' + _prevTxt + '" class="fancybox-nav fancybox-prev" href="javascript:;"><span></span></a>'
    },
};

if ($(window).width() > 650) {
    popupConfig.margin = 100;
}

$(document).ready(function() {
    var config = _fbConfig;
    $(".fancybox").each(function() {

        if (typeof $(this).attr('data-fancybox-margin') !== 'undefined' && $(this).attr('data-fancybox-margin') !== false) {
            config.margin = $(this).attr('data-fancybox-margin').split(',').map(function(b) {
                return parseInt(b);
            });
        }

        if (typeof $(this).attr('data-fancybox-width') !== 'undefined' && $(this).attr('data-fancybox-width') !== false) {
            config.width = $(this).attr('data-fancybox-width');
        }

        if (typeof $(this).attr('data-fancybox-height') !== 'undefined' && $(this).attr('data-fancybox-height') !== false) {
            config.height = $(this).attr('data-fancybox-height');
        }
        
    });
    $(".fancybox").fancybox(config);
    $(".popupFancyBox").fancybox(popupConfig);
});


/*
 *	Management of custom Gallery
 */
myGallery_click_event = function() {
    var html = $(this).find("dt").context.innerHTML;
    $(".myScreen").html(html);
    return false;
};
if ($(".myGallery")) {
    $(".myGallery dl a").click(myGallery_click_event);
    $(".myGallery dl a").first().click();
}

/*@
 * Delegate part
 */

$(document).delegate('a.target', 'click', function(event) {
    event.preventDefault();
    //    event.stopPropagation();
    //    event.stopImmediatePropagation();
    //    return false;
});

//country select
function initialiseSelectCountry() {
    $(".listOfCriteria").empty();
    $(".mySelectCountryID option:selected").each(function() {
        var vall = $(this).attr('value');
        var textt = $(this).text();
        $("<p  class='criteria' country='" + vall + "'>" + textt + "<span country='" + vall + "' class='delete deleteMySelectCountryID'></span></p>").appendTo(".listOfCriteria");
    });
}

$(document).ready(function() {
    $(document).on('change', '.countryMultiSearch', function(event) {
        var thisSelect = $(this);
        var idCountryIdSelected = $(this).val();
        $(".mySelectCountryID option").each(function() {
            if (idCountryIdSelected == $(this).attr('value')) {
                if ($(this).is(':selected')) {
                    //    $(this).removeAttr("selected", "");
                } else {
                    $(this).attr("selected", "selected");
                }
            }
        });
        initialiseSelectCountry();
    });
});

$(document).ready(function() {
    $(document).on('click', 'span.deleteMySelectCountryID', function(event) {
        var elementDeleteMySelectCountryID = $(this)
        var idDeleteCountry = $(this).attr('country');

        $(".mySelectCountryID option").each(function() {
            if ($(this).attr('value') == idDeleteCountry) {
                $(this).removeAttr("selected", "");
            }
        });
        initialiseSelectCountry();
        elementDeleteMySelectCountryID.parent().remove();
    });
});

$(document).ready(function() {
    initialiseSelectCountry();
});

//keyword
function initialiseSelectkeywordCountry() {
    $(".listOfCriteriakeyword").empty();
    $(".mySelectkeywordCountry option:selected").each(function() {
        var vall = $(this).attr('value');
        var textt = $(this).text();
        $("<p  class='criteria' country='" + vall + "'>" + textt + "<span country='" + vall + "' class='delete deleteMySelectkeywordCountry'></span></p>").appendTo(".listOfCriteriakeyword");
    });
}

$(document).ready(function() {
    $(document).on('change', '.keywordCountryMultiSearch', function(event) {
        var thisSelect = $(this);
        var idCountryIdSelected = $(this).val();

        $( ".mySelectkeywordCountry option" ).each(function( ) {
            if(idCountryIdSelected == $( this ).attr('value') ){
                 if($(this).is(':selected')){
                    $(this).removeAttr("selected", "");
                } else {
                    $(this).attr("selected", "selected");
                }
            }
        });
        initialiseSelectkeywordCountry();
    });

    $(document).on('click', 'span.deleteMySelectkeywordCountry', function(event) {
        var elementDeleteMySelectkeywordCountry = $(this)
        var idDeleteCountry = $(this).attr('country');

        $(".mySelectkeywordCountry option").each(function() {
            if ($(this).attr('value') == idDeleteCountry) {
                $(this).removeAttr("selected");
            }
        });
        initialiseSelectkeywordCountry();
        elementDeleteMySelectkeywordCountry.parent().remove();
    });
});

$(document).ready(function() {
    initialiseSelectkeywordCountry();
});

//year
function initialiseSelectYearCountry() {
    $(".listOfCriteriayear").empty();
    $(".mySelectyearCountry option:selected").each(function() {
        var vall = $(this).attr('value');
        var textt = $(this).text();
        if (vall != '0') {
            $("<p  class='criteria' country='" + vall + "'>" + textt + "<span country='" + vall + "' class='delete deleteMySelectyearCountry'></span></p>").appendTo(".listOfCriteriayear");
        }
    });
}

var timeoutGeneratedForm = null;
var pageAutoSave = null;
var autoSaveLocks = {};
$(document).ready(function() {
    $(document).on('change', '.yearCountrySelect', function(event) {
        var thisSelect = $(this);
        var idCountryIdSelected = $(this).val();
        if (idCountryIdSelected != '0') {
            $(".mySelectyearCountry option").each(function() {
                $(this).removeAttr("selected", "");
                if (idCountryIdSelected == $(this).attr('value')) {
                    $(this).attr("selected", "selected");
                }
            });
            initialiseSelectYearCountry();
        }
    });

    $(document).on('click', 'span.deleteMySelectyearCountry', function(event) {
        var elementDeleteMySelectCountryID = $(this)
        var idDeleteCountry = $(this).attr('country');

        $(".mySelectyearCountry option").each(function() {
            if ($(this).attr('value') == idDeleteCountry) {
                $(this).removeAttr("selected");
            }
        });
        initialiseSelectCountry();
        elementDeleteMySelectCountryID.parent().remove();
    });

    /**-------------------------------------------------------------------*
     *---------------------------Periodic Reports-------------------------*
     *--------------------------------------------------------------------*/
    var lg = 'fr';
    if (typeof(reportLang) != 'undefined' && reportLang) {
        lg = reportLang;
    }
    if ($.datepicker)
        $.datepicker.setDefaults($.datepicker.regional[lg]);
    if ($(".date-picker").length > 0) {
        $(".date-picker").datepicker({ minDate: 0, dateFormat: "dd-mm-yy" });
    }
    if ($(".datepickerdetail").length > 0) {
        var dateTmp = new Date();
        $(".datepickerdetail").datepicker({ minDate: new Date(1950, 0, 1), dateFormat: "dd-mm-yy", changeMonth: true, changeYear: true, yearRange: "1950:" + dateTmp.getFullYear(), defaultDate: dateTmp });
    }

    /**-------------------------------------------------------------------*
     *---------------------------Periodic Reports-------------------------*
     *--------------------------------------------------------------------*/

    // Test if we are on the Periodic Reports page
    // by testing if the element exist
    var $form = $('form.autofilter');
    if ($form.length) {
        $('form.autofilter .select').each(function(index, el) {
            $(el).children('select').change(function(event) {
                if (!$(el).children('select').hasClass('act-filter')) {
                    $resetButton = $("<p class='criteria'><span class='delete'></span></p>");
                    $(el).after($resetButton);
                    $(el).children('select').addClass('act-filter');
                }
                // Listen to the click event
                $resetButton.click({ detail: { slt: el, btn: $resetButton } }, addRemoveFilter);
                $form.submit();
            });
            if ($(el).children('select').val()) {
                // Add class if filter selected
                $(el).children('select').addClass('act-filter');
                // Add reset select button
                $resetButton = $("<p class='criteria'><span class='delete'></span></p>");
                $(el).after($resetButton);
                // Listen to the click event
                $resetButton.click({ detail: { slt: el, btn: $resetButton } }, addRemoveFilter);
            }
        });
    }

    function addRemoveFilter(event) {
        var $form = $('form.autofilter');
        var slt = event.data.detail.slt;
        var btn = event.data.detail.btn[0];
        $select = $(slt).children('select');
        $select.children('[selected]').removeAttr('selected');
        $select.children('option').first().attr('selected', 'true');
        $select.trigger('change');
        $(btn).remove();
        $select.removeClass('act-filter');
        $form.submit();
    }

    /**------------------------------------------------------------------*
     *---------------------------Forms generated-------------------------*
     *-------------------------------------------------------------------*/
    // Get the form to validate
    var $pendingForm = $('.generated-form');

    $('.generated-form .validate').each(function() {
        if (!$(this).is('textarea')) {
            var minLength = $(this).attr('minlength');
            if (typeof minLength == 'undefined') {
                minLength = 0;
            } else {
                minLength = parseInt(minLength);
            }
            var minWords = $(this).data('minword');
            if (typeof minWords == 'undefined') {
                minWords = 0;
            } else {
                minWords = parseInt(minWords);
            }

            if (minLength > 0 || minWords > 0) {
                var currentLabel = $(this).siblings('label').first();
                currentLabel.html(currentLabel.html() + '<em>*</em>');
            } else {
                $(this).removeClass('validate');
            }
        }
    });

    if ($('td#form-older-periodicreport .older-periodicreport').length == 0) {
        $('.generated-form .form-history').hide();
    }

    $('#saveFormNotes').on('click', function() {
        var link = $(this).data('link');
        $('#notesMessage').html('');
        $('#notesMessage').hide();
        $.ajax({
            url: link,
            type: 'POST',
            data: { 'notes': $('#textareaNotes').val() },
            dataType: 'json'
        }).done(function(data) {
            var msg = '<p style="padding:10px" class="bg-success text-success">' + data.message + '</p>';
            $('#notesMessage').html(msg);
            $('#notesMessage').show();
        });
    });

    // By default display only the first tab
    $('div.clear').each(function(index) {
        var children = $(this).children('.formPage');
        if (children.length != 0) {
            var hasVisible = false;
            $.each(children, function(index) {
                if ($(this).is(':visible')) {
                    hasVisible = true;
                }
            });
            if (!hasVisible) {
                $(children[0]).css('display', 'block');
            }
        }
    });

    // unbin jquery smoth scroll event
    $("li a.tab-title2").unbind('click');

    $("li span.tab-title2").change(function(event) {
        // manage url refonte graphique
        // recup de la valeur select
        let new_param = '';
        if(document.getElementById('display1') != null) {
            new_param = document.getElementById('display1').value
        }
        else if(document.getElementById('display2') != null) {
            new_param2 = document.getElementById('display2').value
        } else {
            return;
        }

        // on recupere le selected
        let _href = ''
        if(document.getElementById('tab-title-1').classList.contains('tab-label-current') == true) {
            // 2e onglet selectione                
            _href = document.getElementById('tab-title-1').getAttribute("href")
            let old_param = getParameterByName('display1',  _href)
            let new_param = document.getElementById('display1').value
            let old_param2 = getParameterByName('display2',  _href)
            let new_param2= document.getElementById('display2').value
            _href = _href.replace(old_param, new_param)
            _href = _href.replace(old_param2, new_param2)
        } else {
            // 1er onglet selectione
            _href = document.getElementById('tab-title-0').getAttribute("href")
            let old_param = getParameterByName('display1',  _href)
            let new_param = document.getElementById('display1').value
            let old_param2 = getParameterByName('display2',  _href)
            let new_param2 = document.getElementById('display2').value
            _href = _href.replace(old_param, new_param)
            _href = _href.replace(old_param2, new_param2)
        }
        location.assign(_href)
        return
    })    

    // Listen for click event on tab menu
    $("li a.tab-title2").click(function(event) {
        event.preventDefault();
        event.stopPropagation();

        // Manage selected tab menu (li level)
        var selected = $(this).parent();
        selected.siblings().removeClass("tabItemFocus");
        selected.siblings(':not(.saut)').addClass("tabItem");
        //selected.siblings().addClass("tab-label");
        selected.addClass("tabItemFocus");
        selected.removeClass("tabItem");
        //selected.removeClass("tab-label");

        // Manage selected tab menu (a level)
        $(this).siblings().removeClass("tab-label-current");
        $(this).addClass("tab-label-current");
        var tab = $(this).attr("href");

        // Select the right page
        if (tab.match("^#")) {
            var globalParentDiv = $(this).parent().parent().parent().parent().find('div.activities');
            if (globalParentDiv.length > 0) {
                $(globalParentDiv[0]).find('.formPage').not(tab).css('display', 'none');
            } else {
                $('.formPage').not(tab).css('display', 'none');
            }
            /*var url = window.location.href;
            url = url.split('#');
            url = url[0] + tab;
            window.location.href = url;*/

            $(tab).fadeIn();

            var submittedFormIDInput = $('input[name="submittedFormID"]');
            sessionStorage.setItem('genFormPage' + (submittedFormIDInput ? submittedFormIDInput.val() : ''), tab);
        } else {
            window.location.href = tab;
        }
    });

    try {
        var submittedFormIDInput = $('input[name="submittedFormID"]');
        var storeTmp = sessionStorage.getItem('genFormPage' + (submittedFormIDInput ? submittedFormIDInput.val() : ''));
        if (storeTmp) {
            $('a[href="' + storeTmp + '"]').click();
        }
    } catch (e) {

    }

    var $resetBox = $('#reset-box');
    $resetBox.hide();

    var $unsubmitBox = $('#unsubmit-box');
    $unsubmitBox.hide();

    var $confirmReset = $resetBox.find('.reset-confirm');
    var $cancelReset = $resetBox.find('.reset-cancel');

    var $confirmUnsubmit = $unsubmitBox.find('.unsubmit-confirm');
    var $cancelUnsubmit = $unsubmitBox.find('.unsubmit-cancel');

    // On confirm reset
    $confirmReset.click(function(event) {
        // Unbind the reset handler to do the reset
        $pendingForm.unbind('reset');
        // Close the box
        $.fancybox.close();
        // Do the reset
        //$pendingForm[0].reset();

        resetAllForm();

        // Trigger the onconfirmreset event
        // to rebind the prevent default reset
        $resetBox.trigger('onconfirmreset', { 'reset': true });
    });

    // On confirm unsubmit
    $confirmUnsubmit.click(function(event) {
        // Unbind the unsubmit handler to do the unsub
        $pendingForm.unbind('unsubmit');
        // Close the box
        $.fancybox.close();

        unsubmitForm();

        // Trigger the onconfirmunsubmit event
        // to rebind the prevent default reset
        $unsubmitBox.trigger('onconfirmunsubmit', { 'unsubmit': true });
    });

    // On cancel do nothing
    $cancelReset.click(function(event) {
        $.fancybox.close();
    });

    // On cancel do nothing
    $cancelUnsubmit.click(function(event) {
        $.fancybox.close();
    });

    // Trigger custom actin on custom reset
    $resetBox.on('onconfirmreset', function(e, detail) {
        // re bind the reset action
        $pendingForm.bind('reset', resetHandler);
    });

    // Trigger custom actin on custom reset
    $unsubmitBox.on('onconfirmunsubmit', function(e, detail) {
        // re bind the unsubmit action
        $pendingForm.bind('unsubmit', unsubmitHandler);
    });

    // Bind custom event to the reset action
    $pendingForm.bind('reset', resetHandler);
    // Bind custom event to the unsubmit action
    $pendingForm.bind('unsubmit', unsubmitHandler);

    // Prevent from default reset action
    // and open the popup
    function resetHandler(event, detail) {
        event.preventDefault();

        $.fancybox.open("#reset-box");
    }
    // Prevent from default unsubmit action
    // and open the popup
    function unsubmitHandler(event, detail) {
        event.preventDefault();

        $.fancybox.open("#unsubmit-box");
    }

    // Fixme: If button add on each page,
    // handle submitting if not the last page to change the page

    var executeFirst;
    var inputs = $pendingForm.find('input:enabled, select:enabled, textarea:enabled').not('input[type="submit"], input[type="button"], input[type="unsubmit"], input[type="reset"], .skip-autosave');
    var timeoutCount = typeof $pendingForm.find('input[name="autosave"]').val() != 'undefined' ? $pendingForm.find('input[name="autosave"]').val() : 30000;

    inputs.on('change', function(e) {
        e.preventDefault();
        clearTimeout(timeoutGeneratedForm);

        //recuperation de l'onget a enregistrer
        var ongletToSave = '';
        var elEnCours = $(this).parent();
        while (elEnCours) {
            if (elEnCours.hasClass('formPage')) {
                break;
            }
            elEnCours = elEnCours.parent();
        }
        if (elEnCours && elEnCours.hasClass('formPage')) {
            ongletToSave = elEnCours;
        }

        initTimeout(timeoutCount, ongletToSave);
    });

    FileDeleteListener = function(e) {
        e.preventDefault();
        autoSave('file-delete', $(this), false);
    }
    $('[name="file_delete"]').click(FileDeleteListener);

    $('.generated-form #registration-unsubmit').on('click', function(e) {
        e.preventDefault();
        clearTimeout(timeoutGeneratedForm);
        $pendingForm.trigger('unsubmit');
    });


    $(document).on('click', '.generated-form #registration-draft, .generated-form #registration-save, .generated-form #registration-save-staff', function (e) {
        e.preventDefault();
        
        // Clear any previously set timeout
        clearTimeout(timeoutGeneratedForm);
    
        // Get the currently active tab ID
        const activeTab = $('.tabItemFocus').find('a')[0]?.href.split('#')[1] || '';
        const idCible = $('#' + activeTab).length ? activeTab : '';
    
        waiterShow();
    
        // Determine save type
        const actionType = this.id === 'registration-save-staff' ? 'staff-save' :
                          this.id === 'registration-save' ? 'save' : 'draft';
    
        autoSave(actionType, null, idCible);
    });
    
    // Handling error avec server side validation
    if ($(".error").length) {
        $(".error").html(null);
        var errorMsg = $(".error").attr('data-errorMsg');
        var msg = '<p style="padding:10px" class="bg-danger text-danger">' + errorMsg + '</p>';
        $('.info').html(msg);
        $('.info').show('blind', {}, 500);
        setTimeout(function() { $('.info').hide('blind', {}, 500) }, 5000);
    }

    /**
     * initiate the timeout
     * @param  {[type]} time [description]
     * @return {[type]}      [description]
     */
    function initTimeout(time, ongletToSave) {
        var objectId = '';
        if (ongletToSave) {
            objectId = ongletToSave.attr('id');
        }
        var time = typeof time !== 'undefined' ? time : 30000;
        timeoutGeneratedForm = window.setTimeout('autoSave(\'autosaved\', null, \'' + objectId + '\')', time);
        pageAutoSave = objectId;
    }

    /**-------------------------------------------------------------------*
     *-------------------------------Sub form-----------------------------*
     *--------------------------------------------------------------------*/

    // Prevent all subform to be submited on click
    $subForms = $('.subform');
    // Hide sub form by default
    $subForms.hide();

    // Display subforms
    $subformButtons = $('.subform-table button.add-btn');
    $subformButtons.click(function(event) {
        var subformDataId = $(this).attr('data-subform');
        var formId = "#" + subformDataId;
        $.fancybox.open(formId);

        // Set the formFieldsetID
        var $input = $subForms.find('input[name="formFieldsetID"]');
    });

    // Edit
    var $editBtn = $('.edit-btn');
    $editBtn.click(function(e) {
        e.preventDefault();
        var recordID = $(this).attr('data-edit');

        // Get field values
        // And set it into the subform
        var inputs = $('.record' + recordID);
        inputs.each(function(index, el) {
            $input = $subForms.find('input[name="' + el.name + '"]');
            // Radio case
            if ($input.length > 1) {
                // Due to multiple sub input to the radio
                // loop over all input to test the value to be checked
                $input.each(function(idx, element) {
                    if (element.value.toLowerCase() == el.value.toLowerCase()) {
                        $(element).prop('checked', true);
                    }
                });
                // No input found
            } else if (!$input.length) {
                $input = $subForms.find('select[name="' + el.name + '"]');
                if ($input.length) {
                    // Find select options
                    var $options = $input.find('option');
                    // Loo over options
                    // To set the selected property
                    $options.each(function(index, option) {
                        if (option.value.toLowerCase() == el.value.toLowerCase()) {
                            $(option).prop('selected', 'true');
                        }
                    });
                } else if (!$input.length) {
                    $input = $subForms.find('textarea[name="' + el.name + '"]');
                    // If special input
                    $input.val(el.value);
                    // Not a textarea
                    if (!$input.length) {
                        $input = $subForms.find('input[name*="[' + el.name + ']"]');
                        if ($input.val().toLowerCase() != "" && el.value.toLowerCase() == $input.val().toLowerCase()) {
                            $input.prop('checked', true);
                        }
                        // Do not set the value cause
                        // in case the checkbox is not checked, the value will be null
                    }
                }
            } else {
                // Simple default input
                $input.val(el.value);
            }
        });

        // Display the sub form
        var subformDataId = $(this).attr('data-subform');
        var formId = "#" + subformDataId;

        $.fancybox.open(formId, {
            afterClose: function(e) {
                // On close clean inputs
                var $tableInput = $subForms.find('input[name="table"]');
                var table = $tableInput.val();
                $subForms.find('input[name="' + table + 'ID"]').val(null);
                $subForms.find('input[type!="hidden"]')
                    .not('input[type="submit"]')
                    .not('input[type="radio"]')
                    .not('input[type="checkbox"]')
                    .val(null)
                $subForms.find('input[type!="hidden"]').not('input[type="submit"]').removeProp('checked');
                $subForms.find('select option').removeProp('selected');
                $subForms.find('textarea').val(null);
            }
        });
    });

    // Remove
    var $removeBtn = $('.remove-btn');
    var $removeBox = $('#remove-box');
    $removeBox.hide();

    var removeRecordID;
    $removeBtn.click(function(e) {
        e.preventDefault();

        // Display the confirm box
        var subformDataId = $(this).attr('data-subform');
        var formId = "#" + subformDataId;

        // Get form value to be send

        // Form variable group
        var group = $(formId).find('input[name="group"]').val();
        var $hiddenInputGroup = $("#remove-box .input-group");
        $hiddenInputGroup.val(group);


        // Get the table name
        var $tableInput = $subForms.find('input[name="table"]');
        var table = $tableInput.val();

        // Table input
        // Column
        $hiddenInputTable = $('#remove-box input[name="table"]');
        $hiddenInputTable.val(table);

        // Record to remove
        // Column
        removeRecordID = $(this).attr('data-remove');
        var $hiddenInputTable = $('#remove-box .remove-recordID');
        $hiddenInputTable.attr('name', table + "ID");
        $hiddenInputTable.val(removeRecordID);

        // Exhange popup html
        var subFormHtml = $(formId).html();
        $(formId).html($removeBox.html());

        // Listen to the cancel button
        $removeBoxCancel = $(formId + ' .remove-cancel');
        $removeBoxCancel.click(function(event) {
            event.preventDefault();
            $.fancybox.close();
        });

        $.fancybox.open(formId, {
            afterClose: function(e) {
                // On close reset the popup to the subform
                $(formId).html(subFormHtml);
                $removeBoxCancel.unbind('click');
            }
        });
    });

    // Register remove-box listeners
    $removeBoxConfirm = $('#remove-box .remove-confirm');

    /**-------------------------------------------------------------------*
     *---------------------------Input Validation-------------------------*
     *--------------------------------------------------------------------*/

    // Get all textarea to validate
    var $validateTextarea = $('textarea.validate');
    var $submitButton = $('.generated-form #registration-save');

    $validateTextarea.keyup(eventKeyup);

    // Ttrigger the change event to count the first word
    // Odd
    $validateTextarea.each(function(el) {
        this.ICHeventKeyup = eventKeyup;
        this.ICHeventKeyup();
    });
});

// Invalide inputs
var invalideInput = 0;

function eventKeyup() {
    // Get the words length validation
    var minWord = $(this).attr('data-minword');
    var maxWord = $(this).attr('data-maxword');

    // validate singleton
    // Represent the validation state of the current input
    if (typeof this.tovalidate == "undefined") {
        this.tovalidate = false;
    }

    var regex = /\s+/gi;
    var color = "#880000";
    var wordCount = $(this).val().trim().replace(regex, ' ').split(' ').length;
    if ($(this).val().trim() == "") {
        wordCount = 0;
    }

    if (wordCount > minWord && wordCount < maxWord) {
        color = "#008800";
        if (this.tovalidate) {
            invalideInput--;
            this.tovalidate = false;
        }
    } else {
        if (!this.tovalidate) {
            invalideInput++;
            this.tovalidate = true;
        }
    }

    // The counter message which display the word count
    var message = minWord + " &lt;  <span style='color:" + color + "'><b>" + wordCount + "</b></span> &lt; " + maxWord;
    if ($(".counter-crit" + this.id + "_comment .word-number").length > 1) {
        $(this).parent().find(".counter-crit" + this.id + "_comment .word-number").html(message);
    } else {
        $(".counter-crit" + this.id + "_comment .word-number").html(message);
    }
    // FIXME : error message
}

/**-------------------------------------------------------------------*
 *---------------Date picker with range, managment--------------------*
 *--------------------------------------------------------------------*/
$(document).ready(function() {
    //	prepare response on change event
    $('input[name^="questionDateStart-"],input[name^="questionDateEnd-"]').change(function() {
        var key = $(this).attr('name').split('-')[1];

        // update hiddenfield
        var begin = $('input[name="questionDateStart-' + key + '"]').val();
        var end = $('input[name="questionDateEnd-' + key + '"]').val();
        $('input[name="formTreeID[question-' + key + ']"]').val(begin + ' - ' + end);
    });

    //	load data and set begin and end input
    $('input[name^="questionDateStart-"]').each(function() {
        var key = $(this).attr('name').split('-')[1];
        var table = $('input[name="formTreeID[question-' + key + ']"]').val().split(' - ');
        $('input[name="questionDateStart-' + key + '"]').val(table[0]);
        $('input[name="questionDateEnd-' + key + '"]').val(table[1]);
    });
});

$(document).ready(function() {
    initialiseSelectYearCountry();
});

/**
 * Project Activity
 */
$(document).ready(function() {
    var pad = function(num, size) {
        var s = num + "";
        while (s.length < size) s = "0" + s;
        return s;
    };

    var initActivityNowListener = function() {
        $('#activity_now').on('click', function() {

            var today = new Date();
            var current_month = today.getMonth() + 1;
            var next_month = today.getMonth() + 2;
            if (next_month == 11) { next_month = 1; }
            var current_year = today.getFullYear();

            var select1 = $('select[name=month_started_before]');
            select1.val(pad(next_month, 2));
            select1.change();
            var select2 = $('select[name=year_started_before]');
            select2.val(current_year);
            select2.change();
            var select3 = $('select[name=month_completed_after]');
            select3.val(pad(current_month, 2));
            select3.change();
            var select4 = $('select[name=year_completed_after]');
            select4.val(current_year);
            select4.change();
        });
    };

    var initActivityThisYearListener = function() {
        $('#activity_this_year').on('click', function() {

            var today = new Date();
            var previous_year = today.getFullYear() - 1;
            var next_year = today.getFullYear() + 1;

            $('select[name=month_started_before]').val("01").change();
            $('select[name=year_started_before]').val(next_year).change();
            $('select[name=month_completed_after]').val("12").change();
            $('select[name=year_completed_after]').val(previous_year).change();
        });
    };

    var initActivityThisBienniumListener = function() {
        $('#activity_this_biennium').on('click', function() {
            var today = new Date();
            var year_starts = today.getFullYear() - (today.getFullYear() % 2) + 2;
            var year_ends = year_starts - 3;

            $('select[name=month_started_before]').val("01").change();
            $('select[name=year_started_before]').val(year_starts).change();
            $('select[name=month_completed_after]').val("12").change();
            $('select[name=year_completed_after]').val(year_ends).change();
        });
    };

    var resetActivityDateFilters = function() {
        $('select[name*=after] , select[name*=before] ').each(function() {
            $(this).val($(this).find("option:first-child").val()).change();
        })
    };

    var resetTopFilters = function() {
        $('form[name=project_compound_filter_form] fieldset input:text').val('');
        $('form[name=project_compound_filter_form] fieldset input:checkbox').prop('checked', true);

        $(
            'fieldset input:checkbox[name=benef_sid],' +
            'fieldset input:checkbox[name=benef_africa],' +
            'fieldset input:checkbox[name=benef_post_disaster],' +
            'input:checkbox[name=exemplary]'
        ).prop('checked', false);
        $('#filter-country, select[name=donorID] ').each(function() {
            $(this).val($(this).find("option:first-child").val()).change();
        });
    };

    var initCancelFiltersListener = function() {
        $('#cancel_filters').on('click', function() {
            // code below was used when we didn't want the cancel filter to refresh the page
            // resetTopFilters();
            // resetActivityDateFilters();
            // closeFieldSets();

            var url = window.location.protocol + "//" + window.location.host + window.location.pathname;
            window.location.href = url;
        });
    };

    var grayOutSelectPeriod = function() {
        $(".activity-period select").each(function() {
            if ($(this).val() === "") {
                $(this).siblings("span").css("background-color", "#cbcbcb");
            } else {
                $(this).siblings("span").css("background-color", "white");
            }
        });
    };

    var grayOutSelectPeriodOnChange = function() {
        $(".activity-period select").on("change", function() {
            grayOutSelectPeriod();
        });
    };

    var emptyFiltersForm = function() {
        $("input[name=min_amount],input[name=max_amount]").val();
        $("input[name='activity_funds_source[]']").prop('checked', false);
        $("select[name=donorID]").val("00001");
        $("input[name='benef_eb_group[]']").prop('checked', false);
        $("select[name=filter-country]").val("00001");
        $("input[name=benef_sid], input[name=benef_africa], input[name=benef_post_disaster]").prop('checked', false);
        $("select[name*=after] , select[name*=before]").val("");
        $("select[name=term]").val("");
        $("input[name='assistance_purpose[]']").prop('checked', false);
    };

    var updateFormFiltersFromUrl = function() {
        emptyFiltersForm();
        var queryString = window.location.search;
        var request = URLToArray(queryString);

        if (request['compound_submit'] === undefined) {
            resetTopFilters();
            resetActivityDateFilters();
        } else {
            var fieldsetsToOpen = [];

            for (var property in request) {
                if (request.hasOwnProperty(property)) {
                    switch (property) {
                        case 'exemplary':
                            if (request[property] === "on") {
                                $('form[name=project_compound_filter_form] input:checkbox[name=exemplary]').prop('checked', true);
                                fieldsetsToOpen['.js-activity-funds'] = true;
                            }
                            break;
                        case 'min_amount':
                        case 'max_amount':
                            $("input[name=" + property + "]").val(request[property]);
                            if (request[property] !== "") {
                                fieldsetsToOpen['.js-activity-amount'] = true;
                            };
                            break;
                        case 'activity_funds_source[]':
                        case 'benef_eb_group[]':
                        case 'assistance_purpose[]':
                            if (Array.isArray(request[property])) {
                                for (var fundSource in request[property]) {
                                    $("input[name='" + property + "'][value='" + request[property][fundSource] + "']").prop('checked', true);
                                    $("input[name='" + property + "'][value='" + parseInt(request[property][fundSource]) + "']").prop('checked', true);
                                }
                            } else {
                                $("input[name='" + property + "'][value='" + request[property] + "']").prop('checked', true);
                            }
                            break;
                        case 'donorID':
                            $("select[name='donorID']").val(request[property]).change();
                            if (request[property] !== '00001') {
                                fieldsetsToOpen['.js-activity-funds'] = true;
                            }
                            break;
                        case 'countryID':
                            $("select[name='countryID']").val(request[property]).change();
                            if (request[property] !== '00001') {
                                fieldsetsToOpen['.js-activity-beneficiaries'] = true;
                            }
                            break;
                        case 'benef_sid':
                        case 'benef_africa':
                        case 'benef_post_disaster':
                            $("input[name=" + property + "]").prop('checked', true);
                            if (request[property] === 'on') {
                                fieldsetsToOpen['.js-activity-beneficiaries'] = true;
                            }
                            break;
                        case 'term':
                            $("select[name='term']").val(request[property]).change();
                            if (request[property] !== '') {
                                fieldsetsToOpen['.js-activity-type'] = true;
                            }
                            break;
                        default:
                            break;
                    }

                    // cas ou select de date
                    if (property.indexOf('after') !== -1 || property.indexOf('before') !== -1) {
                        // cas ou select mois
                        if (property.indexOf('month') !== -1) {
                            var correspondingField = property.replace("month", "year");

                            // mois non vide
                            if (request[property] !== "") {
                                // si l'année est vide , on vide le mois egalement
                                if (request[correspondingField] === "") {
                                    $("select[name='" + property + "']").val("").change();
                                } else {
                                    $("select[name='" + property + "']").val(request[property]).change();
                                    fieldsetsToOpen['.js-activity-period'] = true;
                                }
                                // mois vide
                            } else {
                                // si l'année est non vide , on passe le mois à janvier
                                if (request[correspondingField] !== "") {
                                    $("select[name='" + property + "']").val("01").change();
                                    fieldsetsToOpen['.js-activity-period'] = true;
                                }
                            }
                        }
                        // cas ou select année
                        else {
                            $("select[name='" + property + "']").val(request[property]).change();
                            if (request[property] !== "") {
                                fieldsetsToOpen['.js-activity-period'] = true;
                            }
                        }
                    }
                }
            }

            if (!request.hasOwnProperty('benef_eb_group[]') || request['benef_eb_group[]'].length !== 7) {
                fieldsetsToOpen['.js-activity-beneficiaries'] = true;
            }

            if (!request.hasOwnProperty('activity_funds_source[]') || request['activity_funds_source[]'].length !== 5) {
                fieldsetsToOpen['.js-activity-funds'] = true;
            }

            for (var fieldsetToOpen in fieldsetsToOpen) {
                if (fieldsetsToOpen[fieldsetToOpen]) {
                    $(fieldsetToOpen).show();
                }
            }
        }
    };

    var URLToArray = function(query) {
        var re = /([^&=]+)=?([^&]*)/g;
        var decode = function(str) {
            return decodeURIComponent(str.replace(/\+/g, ' '));
        };

        var params = {},
            e;
        if (query) {
            if (query.substr(0, 1) == '?') {
                query = query.substr(1);
            }

            while (e = re.exec(query)) {
                var k = decode(e[1]);
                var v = decode(e[2]);
                if (params[k] !== undefined) {
                    if (!$.isArray(params[k])) {
                        params[k] = [params[k]];
                    }
                    params[k].push(v);
                } else {
                    params[k] = v;
                }
            }
        }
        return params;
    };

    var closeFieldSets = function() {
        var fieldsets = ['.js-activity-beneficiaries', '.js-activity-amount', '.js-activity-funds', '.js-activity-period', '.js-activity-type'];
        for (var i in fieldsets) {
            $(fieldsets[i]).hide(300);
        }
        var arrows = ['.js-arrow-beneficiaries', '.js-arrow-amount', '.js-arrow-funds', '.js-arrow-period', '.js-arrow-type'];
        for (var i in arrows) {
            $(arrows[i]).removeClass('arrow-up');
            $(arrows[i]).addClass('arrow-down');
        }
    };

    var toggleFieldset = function(toggleClass, toggleArrow) {
        var arrowElement = $(toggleArrow).first();
        if (arrowElement.hasClass('arrow-up')) {
            arrowElement.removeClass('arrow-up');
            arrowElement.addClass('arrow-down');
        } else {
            arrowElement.removeClass('arrow-down');
            arrowElement.addClass('arrow-up');
        }

        $(toggleClass).toggle(300);
    };

    var toggleFieldsetListener = function() {
        $('.js-span-beneficiaries').on('click', function() {
            toggleFieldset('.js-activity-beneficiaries', '.js-arrow-beneficiaries');
        });

        $('.js-span-amount').on('click', function() {
            toggleFieldset('.js-activity-amount', '.js-arrow-amount');
        });

        $('.js-span-funds').on('click', function() {
            toggleFieldset('.js-activity-funds', '.js-arrow-funds');
        });

        $('.js-span-period').on('click', function() {
            toggleFieldset('.js-activity-period', '.js-arrow-period');
        });

        $('.js-span-term').on('click', function() {
            toggleFieldset('.js-activity-type', '.js-arrow-type');
        });

        $('.js-span-biblio').on('click', function() {
            toggleFieldset('.js-activity-biblio', '.js-arrow-biblio');
        });
    };

    var initProjectActivityScripts = function() {
        initActivityNowListener();
        initActivityThisYearListener();
        initActivityThisBienniumListener();
        initCancelFiltersListener();
        grayOutSelectPeriod();
        grayOutSelectPeriodOnChange();
        updateFormFiltersFromUrl();
        toggleFieldsetListener();
    };

    initProjectActivityScripts();
});


/**
 * ICH FORM 09
 * special case : trigger show one select when particular option selected in another select
 * happens 3 times : tab 3 (1) and tab 6 (2)
 */
$(document).ready(function() {
    var isInternationalSelected = function() {
        return $("#form-ich-ngo-area1").parent().siblings("select").find(":selected").filter("option[id=2498]").length > 0;
    };
    var isOtherDomainSelected = function() {
        return $("#form-ich-ngo-domain").parent().siblings("select").find(":selected").filter("option[id=08]").length > 0;
    };
    var isOtherSafeMeasSelected = function() {
        return $("#form-ich-ngo-safe-meas").parent().siblings("select").find(":selected").filter("option[id=08]").length > 0;
    };

    if (!isInternationalSelected()) {
        $("#form-ich-ngo-area2").parent().parent().hide();
    }
    if (!isOtherDomainSelected()) {
        $("#form-ich-ngo-domain").parent().parent().siblings('div').has('input[type=text]').hide();
    }
    if (!isOtherSafeMeasSelected()) {
        $("#form-ich-ngo-safe-meas").parent().parent().siblings('div').has('input[type=text]').hide();
    }

    $("#form-ich-ngo-area1").parent().siblings("select").on("change", function() {
        if (isInternationalSelected()) {
            $("#form-ich-ngo-area2").parent().parent().show();
        } else {
            $("#form-ich-ngo-area2").parent().parent().hide();
        }
    });

    $("#form-ich-ngo-domain").parent().siblings("select").on("change", function() {
        if (isOtherDomainSelected()) {
            $("#form-ich-ngo-domain").parent().parent().siblings('div').has('input[type=text]').show();
        } else {
            $("#form-ich-ngo-domain").parent().parent().siblings('div').has('input[type=text]').hide();
        }
    });

    $("#form-ich-ngo-safe-meas").parent().siblings("select").on("change", function() {
        if (isOtherSafeMeasSelected()) {
            $("#form-ich-ngo-safe-meas").parent().parent().siblings('div').has('input[type=text]').show();
        } else {
            $("#form-ich-ngo-safe-meas").parent().parent().siblings('div').has('input[type=text]').hide();
        }
    });
});

/* MENU USER / MENU COUNTRY / SUB SUB MENU -  SPECIAL MOBILE */
$(document).ready(function() {
    $('.nav_country-link').click(function(event) {
        event.stopPropagation();
        $(".nav_country-subblock").click(function (event) {event.stopPropagation();}).animate({opacity: "toggle"}, 500, function () {
            $("document").one("click", function (event) {
                $( "#header" ).find(".nav_country-subblock").animate({opacity: "toggle"}, 500);
            });
        });
    })
    $(".dropdown-toggle").on("click", function() {
        $(this).siblings(".dropdown-menu").toggle("slow");
    })
    $(".dropdown-toggle-user").on("click", function() {
        $("#navbar").find(".dropdown-menu-user").toggle("slow");
    })
    $(".module-header").on("click", function() {
        $(this).siblings(".module-content").toggle("slow");
    })
    $(".dropdown-link").on("click", function() {
        $(this).next(".dropdown-content").toggle("slow");
    })
});

/* SLIDE NAVIGATION SPECIAL MOBILE
$(document).ready(function() {
  $('.js-submennu-action').click(function () {
      if($('.js-submenu').hasClass('hidden-nav'))
      {
         $(this).next('.js-submenu').removeClass('hidden-nav').addClass('sp-mobile');
      }
      else {
         $(this).next('.js-submenu').addClass('hidden-nav').removeClass('sp-mobile');
        }
  })
});*/

/* SCROLL TOP*/
jQuery(document).ready(function($) {
    // browser window scroll (in pixels) after which the "back to top" link is shown
    var offset = 300,
        //browser window scroll (in pixels) after which the "back to top" link opacity is reduced
        offset_opacity = 1200,
        //duration of the top scrolling animation (in ms)
        scroll_top_duration = 700,
        //grab the "back to top" link
        $back_to_top = $('.cd-top');

    //hide or show the "back to top" link
    $(window).scroll(function() {
        ($(this).scrollTop() > offset) ? $back_to_top.addClass('cd-is-visible'): $back_to_top.removeClass('cd-is-visible cd-fade-out');
        if ($(this).scrollTop() > offset_opacity) {
            $back_to_top.addClass('cd-fade-out');
        }
    });

    //smooth scroll to top
    $back_to_top.on('click', function(event) {
        event.preventDefault();
        $('body,html').animate({
            scrollTop: 0,
        }, scroll_top_duration);
    });

    $(window).scroll(function () {
        $('.header').toggleClass("is-scroll", ($(window).scrollTop() > 100));
     });
});

/* OPEN SERACH TABS MOBILE */
(function($) {
    // GLOBALS VARS //
    var _doc = $(document);
    var _window = $(window);

    function windowSize() {
        windowHeight = window.innerHeight ? window.innerHeight : $(window).height();
        windowWidth = window.innerWidth ? window.innerWidth : $(window).width();
    }
    $(window).resize(function() {
        windowSize();

        var myButton = true,
            buttonShow = $('.hdtb-msel a'),
            SubNav = $('#hdtbSum');
        SubNavElem = $('.hdtb-mitem');

        if (windowWidth < 770) {
            $.initOpenTabs = function() {
                buttonShow.attr("href", "#")
                buttonShow.click(function() {
                    SubNav.addClass('open');
                    SubNavElem.addClass('open');
                    myButton = false;
                });
            };
        } else {
            $.initOpenTabs = function() {
                buttonShow.click(function() {
                    SubNav.removeClass('open');
                    SubNavElem.removeClass('open');
                    myButton = false;
                });
            };
        }
        _doc.ready(function() {
            $.initOpenTabs();
        });
    })
})(jQuery);

/* SCRIP AUTOCOMPLETE NAV INPUT COUNTRY*/
$(document).ready(function() {
    set_country_autocomplete();
});

$(document).ready(function() {
    $('#lists-pr-element li:first a').click();
});

$(document).ready(function() {
    var elementsListFormSubmit = function(e) {
        e.preventDefault();

        var data1 = $('#elementsListForm1').serializeArray();
        var data2 = $('#elementsListForm2').serializeArray();

        var data = '?';
        $.each(data1, function(index, value) {
            if (data !== '?') {
                data += '&';
            }
            data += value['name'] + '=' + value['value'];
        });
        $.each(data2, function(index, value) {
            if (data !== '?') {
                data += '&';
            }
            data += value['name'] + '=' + value['value'];
        });
        data += '#tabs';

        var formUrl = window.location.href.split('?')[0];
        window.location.replace(formUrl + data);
    };

    $('#elementsListForm1').on('submit', elementsListFormSubmit);
    $('#elementsListForm2 select').on('change', elementsListFormSubmit);
});

function biblioAutocomplete(ajaxCall) {
    if (ajaxCall) {
        $('#biblio-div').find('select, :file, :radio, :checkbox').not('.sf').uniform({
            selectClass: 'select',
            checkboxClass: 'checkbox',
            fileBtnClass: 'form_btn',
            fileButtonHtml: _downloadTxt,
            fileDefaultHtml: _selectFileTxt,
            fileClass: 'input_file',
            filenameClass: 'input_file_name',
            selectAutoWidth: false
        });
        $('#biblio-div .select span').after('<i class="arrow"></i>');
    }
    $('.biblioAutocomplete').each(function() {
        var tags = $(this).attr('tags');
        $(this).select2({ tags: tags });
        $(this).trigger('change');

        if (!$(this).hasClass('biblioAutocompleteMultiple')) {
            $(this).parent().children('span').first().hide();
            $(this).parent().children('i').first().hide();
        }
        if ($(this).hasClass('red_bold')) {
            $(this).parent().children('span.select2-container').first().addClass('red_bold');
        }
    });
    $('.biblioJSAutocomplete').each(function() {
        var url = window.location.href.split('?')[0];
        var field = $(this).attr('field');
        $(this).autocomplete({
            source: function(request, response) {
                request['autocomplete'] = field;
                $.post(window.location.pathname, request, response, 'json');
            },
            success: function(data) {
                response(data);
            }
        });
    });
}
$(document).ready(function() {
    if (typeof biblioActiveTab !== 'undefined') {
        $(biblioActiveTab).click();
    }
    biblioAutocomplete(false);
    $('.biblio-delete').on('click', function(e) {
        e.preventDefault();
        if (confirm(biblioConfirmDelete)) {
            window.location.href = $(this).attr('href');
        }
    });

    $('.filter-select2').each(function() {
        $(this).select2();
    });

    if ($('.researcher-list div[id^="tab-"][data-selected-tab="yes"]').length > 0) {
        var selected_tab = $('.researcher-list div[id^="tab-"][data-selected-tab="yes"]').attr('id');
        $('.researcher-list div[id^="tab-"][data-selected-tab="yes"]').parents('article').find('.block_tabs a[href="#' + selected_tab + '"]').trigger('click');
    }
});

$(document).ready(function() {
    $('.photo-nav nav ul li a.page-link').on('click', function(e) {
        e.preventDefault();

        var pageUrl = window.location.href.replace('#photo-nav', '').split('?');

        var urlParameters = '?page=' + $(this).html().trim();
        if (pageUrl.length > 1) {
            var aParameters = pageUrl[1].split('&');
            $.each(aParameters, function(index, parameter) {
                var parameterInfos = parameter.split('=');
                if (parameterInfos.length == 2 && parameterInfos[0] !== 'page') {
                    urlParameters += '&' + parameterInfos[0] + '=' + parameterInfos[1];
                }
            });
        }

        window.location.href = pageUrl[0] + urlParameters + '#photo-nav';
    });
});

// #686 simule clic sur les radio avec "no" coché, pour placer le tag spécial ~NO~ lors du reload du formulaire
$(document).ready(function() {
    $(':radio[value="no"]:checked').each(function() { 
        $(this).trigger('click'); 
    });
});

/**
 * Find an element in parents
 * @param object obj 
 */
function findSiblingInParent(obj, item) {
    var ele = null;
    var ptr = obj;
    var cnt = 0;
    if (ptr) {
        do {
            cnt++;
            ptr = ptr.parent();
            if (ptr) {
                ele = ptr.siblings(item);
                if (ele.length) {
                    return ele.first().html();
                }
            }
        } while (ptr && cnt<32);
    }
    return '';
}

/**
 * Retourne la chaine str sans les tags html
 * @param str : le texte à nettoyer
 * @param allow : une chaine de tags (ex: "<p><a><div>")
 * @return string
 */
function strip_tags(str, allow) {
    // making sure the allow arg is a string containing only tags in lowercase (<a><b><c>)
    allow = (((allow || '') + '').toLowerCase().match(/<[a-z][a-z0-9]*>/g) || []).join('');

    var tags = /<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;
    var commentsAndPhpTags = /<!--[\s\S]*?-->|<\?(?:php)?[\s\S]*?\?>/gi;
    return str.replace(commentsAndPhpTags, '').replace(tags, function($0, $1) {
        return allow.indexOf('<' + $1.toLowerCase() + '>') > -1 ? $0 : '';
    });
}

/**
 * Auto save function
 * @return {[type]} [description]
 */
function autoSave(action, element, ongletToSaveID) {
    var saveDraft = false;
    if (action === 'draft') {
        action = 'autosaved';
        saveDraft = true;
    }

    reload = false;
    switch (action) {
        case 'save':
        case 'staff-save':
            reload = true;
            break;
    }

    // Get the url
    var url = window.location.protocol + "//" + window.location.host + window.location.pathname;
    if (typeof autoSaveLocks[url] === 'undefined') {
        autoSaveLocks[url] = false;
    }

    var interval = setInterval(function() {
        if (!autoSaveLocks[url]) {
            autoSaveLocks[url] = true;
            pageAutoSave = null;

            var formData = getGeneratedFormData(action, element, ongletToSaveID);
            if (formData !== false) {
                $.ajax({
                    url: url,
                    type: 'POST',
                    contentType: false,
                    processData: false,
                    data: formData
                }).done(function(response) {
                    waiterHide();

                    var r = {};
                    try {
                        r = JSON.parse(response);
                        if (action=='file-delete' && r=='ok') {
                            var span = $(element).parent();
                            $(span).remove();
                        }
                    } catch (err) {
                        console.log(err);
                    }

                    // update submittedFormId
                    if (r.submittedFormId && r.submittedFormId != '') {
                        updateFormAction(r.submittedFormId);
                    }

                    var msg = '';
                    var timerHide = 5000;
                    // show success message
                    if (r.success) {
                        $(".error").html(null);
                        msg = '<p style="padding:10px;margin-bottom:0" class="bg-success text-success">' + r.success + '</p>';
                    }

                    // show error message
                    if (r.error) {
                        $(".error").html(null);
                        msg = '<p style="padding:10px;margin-bottom:0" class="bg-danger text-danger">' + r.error + '</p>';
                    }
                    if (r.hasModifications) {
                        msg += '<p style="padding:10px;margin-bottom:0" class="bg-warning text-warning">' + r.hasModifications + '</p>';
                        timerHide = 10000;
                    }
                    if (msg) {
                        $('#infoFixed').html(msg);
                        $('#infoFixed').show('blind', {}, 500);
                        setTimeout(function() { $('#infoFixed').hide('blind', {}, 500); }, timerHide);
                    }

                    $('.form-err').each(function() {
                        $(this).removeClass('form-err');
                    });

                    if (r.generationDate && $('#generationDate').length) {
                        $('#generationDate').val(r.generationDate);
                    }

                    var invalidFields = [];
                    var invalidFieldsCount = [];
                    if (r.error_fields) {
                        reload = false;

                        for (var iF = 0; iF < r.error_fields.length; iF++) {
                            var obj = $("[name='formTreeID[question-" + r.error_fields[iF] + "]']");
                            if (obj.length > 0) {
                                obj.addClass('form-err');
                            } else {
                                var obj = $("[name='formTreeID[question-1-" + r.error_fields[iF] + "]']");
                                if (obj.length > 0) {
                                    obj.addClass('form-err');
                                }
                            }
                            var fieldset = '';
                            var fieldLabel = '';
                            if (obj) {
                                var txt = findSiblingInParent(obj, '.intro');
                                if (txt.length) {
                                    var customCode = findSiblingInParent(obj.parent(), '.fld_question > legend');
                                    if (customCode !== '') {
                                        txt = customCode + ' : ' + txt;
                                    }
                                    fieldset = txt;
                                } else {
                                    fieldset = findSiblingInParent(obj.parent(), '.fld_question > legend');
                                    if (fieldset === '') {
                                        fieldLabel = findSiblingInParent(obj, 'label').replace('<em>*</em>', '');
                                    }
                                }
                            }
                            var entry = strip_tags(fieldset, '') + ((fieldset.length && fieldLabel.length) ? ' - ' : '') + strip_tags(fieldLabel, '');
                            if (entry.length) {
                                if (!invalidFields.includes(entry)) {
                                    invalidFieldsCount[entry] = 1;
                                    invalidFields.push(entry);
                                } else {
                                    invalidFieldsCount[entry]++;
                                }
                            }
                        }
                        var errorMsg = '<p class="bg-danger text-danger" style="padding:10px;"><span class="bold">' + missingFieldsLabel + '</span><br />';
                        $.each(invalidFields, function(index, value) {
                            var detail = '';
                            var cnt = invalidFieldsCount[value];
                            if (cnt > 1) {
                                // add items count if more than 1
                                detail = ' <b>(x ' + cnt + ')</b>';
                            }
                            errorMsg += '<span>' + value + detail + '</span><br />';
                        });
                        errorMsg += '</p>';

                        $('#invalidFieldsBox').html(errorMsg);
                        $('#registration-save-staff').show();
                    } else {
                        $('#invalidFieldsBox').html('');
                    }
                    if (saveDraft) {
                        $(document).scrollTop($('.info').offset().top);
                    }

                    // update content of document list or image carousel
                    for (var i in r.HTML) {
                        if (r.HTML[i].identifier) {
                            $(r.HTML[i].identifier).html(r.HTML[i].content);

                            if ($(r.HTML[i].identifier + ' [name="file_delete"]')) {
                                $(r.HTML[i].identifier + ' [name="file_delete"]').click(FileDeleteListener);
                            }

                            if ($(".myGallery")) {
                                $(".myGallery dl a").click(myGallery_click_event);
                                $(".myGallery dl a").first().click();
                            }
                        }
                    }

                    // clear input file to avoid resubmit file !
                    $('.input_file_name').html('Select a file');
                    $('input[type="file"]').val('');

                    if (reload) {
                        window.location.reload();
                    } else if ($('#generatedFormCompletion').length > 0) {
                        var formCompletion = (r.total > 0) ? Math.round((r.validated * 100) / r.total) : 100;
                        $('#generatedFormCompletion').prop('aria-valuenow', formCompletion);
                        $('#generatedFormCompletion').css('width', formCompletion + '%');
                        $('#generatedFormCompletion').html(formCompletion + '%');
                    }
                }).always(function(response) {
                    waiterHide();
                    autoSaveLocks[url] = false;
                });
            } else {
                autoSaveLocks[url] = false;
            }
            clearInterval(interval);
        }
    }, 200);

    return true;
}

/**-------------------------------------------------------------------*
 *----------------------------Form auto save--------------------------*
 *--------------------------------------------------------------------*/
function getGeneratedFormData(action, element, ongletToSaveID) {
    if (action=='file-delete') {
        var data = new FormData();
        data.append('file_delete', $(element).attr('value'));
        data.append('autosaved', true);
        data.append('formID', $('input[name="formID"]').val());
        data.append('submittedFormID', $('input[name="submittedFormID"]').val());
        return data;
    }
    var data = new FormData($('.generated-form')[0]);
    if (ongletToSaveID) {
        var data = new FormData();
        var $postInputs = $('#' + ongletToSaveID).find('input:enabled, select:enabled, textarea:enabled').add('.generated-form input[type=hidden]:not([name^=formTreeID]):enabled');
        $postInputs.each(function(idx) {
            if (this.type == 'checkbox') {
                if (!data.has(this.name)) {
                    obj = $("input[name='" + this.name + "']");
                    for (var jObj in obj) {
                        if (obj[jObj].checked) {
                            data.append(this.name, obj[jObj].value);
                        }
                    }
                }
            } else if (this.type == 'radio') {
                if (!data.has(this.name)) {
                    obj = $("input[name='" + this.name + "']");
                    for (var jObj in obj) {
                        if (obj[jObj].checked) {
                            data.append(this.name, obj[jObj].value);
                        }
                    }
                }
            } else if (this.type == 'file') {
                var fileUpload = $(this).get(0);
                var files = fileUpload.files;
                if (files.length != 0) {
                    for (var i = 0; i < files.length; i++) {
                        data.append(this.name, files[i]);
                    }
                }
            } else if (this.type != 'submit') {
                data.append(this.name, this.value);
            } else {

            }
        });
    }

    if (action === 'autosaved') {
        data.append('autosaved', true);    
    }

    // Add empty selector
    //fix quand valeur vide
    if (ongletToSaveID) {
        var inputStream = $('#' + ongletToSaveID).find('input:enabled, select:enabled')
            .not('input[type="submit"], input[type="button"], input[type="reset"], input[type="unsubmit"]');
    } else {
        var inputStream = $('.generated-form').find('input:enabled, select:enabled')
            .not('input[type="submit"], input[type="button"], input[type="reset"], input[type="unsubmit"]');
    }

    inputStream.each(function(idx) {
        if (!data.has(this.name)) {
            if ((this.type == 'checkbox' || this.type == 'radio')) {
                var coche = -1;
                obj = $("input[name='" + this.name + "']");
                for (var jObj in obj) {
                    if (coche == -1) {
                        coche = 0;
                    }
                    if (obj[jObj].checked) {
                        coche = 1;
                    }
                }
                if (coche == 0) {
                    data.append(this.name, "");
                }
            } else if (this.type == 'select' || this.type == 'select-multiple') {
                var value = this.value;
                if (value == null) {
                    data.append(this.name, "");
                }
            }
        }
    });

    /* This code was preventing tab1 from USL reports to save, but fixing an issue in Convention reports where tab1 doesn't save the whole report
    
    if (ongletToSaveID == 'tab1') {
        // we are on first tab, go to next one
        if (moveToNextTab()) {
            // redo same action on good tab
            if (action === 'draft' || action === 'autosaved') {
                $('#registration-draft').click();
            } else if (action === 'save') {
                $('#registration-save').click();
            } else {
                $('#registration-save-staff').click();
            }
        }
        return false;
    }*/


    if (action === 'save' || action === 'staff-save') {
        $('#invalidFieldsBox').html('');

        var invalidFields = [];
        var generatedFormValidated = true;
        if (action === 'save') {
            //controle server a present
            if (typeof(newGestionErreur) == 'undefined') {
                var selecteur = $('.generated-form .validate');
                if (ongletToSaveID) {
                    if (typeof(verifTousOnglets) == 'undefined' || !verifTousOnglets) {
                        selecteur = $('#' + ongletToSaveID + ' .validate');
                    }
                }
                selecteur.each(function() {
                    var fieldValue = $(this).val().trim();

                    var wordCount = (fieldValue == '' ? 0 : fieldValue.replace(/\s+/gi, ' ').split(' ').length);
                    if (fieldValue.trim() == "") {
                        wordCount = 0;
                    }
                    var minSize = $(this).attr('minlength');
                    minSize = (typeof minSize != 'undefined') ? parseInt(minSize) : 0;
                    var maxSize = $(this).attr('maxlength');
                    maxSize = (typeof maxSize != 'undefined') ? parseInt(maxSize) : 0;

                    var minWord = $(this).data('minword');
                    minWord = (typeof minWord != 'undefined') ? parseInt(minWord) : 0;
                    var maxWord = $(this).data('maxword');
                    maxWord = (typeof maxWord != 'undefined') ? parseInt(maxWord) : 0;

                    if (fieldValue.length < minSize || (maxSize > 0 && fieldValue.length > maxSize) || wordCount < minWord || (maxWord > 0 && wordCount > maxWord)) {
                        generatedFormValidated = false;

                        var fieldset = '';
                        var txt = $(this).parent().siblings('.intro');
                        if (txt.length) {
                            txt = txt.first().html();
                            var customCode = $(this).parent().parent().siblings('legend');
                            if (customCode.length) {
                                txt = customCode.first().html() + ' ' + txt;
                            }
                            fieldset = txt;
                        } else {
                            if ($(this).parent().siblings('legend').length) {
                                fieldset = $(this).parent().siblings('legend').first().html();
                            } else {
                                if ($(this).parentsUntil('fieldset').siblings('legend').length) {
                                    fieldset = $(this).parentsUntil('fieldset').siblings('legend').first().html();
                                }
                            }
                        }

                        var fieldLabel = ($(this).siblings('label').length != 0) ? $(this).siblings('label').first().html().replace('<em>*</em>', '') : '';

                        invalidFields.push(fieldset + ((fieldset.length != 0 && fieldLabel.length != 0) ? ' - ' : '') + fieldLabel);
                    }
                });
            }
        }
        if (generatedFormValidated || action === 'staff-save') {
            if (action === 'staff-save') {
                data.append('forcedsave', '1');
            }
            return data;
        } else {
            var errorMsg = '<p class="bg-danger text-danger" style="padding:10px;"><span class="bold">' + missingFieldsLabel + '</span><br />';
            $.each(invalidFields, function(index, value) {
                errorMsg += '<span>' + value + '</span><br />';
            });
            errorMsg += '</p>';
            $('#invalidFieldsBox').html(errorMsg);
            $('#registration-save-staff').show();
        }
    } else {
        data.append('autosaved', true);
        return data;
    }

    return false;
}

function moveToNextTab() {
    try {
        var cible = $('.tabItemFocus');
        if (cible && cible.next()) {
            cible = cible.next();
        }
        if (cible) {
            if (cible.prop("tagName") == 'LI' && cible.hasClass("tabItem")) {
                cible.find('a:first-child').click();
                $('html, body').animate({ scrollTop: $('#form').height() }, 1);
                return true;
            }
        }
    } catch (err) {
        console.log(err);
    }
    return false;
}

/**
 * Update the form action attribute
 * Update the input[name="submittedFormID"]
 * Update url for user to know
 * @return {[type]} [description]
 */
function updateFormAction(submittedFormID) {
    // Initialize or retrieve the current URL
    var url = window.location.href;

    if (window.location.search == "") {
        // Append query parameter to the URL
        url = url + "?edit_form=" + submittedFormID;

        // Update the URL in the browser without reloading the page
        history.pushState({}, '', url);
    } else {
        // If the URL already has query parameters, ensure we append the new parameter correctly
        var params = new URLSearchParams(window.location.search);
        params.set('edit_form', submittedFormID);
        url = window.location.pathname + '?' + params.toString();
        
        // Update the URL in the browser without reloading the page
        history.pushState({}, '', url);
    }

    // Update the form action
    var $pendingForm = $('.generated-form');
    $pendingForm.prop('action', url);

    // Update the hidden input with the submitted form ID
    $pendingForm.find('input[name="submittedFormID"]').val(submittedFormID);

    // Unlock subforms
    unlockSubForms();
}


/**
 * Unlock subforms
 * @return {[type]} [description]
 */
function unlockSubForms() {
    var $subFormBtn = $('.subform-table .add-btn');
    $subFormBtn.removeProp('disabled');
}

function unsubmitForm() {
    $('#registration-unsubmit').prop('disabled', true);
    waiterShow();
    var url = window.location.href;
    if (url.indexOf("#")) url = url.split("#")[0];
    url = url + "&unsubmit=1";
    $.ajax({
        url: url,
        type: 'POST',
        contentType: false,
        processData: false,
        data: ''
    }).done(function(response) {
        var r = {};
        try {
            r = JSON.parse(response);
        } catch (e) {
            console.error("invalid json !");
        }

        var msg = '';
        // show success message
        if (r.success) {
            $(".error").html(null);
            msg = '<p style="padding:10px;margin-bottom:0" class="bg-success text-success">' + r.success + '</p>';
            $('#infoFixed').html(msg);
            $('#infoFixed').show('blind', {}, 500);
            setTimeout(function() { $('#infoFixed').hide('blind', {}, 500); }, 5000);
        }

        // remove unsubmit button
        $('#unsubmit-button-box').remove();
        waiterHide();
    });
}

function resetAllForm() {
    var submittedFormIDInput = $('input[name="submittedFormID"]');
    var groupInput = $('input[name="group"]');
    if (submittedFormIDInput && groupInput) {
        var url = window.location.protocol + "//" + window.location.host + window.location.pathname;

        waiterShow();

        var formData = new FormData();
        formData.append('resetAllForm', true);
        formData.append('submittedFormID', submittedFormIDInput.val());
        formData.append('group', groupInput.val());

        if (formData !== false) {
            $.ajax({
                url: url,
                type: 'POST',
                contentType: false,
                processData: false,
                data: formData
            }).done(function(response) {
                waiterHide();
                window.location.reload();
            });
        }
    }
}

function collapse(id) {
    let list = ['about','event','ressource','faq'];
    if(document.getElementById(id).style.display == "none" || document.getElementById(id).style.display == "") {
        document.getElementById(id + 'Father').classList.add("arrow_up");
        document.getElementById(id).style.display = "block";
    } else {
        list.forEach((item, index) => {
            document.getElementById(id + 'Father').classList.remove("arrow_up");
            document.getElementById(id).style.display = "none";	
        })
    }
}

function openIdentifiant() {
    openmenu(25, 0)
}

function openmenu(idBloc, pos) {
//    let desktopWidth = 992
    if ($(window).width() < desktopWidth) {
        // recup du titre    
        let id = idBloc.replace("menu-col-", "")
        let titreRubriqueMobile = document.getElementById('head_menu-col-' + id).innerHTML 

        // recup & sav de la div associee pour le remmettre après
        let savContenu = document.getElementById(idBloc).innerHTML
        // pour avoir l'effet overlay
        document.getElementById('divMenu').classList.add("submenu-open")
        let contenuAfficher = document.getElementById(idBloc).innerHTML
        document.getElementById(idBloc).innerHTML = savContenu
        document.getElementById('divMenu').innerHTML = contenuAfficher

        document.getElementById('left-header').innerHTML = 
        '<div class="submenu-header"><span onclick="chevronMobile(\'' + id + '\')" class="material-icons-sharp">chevron_left</span><span class="submenu-title">' + titreRubriqueMobile + '</span></div>'

        document.getElementById("header-mobile").style.display = "none"
        document.getElementById("header-mobile").style.visibility = "hidden"
        document.getElementById("header-mobile-remplacement").style.visibility = "visible"
        document.getElementById("header-mobile-remplacement").style.display = "block"
        document.getElementById("divMenu").style.visibility = "visible"
        document.getElementById("divMenu").style.display = "block"
        //document.getElementById("divMenu").classList.add("submenu-open");

        document.getElementById("body").classList.toggle("menu-open")

    } else {
        if(document.getElementById('login-menu')) {
            document.getElementById('login-menu').style.display = "none";
        }
        for (let i=0; i<13; i++) {
            if(idBloc !== 'menu-col-' + i) {
                if(document.getElementById('menu-col-' + i)) {
                    document.getElementById('menu-col-' + i).classList.remove("m-fadeIn");
                    document.getElementById('menu-col-' + i).classList.add("m-fadeOut");
                    if(document.getElementById('head_menu-col-' + i) != null) {
                        document.getElementById('head_menu-col-' + i).classList.remove("active-menu");
                    }
                }
            } else {
                if(document.getElementById(idBloc).classList.contains('m-fadeIn')) {
                    if(pos == '1') {
                        document.getElementById('menu-col-' + i).classList.remove("m-fadeIn");
                        document.getElementById('menu-col-' + i).classList.add("m-fadeOut");
                        document.getElementById(idBloc).classList.remove("active-menu");
                    }
                } else {
                    document.getElementById(idBloc).classList.remove("m-fadeOut");
                    document.getElementById(idBloc).classList.add("m-fadeIn");
                    if(document.getElementById('head_menu-col-' + idBloc.slice(-1)) != null) {
                        if(i < 10) {
                            document.getElementById('head_menu-col-' + idBloc.slice(-1)).classList.add("active-menu");
                        } else {
                            document.getElementById('head_menu-col-' + i).classList.add("active-menu");
                        }
                    }
                }
            }
        }
    }
}

function chevronMobile(id) {

    document.getElementById("header-mobile").style.display = "block"
    document.getElementById("header-mobile").style.visibility = "visible"

    document.getElementById("header-mobile-remplacement").style.visibility = "hidden"
    document.getElementById("header-mobile-remplacement").style.display = "none"

    document.getElementById("divMenu").style.visibility = "hidden"
    document.getElementById("divMenu").style.display = "none"


    document.getElementById("divMenu").innerHTML = ''
}

function backgroundMenu() {
   document.getElementById('wrapper_navbar').style.backgroundColor = "#0077D4";
}

$(document).click(function(event) {
    if (!$(event.target).closest("#header").length) {
        document.getElementById('wrapper_navbar').style.backgroundColor = "";
        for (let i=0; i<13; i++) {
            if(document.getElementById('menu-col-' + i)) {
                document.getElementById('menu-col-' + i).classList.remove("m-fadeIn");
                document.getElementById('menu-col-' + i).classList.add("m-fadeOut");
                if(document.getElementById('head_menu-col-' + i) != null) {
                    document.getElementById('head_menu-col-' + i).classList.remove("active-menu");
                }    
            }
        }        
    }
});
function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
}

function clickBtnMenu() {
    if ($(window).width() < desktopWidth) {
        document.getElementById("header-mobile-remplacement").classList.remove("submenu-open")
        document.getElementById("divMenu").classList.remove("submenu-open")
        document.getElementById('header-mobile').style.background = '#0077D4'
        document.getElementById("body").classList.toggle("menu-open")

        document.getElementById("header-navigation").classList.toggle("d-lg-none")

        if (!document.getElementById("header-navigation").classList.contains('d-lg-none')) {
            document.getElementById("header-navigation").classList.add("d-block")
        } else {
            document.getElementById("header-navigation").classList.remove("d-block")
        }
    }
}

function clickBtnMenuReplace() {
    if ($(window).width() < desktopWidth) {
        document.getElementById("mobile-header").classList.remove("submenu-close")
        document.getElementById("divMenu").classList.remove("submenu-close")
        document.getElementById("header-mobile-remplacement").style.display = "none"
        document.getElementById("header-mobile-remplacement").style.visibility = "hidden"

        document.getElementById("divMenu").style.display = "none"
        document.getElementById("divMenu").style.visibility = "hidden"

        document.getElementById('header-mobile').style.background = '#0077D4'
        document.getElementById("header-mobile").style.visibility = "visible"
        document.getElementById("header-mobile").style.display = "block"

        document.getElementById("body").classList.toggle("menu-open")

        document.getElementById("header-navigation").classList.toggle("d-lg-none")

        if (!document.getElementById("header-navigation").classList.contains('d-lg-none')) {
            document.getElementById("header-navigation").classList.add("d-block")
        } else {
            document.getElementById("header-navigation").classList.remove("d-block")
        }
    }    
}

// reset filtre page news
function resetFilterNews() {
    let blocPays = document.getElementById('rch-pays')
    let labelResetPays = document.getElementById('savLabelCountry').value
    // remplacement
    blocPays.getElementsByTagName('span')[0].innerHTML = '-- ' + labelResetPays + ' --'

    let blocYear = document.getElementById('rch-year')
    let labelResetYear = document.getElementById('savLabelYear').value
    // remplacement
    blocYear.getElementsByTagName('span')[0].innerHTML = '-- ' + labelResetYear + ' --'

    // deselection des options dans formulaire
    let parentCountry = document.getElementsByClassName('mySelectCountryID')
    for (let itemCountry of parentCountry[0]) {
        itemCountry.selected = false
    }

    let parentYear = document.getElementsByClassName('mySelectyearCountry')
    for (let itemYear of parentYear[0]) {
        if(itemYear.attributes.selected) {
          itemYear.removeAttribute('selected')
        }
    }

    document.getElementsByTagName('yea').value = ''
    document.getElementsByTagName('year').value = ''
    document.getElementsByTagName(' country').value = ''

    // suppression des taggs
    document.getElementById('listOfCriteria').innerHTML = ''
    document.getElementById('listOfCriteriayear').innerHTML = ''
    document.getElementById('nbr_result').innerHTML = ''
 }

 // reset filtre page events
 function resetFilterEvents() {
    let selectYear = document.getElementById('categ')
    firstOpt = selectYear.options[0].text
    let blocYear = document.getElementById('uniform-categ')
    blocYear.firstChild.innerHTML = firstOpt
    selectYear.value = ""

    let selectCountry = document.getElementById('country')
    firstOpt = selectCountry.options[0].text
    let blocCountry = document.getElementById('uniform-country')
    blocCountry.firstChild.innerHTML = firstOpt
    selectCountry.value = ""

    let selectTheme = document.getElementById('keyword')
    firstOpt = selectTheme.options[0].text
    let blocTheme = document.getElementById('uniform-keyword')
    blocTheme.firstChild.innerHTML = firstOpt
    selectTheme.value = ""
 }

$(".arrowb:first-of-type").click(
    function() {
        let navwidth = $("#nav");
        //  navwidth.scrollLeft(navwidth.scrollLeft() - 200);
        let scroll = navwidth.scrollLeft() - 200
        navwidth.animate({
            scrollLeft: scroll
        }, 400);

    }
);
$(".arrowb:nth-of-type(2)").click(
    function() {
        let navwidth = $("#nav");
    //  navwidth.scrollLeft(navwidth.scrollLeft() + 200);
        let scroll = navwidth.scrollLeft() + 200
        navwidth.animate({
            scrollLeft: scroll
        }, 400);
    }
);

(function() {
    if(document.getElementById('nav') != null) {
        let width = document.getElementById('nav').offsetWidth
        let width2 = document.getElementById('nav').scrollWidth
        if(width2 <= width) {
            document.getElementById('right').style.display = 'none';
            document.getElementById('left').style.display = 'none';
        }
    }
 })();