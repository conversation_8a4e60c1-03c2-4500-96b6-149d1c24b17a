
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"2",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":false},{"function":"__c","vtp_value":"google.es"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_ip_mark","priority":13,"vtp_instanceOrder":0,"vtp_paramValue":"internal","vtp_ruleResult":["macro",1],"tag_id":8},{"function":"__ogt_1p_data_v2","priority":13,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":10},{"function":"__ccd_ga_first","priority":12,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":23},{"function":"__set_product_settings","priority":11,"vtp_instanceDestinationId":"G-06RDJQW848","vtp_foreignTldMacroResult":["macro",2],"vtp_isChinaVipRegionMacroResult":["macro",3],"tag_id":22},{"function":"__ccd_ga_regscope","priority":10,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":21},{"function":"__ccd_em_download","priority":9,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":20},{"function":"__ccd_em_form","priority":8,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":19},{"function":"__ccd_em_outbound_click","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":18},{"function":"__ccd_em_page_view","priority":6,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":17},{"function":"__ccd_em_scroll","priority":5,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":16},{"function":"__ccd_em_site_search","priority":4,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":15},{"function":"__ccd_em_video","priority":3,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":14},{"function":"__ccd_conversion_marking","priority":2,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":13},{"function":"__ccd_auto_redact","priority":1,"vtp_redactEmail":false,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":12},{"function":"__gct","vtp_trackingId":"G-06RDJQW848","vtp_sessionDuration":0,"tag_id":5},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-06RDJQW848","tag_id":11}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",14]],[["if",1],["add",0,1,15,13,12,11,10,9,8,7,6,5,4,3,2]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_auto_redact",[46,"a"],[50,"u",[46,"aI"],[36,[2,[15,"aI"],"replace",[7,[15,"t"],"\\$1"]]]],[50,"v",[46,"aI"],[52,"aJ",[30,["c",[15,"aI"]],[15,"aI"]]],[52,"aK",[7]],[65,"aL",[2,[15,"aJ"],"split",[7,""]],[46,[53,[52,"aM",[7,["u",[15,"aL"]]]],[52,"aN",["d",[15,"aL"]]],[22,[12,[15,"aN"],[45]],[46,[53,[36,["d",["u",[15,"aI"]]]]]]],[22,[21,[15,"aN"],[15,"aL"]],[46,[53,[2,[15,"aM"],"push",[7,[15,"aN"]]],[22,[21,[15,"aL"],[2,[15,"aL"],"toLowerCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toLowerCase",[7]]]]]]],[46,[22,[21,[15,"aL"],[2,[15,"aL"],"toUpperCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toUpperCase",[7]]]]]]]]]]]]],[22,[18,[17,[15,"aM"],"length"],1],[46,[53,[2,[15,"aK"],"push",[7,[0,[0,"(?:",[2,[15,"aM"],"join",[7,"|"]]],")"]]]]],[46,[53,[2,[15,"aK"],"push",[7,[16,[15,"aM"],0]]]]]]]]],[36,[2,[15,"aK"],"join",[7,""]]]],[50,"w",[46,"aI","aJ","aK"],[52,"aL",["y",[15,"aI"],[15,"aK"]]],[22,[28,[15,"aL"]],[46,[36,[15,"aI"]]]],[22,[28,[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[41,"aM"],[3,"aM",[17,[15,"aL"],"search"]],[65,"aN",[15,"aJ"],[46,[53,[52,"aO",[7,["u",[15,"aN"]],["v",[15,"aN"]]]],[65,"aP",[15,"aO"],[46,[53,[52,"aQ",[30,[16,[15,"s"],[15,"aP"]],[43,[15,"s"],[15,"aP"],["b",[0,[0,"([?&]",[15,"aP"]],"=)([^&]*)"],"gi"]]]],[3,"aM",[2,[15,"aM"],"replace",[7,[15,"aQ"],[0,"$1",[15,"q"]]]]]]]]]]],[22,[20,[15,"aM"],[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[22,[20,[16,[15,"aM"],0],"&"],[46,[3,"aM",[2,[15,"aM"],"substring",[7,1]]]]],[22,[21,[16,[15,"aM"],0],"?"],[46,[3,"aM",[0,"?",[15,"aM"]]]]],[22,[20,[15,"aM"],"?"],[46,[3,"aM",""]]],[43,[15,"aL"],"search",[15,"aM"]],[36,["z",[15,"aL"],[15,"aK"]]]],[50,"y",[46,"aI","aJ"],[22,[20,[15,"aJ"],[17,[15,"r"],"PATH"]],[46,[53,[3,"aI",[0,[15,"x"],[15,"aI"]]]]]],[36,["f",[15,"aI"]]]],[50,"z",[46,"aI","aJ"],[41,"aK"],[3,"aK",""],[22,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[46,[53,[41,"aL"],[3,"aL",""],[22,[30,[17,[15,"aI"],"username"],[17,[15,"aI"],"password"]],[46,[53,[3,"aL",[0,[15,"aL"],[0,[0,[0,[17,[15,"aI"],"username"],[39,[17,[15,"aI"],"password"],":",""]],[17,[15,"aI"],"password"]],"@"]]]]]],[3,"aK",[0,[0,[0,[17,[15,"aI"],"protocol"],"//"],[15,"aL"]],[17,[15,"aI"],"host"]]]]]],[36,[0,[0,[0,[15,"aK"],[17,[15,"aI"],"pathname"]],[17,[15,"aI"],"search"]],[17,[15,"aI"],"hash"]]]],[50,"aA",[46,"aI","aJ"],[41,"aK"],[3,"aK",[2,[15,"aI"],"replace",[7,[15,"m"],[15,"q"]]]],[22,[30,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[20,[15,"aJ"],[17,[15,"r"],"PATH"]]],[46,[53,[52,"aL",["y",[15,"aK"],[15,"aJ"]]],[22,[20,[15,"aL"],[44]],[46,[36,[15,"aK"]]]],[52,"aM",[17,[15,"aL"],"search"]],[52,"aN",[2,[15,"aM"],"replace",[7,[15,"n"],[15,"q"]]]],[22,[20,[15,"aM"],[15,"aN"]],[46,[36,[15,"aK"]]]],[43,[15,"aL"],"search",[15,"aN"]],[3,"aK",["z",[15,"aL"],[15,"aJ"]]]]]],[36,[15,"aK"]]],[50,"aB",[46,"aI"],[22,[20,[15,"aI"],[15,"p"]],[46,[53,[36,[17,[15,"r"],"PATH"]]]],[46,[22,[21,[2,[15,"o"],"indexOf",[7,[15,"aI"]]],[27,1]],[46,[53,[36,[17,[15,"r"],"URL"]]]],[46,[53,[36,[17,[15,"r"],"TEXT"]]]]]]]],[50,"aC",[46,"aI","aJ"],[41,"aK"],[3,"aK",false],[52,"aL",["e",[15,"aI"]]],[38,[15,"aL"],[46,"string","array","object"],[46,[5,[46,[52,"aM",["aA",[15,"aI"],[15,"aJ"]]],[22,[21,[15,"aI"],[15,"aM"]],[46,[53,[36,[15,"aM"]]]]],[4]]],[5,[46,[53,[41,"aN"],[3,"aN",0],[63,[7,"aN"],[23,[15,"aN"],[17,[15,"aI"],"length"]],[33,[15,"aN"],[3,"aN",[0,[15,"aN"],1]]],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]]],[4]]],[5,[46,[54,"aN",[15,"aI"],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]],[4]]]]],[36,[39,[15,"aK"],[15,"aI"],[44]]]],[50,"aH",[46,"aI","aJ"],[52,"aK",[30,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"I"]]],[7]]],[22,[20,[2,[15,"aK"],"indexOf",[7,[15,"aJ"]]],[27,1]],[46,[53,[2,[15,"aK"],"push",[7,[15,"aJ"]]]]]],[2,[15,"aI"],"setMetadata",[7,[17,[15,"h"],"I"],[15,"aK"]]]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","encodeUriComponent"]],[52,"e",["require","getType"]],[52,"f",["require","parseUrl"]],[52,"g",["require","internal.registerCcdCallback"]],[52,"h",[15,"__module_metadataSchema"]],[52,"i",[17,[15,"a"],"instanceDestinationId"]],[52,"j",[17,[15,"a"],"redactEmail"]],[52,"k",[17,[15,"a"],"redactQueryParams"]],[52,"l",[39,[15,"k"],[2,[15,"k"],"split",[7,","]],[7]]],[22,[1,[28,[17,[15,"l"],"length"]],[28,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["b","[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}","gi"]],[52,"n",["b",[0,"([A-Z0-9._-]|%25|%2B)+%40[A-Z0-9.-]","+\\.[A-Z]{2,}"],"gi"]],[52,"o",[7,"page_location","page_referrer","page_path","link_url","video_url","form_destination"]],[52,"p","page_path"],[52,"q","(redacted)"],[52,"r",[8,"TEXT",0,"URL",1,"PATH",2]],[52,"s",[8]],[52,"t",["b","([\\\\^$.|?*+(){}]|\\[|\\[)","g"]],[52,"x","http://."],[52,"aD",15],[52,"aE",16],[52,"aF",23],[52,"aG",24],["g",[15,"i"],[51,"",[7,"aI"],[22,[15,"j"],[46,[53,[52,"aJ",[2,[15,"aI"],"getHitKeys",[7]]],[65,"aK",[15,"aJ"],[46,[53,[22,[20,[15,"aK"],"_sst_parameters"],[46,[6]]],[52,"aL",[2,[15,"aI"],"getHitData",[7,[15,"aK"]]]],[22,[28,[15,"aL"]],[46,[6]]],[52,"aM",["aB",[15,"aK"]]],[52,"aN",["aC",[15,"aL"],[15,"aM"]]],[22,[21,[15,"aN"],[44]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aK"],[15,"aN"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"Y"]]],[15,"aF"],[15,"aD"]]]]]]]]]]]],[22,[17,[15,"l"],"length"],[46,[53,[65,"aJ",[15,"o"],[46,[53,[52,"aK",[2,[15,"aI"],"getHitData",[7,[15,"aJ"]]]],[22,[28,[15,"aK"]],[46,[6]]],[52,"aL",[39,[20,[15,"aJ"],[15,"p"]],[17,[15,"r"],"PATH"],[17,[15,"r"],"URL"]]],[52,"aM",["w",[15,"aK"],[15,"l"],[15,"aL"]]],[22,[21,[15,"aM"],[15,"aK"]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aJ"],[15,"aM"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"Y"]]],[15,"aG"],[15,"aE"]]]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"M"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"O"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"P"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"V"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"W"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"q",[46,"w"],[36,[1,[15,"w"],[21,[2,[2,[15,"w"],"toLowerCase",[7]],"match",[7,[15,"p"]]],[45]]]]],[50,"r",[46,"w"],[52,"x",[2,[17,[15,"w"],"pathname"],"split",[7,"."]]],[52,"y",[39,[18,[17,[15,"x"],"length"],1],[16,[15,"x"],[37,[17,[15,"x"],"length"],1]],""]],[36,[16,[2,[15,"y"],"split",[7,"/"]],0]]],[50,"s",[46,"w"],[36,[39,[12,[2,[17,[15,"w"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"w"],"pathname"],[0,"/",[17,[15,"w"],"pathname"]]]]],[50,"t",[46,"w"],[41,"x"],[3,"x",""],[22,[1,[15,"w"],[17,[15,"w"],"href"]],[46,[53,[41,"y"],[3,"y",[2,[17,[15,"w"],"href"],"indexOf",[7,"#"]]],[3,"x",[39,[23,[15,"y"],0],[17,[15,"w"],"href"],[2,[17,[15,"w"],"href"],"substring",[7,0,[15,"y"]]]]]]]],[36,[15,"x"]]],[50,"v",[46,"w"],[52,"x",[8]],[43,[15,"x"],[15,"i"],true],[43,[15,"x"],[15,"e"],true],[43,[15,"w"],"eventMetadata",[15,"x"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmDownloadActivity"]],[52,"e","speculative"],[52,"f","ae_block_downloads"],[52,"g","file_download"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","parseUrl"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"u",["l",[8,"checkValidation",true]]],[22,[28,[15,"u"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"w","x"],["x"],[52,"y",[8,"eventId",[16,[15,"w"],"gtm.uniqueEventId"],"deferrable",true]],[52,"z",[16,[15,"w"],"gtm.elementUrl"]],[52,"aA",["n",[15,"z"]]],[22,[28,[15,"aA"]],[46,[36]]],[52,"aB",["r",[15,"aA"]]],[22,[28,["q",[15,"aB"]]],[46,[53,[36]]]],[52,"aC",[8,"link_id",[16,[15,"w"],"gtm.elementId"],"link_url",["t",[15,"aA"]],"link_text",[16,[15,"w"],"gtm.elementText"],"file_name",["s",[15,"aA"]],"file_extension",[15,"aB"]]],["v",[15,"y"]],["o",["m"],[15,"g"],[15,"aC"],[15,"y"]]],[15,"u"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_form",[46,"a"],[50,"t",[46,"aA"],[52,"aB",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aB"],"event_usage",[7,8]],[43,[15,"aA"],[15,"m"],[15,"aB"]]],[50,"u",[46,"aA","aB"],[52,"aC",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aC"],[15,"l"],true],[43,[15,"aC"],[15,"g"],true],[22,[16,[15,"aB"],"gtm.formCanceled"],[46,[53,[43,[15,"aC"],[15,"n"],true]]]],[43,[15,"aA"],[15,"m"],[15,"aC"]]],[50,"v",[46,"aA","aB","aC"],[52,"aD",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[20,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aD"],"length"],0],[46,[53,["s",[15,"aD"],[15,"aA"],[15,"aB"],[15,"aC"]]]]],[52,"aE",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[21,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aE"],"length"],0],[46,[53,[43,[15,"aC"],"deferrable",true],["s",[15,"aE"],[15,"aA"],[15,"aB"],[15,"aC"]]]]]],[52,"b",["require","internal.isFeatureEnabled"]],[52,"c",[15,"__module_featureFlags"]],[52,"d",["require","internal.getProductSettingsParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmFormActivity"]],[52,"g","speculative"],[52,"h","ae_block_form"],[52,"i","form_submit"],[52,"j","form_start"],[52,"k","isRegistered"],[52,"l","em_event"],[52,"m","eventMetadata"],[52,"n","form_event_canceled"],[52,"o",[17,[15,"a"],"instanceDestinationId"]],[22,["d",[15,"o"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"A",[7,[17,[15,"a"],"instanceDestinationId"],[17,[15,"a"],"skipValidation"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"e"],"getItem",[7,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"k"],true]],[52,"p",["require","internal.addFormInteractionListener"]],[52,"q",["require","internal.addFormSubmitListener"]],[52,"r",["require","internal.getDestinationIds"]],[52,"s",["require","internal.sendGtagEvent"]],[52,"w",[8]],[52,"x",[51,"",[7,"aA","aB"],[22,[15,"aB"],[46,["aB"]]],[52,"aC",[16,[15,"aA"],"gtm.elementId"]],[22,[16,[15,"w"],[15,"aC"]],[46,[36]]],[43,[15,"w"],[15,"aC"],true],[52,"aD",[8,"form_id",[15,"aC"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"first_field_id",[16,[15,"aA"],"gtm.interactedFormFieldId"],"first_field_name",[16,[15,"aA"],"gtm.interactedFormFieldName"],"first_field_type",[16,[15,"aA"],"gtm.interactedFormFieldType"],"first_field_position",[16,[15,"aA"],"gtm.interactedFormFieldPosition"]]],[52,"aE",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aE"]],["u",[15,"aE"],[15,"aA"]],["v",[15,"j"],[15,"aD"],[15,"aE"]]]],[52,"y",["b",[17,[15,"c"],"DQ"]]],[52,"z",[51,"",[7,"aA","aB"],["x",[15,"aA"],[44]],[52,"aC",[8,"form_id",[16,[15,"aA"],"gtm.elementId"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"form_submit_text",[39,[15,"y"],[16,[15,"aA"],"gtm.formSubmitElementText"],[16,[15,"aA"],"gtm.formSubmitButtonText"]]]],[43,[15,"aC"],"event_callback",[15,"aB"]],[52,"aD",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aD"]],["u",[15,"aD"],[15,"aA"]],["v",[15,"i"],[15,"aC"],[15,"aD"]]]],[22,[15,"y"],[46,[53,[52,"aA",["require","internal.addDataLayerEventListener"]],[52,"aB",["require","internal.enableAutoEventOnFormSubmit"]],[52,"aC",["require","internal.enableAutoEventOnFormInteraction"]],[52,"aD",["aC"]],[22,[28,[15,"aD"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formInteract",[15,"x"],[15,"aD"]],[52,"aE",["aB",[8,"checkValidation",false,"waitForTags",false]]],[22,[28,[15,"aE"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formSubmit",[15,"z"],[15,"aE"]]]],[46,[53,["p",[15,"x"]],["q",[15,"z"],[8,"waitForCallbacks",false,"checkValidation",false]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"r",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",""],[22,[1,[15,"x"],[17,[15,"x"],"href"]],[46,[53,[41,"z"],[3,"z",[2,[17,[15,"x"],"href"],"indexOf",[7,"#"]]],[3,"y",[39,[23,[15,"z"],0],[17,[15,"x"],"href"],[2,[17,[15,"x"],"href"],"substring",[7,0,[15,"z"]]]]]]]],[36,[15,"y"]]],[50,"s",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",[17,[15,"x"],"hostname"]],[52,"z",[2,[15,"y"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"z"],[16,[15,"z"],0]],[46,[3,"y",[2,[15,"y"],"substring",[7,[17,[16,[15,"z"],0],"length"]]]]]],[36,[15,"y"]]],[50,"t",[46,"x"],[22,[28,[15,"x"]],[46,[36,false]]],[52,"y",[2,[17,[15,"x"],"hostname"],"toLowerCase",[7]]],[22,[28,[15,"y"]],[46,[53,[36,false]]]],[41,"z"],[3,"z",[2,["s",["p",["o"]]],"toLowerCase",[7]]],[41,"aA"],[3,"aA",[37,[17,[15,"y"],"length"],[17,[15,"z"],"length"]]],[22,[1,[18,[15,"aA"],0],[29,[2,[15,"z"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"aA"],[3,"aA",[37,[15,"aA"],1]]],[3,"z",[0,".",[15,"z"]]]]]],[22,[1,[19,[15,"aA"],0],[12,[2,[15,"y"],"indexOf",[7,[15,"z"],[15,"aA"]]],[15,"aA"]]],[46,[53,[36,false]]]],[36,true]],[50,"w",[46,"x"],[52,"y",[8]],[43,[15,"y"],[15,"i"],true],[43,[15,"y"],[15,"e"],true],[43,[15,"x"],"eventMetadata",[15,"y"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmOutboundClickActivity"]],[52,"e","speculative"],[52,"f","ae_block_outbound_click"],[52,"g","click"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.getRemoteConfigParameter"]],[52,"o",["require","getUrl"]],[52,"p",["require","parseUrl"]],[52,"q",["require","internal.sendGtagEvent"]],[52,"u",["n",[15,"j"],"cross_domain_conditions"]],[52,"v",["l",[8,"affiliateDomains",[15,"u"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"v"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"x","y"],[52,"z",["p",[16,[15,"x"],"gtm.elementUrl"]]],[22,[28,["t",[15,"z"]]],[46,[53,["y"],[36]]]],[52,"aA",[8,"link_id",[16,[15,"x"],"gtm.elementId"],"link_classes",[16,[15,"x"],"gtm.elementClasses"],"link_url",["r",[15,"z"]],"link_domain",["s",[15,"z"]],"outbound",true]],[43,[15,"aA"],"event_callback",[15,"y"]],[52,"aB",[8,"eventId",[16,[15,"x"],"gtm.uniqueEventId"],"deferrable",true]],["w",[15,"aB"]],["q",["m"],[15,"g"],[15,"aA"],[15,"aB"]]],[15,"v"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[17,[15,"f"],"G"],true],[43,[15,"s"],[17,[15,"f"],"AG"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmPageViewActivity"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g","ae_block_history"],[52,"h","page_view"],[52,"i","isRegistered"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"j"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnHistoryChange"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[8,"interval",1000,"useV2EventName",true]],[52,"p",["l",[15,"o"]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["k","gtm.historyChange-v2",[51,"",[7,"r","s"],["s"],[52,"t",[16,[15,"r"],"gtm.oldUrl"]],[22,[20,[16,[15,"r"],"gtm.newUrl"],[15,"t"]],[46,[36]]],[52,"u",[16,[15,"r"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"u"],"pushState"],[21,[15,"u"],"popstate"]],[21,[15,"u"],"replaceState"]],[46,[53,[36]]]],[52,"v",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"v"],"page_location",[16,[15,"r"],"gtm.newUrl"]],[43,[15,"v"],"page_referrer",[15,"t"]]]]],[52,"w",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"]]],[22,[21,[17,[15,"a"],"deferPageView"],false],[46,[53,[43,[15,"w"],"deferrable",true]]]],["q",[15,"w"]],["n",["m"],[15,"h"],[15,"v"],[15,"w"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"p",[46,"q"],[52,"r",[8]],[43,[15,"r"],[15,"i"],true],[43,[15,"r"],[15,"e"],true],[43,[15,"q"],"eventMetadata",[15,"r"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmScrollActivity"]],[52,"e","speculative"],[52,"f","ae_block_scroll"],[52,"g","scroll"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnScroll"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",["l",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"o"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.scrollDepth",[51,"",[7,"q","r"],["r"],[52,"s",[8,"eventId",[16,[15,"q"],"gtm.uniqueEventId"],"deferrable",true]],[52,"t",[8,"percent_scrolled",[16,[15,"q"],"gtm.scrollThreshold"]]],["p",[15,"s"]],["n",["m"],[15,"g"],[15,"t"],[15,"s"]]],[15,"o"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"A",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"B",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[15,"k"],true],[43,[15,"t"],[15,"e"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmVideoActivity"]],[52,"e","speculative"],[52,"f","ae_block_video"],[52,"g","video_start"],[52,"h","video_progress"],[52,"i","video_complete"],[52,"j","isRegistered"],[52,"k","em_event"],[52,"l",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"l"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"l"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["require","internal.addDataLayerEventListener"]],[52,"n",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"o",["require","internal.getDestinationIds"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",["n",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"j"],true]],["m","gtm.video",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.videoStatus"]],[41,"v"],[22,[20,[15,"u"],"start"],[46,[53,[3,"v",[15,"g"]]]],[46,[22,[20,[15,"u"],"progress"],[46,[53,[3,"v",[15,"h"]]]],[46,[22,[20,[15,"u"],"complete"],[46,[53,[3,"v",[15,"i"]]]],[46,[53,[36]]]]]]]],[52,"w",[8,"video_current_time",[16,[15,"s"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"s"],"gtm.videoDuration"],"video_percent",[16,[15,"s"],"gtm.videoPercent"],"video_provider",[16,[15,"s"],"gtm.videoProvider"],"video_title",[16,[15,"s"],"gtm.videoTitle"],"video_url",[16,[15,"s"],"gtm.videoUrl"],"visible",[16,[15,"s"],"gtm.videoVisible"]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"],"deferrable",true]],["r",[15,"x"]],["p",["o"],[15,"v"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"EG"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"AB"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"AL"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CS"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"AC"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AL"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AM"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_ip_mark",[46,"a"],[52,"b",["require","internal.appendRemoteConfigParameter"]],[52,"c",["require","internal.getDestinationIds"]],[52,"d",["require","internal.sortRemoteConfigParameters"]],[52,"e",[8,"instance_order",[17,[15,"a"],"instanceOrder"],"traffic_type",[17,[15,"a"],"paramValue"],"rule_result",[17,[15,"a"],"ruleResult"]]],[41,"f"],[3,"f",[30,["c"],[7]]],[65,"g",[15,"f"],[46,[53,["b",[15,"g"],"internal_traffic_results",[15,"e"]],["d",[15,"g"],"internal_traffic_results",[8,"sortKey","instance_order"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",0],[52,"c",1],[52,"d",2],[52,"e",3],[52,"f",4],[52,"g",5],[52,"h",6],[52,"i",7],[52,"j",8],[52,"k",9],[52,"l",10],[52,"m",13],[52,"n",16],[52,"o",17],[52,"p",19],[52,"q",20],[52,"r",21],[52,"s",22],[52,"t",23],[52,"u",24],[52,"v",25],[52,"w",26],[52,"x",27],[52,"y",28],[52,"z",29],[52,"aA",30],[52,"aB",31],[52,"aC",32],[52,"aD",33],[52,"aE",34],[52,"aF",35],[52,"aG",36],[52,"aH",37],[52,"aI",38],[52,"aJ",39],[52,"aK",40],[52,"aL",41],[52,"aM",47],[52,"aN",42],[52,"aO",43],[52,"aP",44],[52,"aQ",45],[52,"aR",46],[52,"aS",49],[52,"aT",52],[52,"aU",53],[52,"aV",54],[52,"aW",56],[52,"aX",59],[52,"aY",60],[52,"aZ",62],[52,"bA",63],[52,"bB",66],[52,"bC",68],[52,"bD",69],[52,"bE",71],[52,"bF",72],[52,"bG",75],[52,"bH",78],[52,"bI",83],[52,"bJ",84],[52,"bK",87],[52,"bL",88],[52,"bM",89],[52,"bN",90],[52,"bO",91],[52,"bP",92],[52,"bQ",93],[52,"bR",94],[52,"bS",95],[52,"bT",97],[52,"bU",100],[52,"bV",101],[52,"bW",102],[52,"bX",103],[52,"bY",104],[52,"bZ",106],[52,"cA",107],[52,"cB",108],[52,"cC",109],[52,"cD",111],[52,"cE",112],[52,"cF",113],[52,"cG",114],[52,"cH",115],[52,"cI",116],[52,"cJ",118],[52,"cK",119],[52,"cL",120],[52,"cM",121],[52,"cN",122],[52,"cO",123],[52,"cP",125],[52,"cQ",126],[52,"cR",127],[52,"cS",128],[52,"cT",129],[52,"cU",130],[52,"cV",131],[52,"cW",132],[52,"cX",133],[52,"cY",134],[52,"cZ",135],[52,"dA",136],[52,"dB",137],[52,"dC",138],[52,"dD",139],[52,"dE",140],[52,"dF",141],[52,"dG",142],[52,"dH",143],[52,"dI",144],[52,"dJ",145],[52,"dK",147],[52,"dL",148],[52,"dM",149],[52,"dN",152],[52,"dO",153],[52,"dP",154],[52,"dQ",155],[52,"dR",156],[52,"dS",157],[52,"dT",158],[52,"dU",159],[52,"dV",160],[52,"dW",162],[52,"dX",164],[52,"dY",165],[52,"dZ",168],[52,"eA",169],[52,"eB",170],[52,"eC",171],[52,"eD",174],[52,"eE",175],[52,"eF",176],[52,"eG",177],[52,"eH",178],[52,"eI",183],[52,"eJ",185],[52,"eK",186],[52,"eL",187],[52,"eM",188],[52,"eN",189],[52,"eO",190],[52,"eP",191],[52,"eQ",192],[52,"eR",193],[52,"eS",195],[52,"eT",196],[52,"eU",197],[52,"eV",198],[52,"eW",199],[52,"eX",200],[52,"eY",201],[52,"eZ",202],[52,"fA",203],[52,"fB",204],[52,"fC",205],[52,"fD",206],[52,"fE",207],[52,"fF",208],[52,"fG",209],[52,"fH",210],[52,"fI",211],[52,"fJ",212],[52,"fK",213],[36,[8,"E",[15,"f"],"F",[15,"g"],"EK",[15,"eL"],"EM",[15,"eN"],"EY",[15,"eZ"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"DY",[15,"dZ"],"O",[15,"p"],"EU",[15,"eV"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DV",[15,"dW"],"AJ",[15,"aK"],"AK",[15,"aL"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AL",[15,"aM"],"FA",[15,"fB"],"EP",[15,"eQ"],"AR",[15,"aS"],"EL",[15,"eM"],"AT",[15,"aU"],"AU",[15,"aV"],"AS",[15,"aT"],"AV",[15,"aW"],"AW",[15,"aX"],"EA",[15,"eB"],"FH",[15,"fI"],"EC",[15,"eD"],"EN",[15,"eO"],"AX",[15,"aY"],"DW",[15,"dX"],"FE",[15,"fF"],"EG",[15,"eH"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"EO",[15,"eP"],"BD",[15,"bE"],"BE",[15,"bF"],"FC",[15,"fD"],"BF",[15,"bG"],"FD",[15,"fE"],"BG",[15,"bH"],"EE",[15,"eF"],"BH",[15,"bI"],"ED",[15,"eE"],"BI",[15,"bJ"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"DX",[15,"dY"],"BS",[15,"bT"],"BT",[15,"bU"],"BU",[15,"bV"],"BJ",[15,"bK"],"FB",[15,"fC"],"EV",[15,"eW"],"BV",[15,"bW"],"BW",[15,"bX"],"ET",[15,"eU"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"EZ",[15,"fA"],"CE",[15,"cF"],"CF",[15,"cG"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"EW",[15,"eX"],"EF",[15,"eG"],"EQ",[15,"eR"],"CN",[15,"cO"],"EI",[15,"eJ"],"EB",[15,"eC"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"EJ",[15,"eK"],"CR",[15,"cS"],"FG",[15,"fH"],"CS",[15,"cT"],"CT",[15,"cU"],"FF",[15,"fG"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"DZ",[15,"eA"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"],"DG",[15,"dH"],"FI",[15,"fJ"],"EX",[15,"eY"],"DH",[15,"dI"],"DI",[15,"dJ"],"FJ",[15,"fK"],"ER",[15,"eS"],"ES",[15,"eT"],"B",[15,"c"],"D",[15,"e"],"C",[15,"d"],"DJ",[15,"dK"],"DK",[15,"dL"],"DL",[15,"dM"],"DM",[15,"dN"],"DN",[15,"dO"],"A",[15,"b"],"DO",[15,"dP"],"DP",[15,"dQ"],"DQ",[15,"dR"],"DR",[15,"dS"],"DS",[15,"dT"],"EH",[15,"eI"],"DT",[15,"dU"],"DU",[15,"dV"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","gclgb"],[52,"t","gclid"],[52,"u","gclgs"],[52,"v","gcllp"],[52,"w","gclst"],[52,"x","ads_data_redaction"],[52,"y","allow_ad_personalization_signals"],[52,"z","allow_direct_google_requests"],[52,"aA","allow_google_signals"],[52,"aB","auid"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_id"],[52,"aK","conversion_linker"],[52,"aL","conversion_api"],[52,"aM","cookie_deprecation"],[52,"aN","cookie_expires"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","shipping"],[52,"aX","engagement_time_msec"],[52,"aY","estimated_delivery_date"],[52,"aZ","event_developer_id_string"],[52,"bA","event"],[52,"bB","event_timeout"],[52,"bC","first_party_collection"],[52,"bD","gdpr_applies"],[52,"bE","google_analysis_params"],[52,"bF","_google_ng"],[52,"bG","gpp_sid"],[52,"bH","gpp_string"],[52,"bI","gsa_experiment_id"],[52,"bJ","gtag_event_feature_usage"],[52,"bK","iframe_state"],[52,"bL","ignore_referrer"],[52,"bM","is_passthrough"],[52,"bN","_lps"],[52,"bO","language"],[52,"bP","merchant_feed_label"],[52,"bQ","merchant_feed_language"],[52,"bR","merchant_id"],[52,"bS","new_customer"],[52,"bT","page_hostname"],[52,"bU","page_path"],[52,"bV","page_referrer"],[52,"bW","page_title"],[52,"bX","_platinum_request_status"],[52,"bY","restricted_data_processing"],[52,"bZ","screen_resolution"],[52,"cA","search_term"],[52,"cB","send_page_view"],[52,"cC","server_container_url"],[52,"cD","session_duration"],[52,"cE","session_engaged_time"],[52,"cF","session_id"],[52,"cG","_shared_user_id"],[52,"cH","topmost_url"],[52,"cI","transaction_id"],[52,"cJ","transport_url"],[52,"cK","update"],[52,"cL","_user_agent_architecture"],[52,"cM","_user_agent_bitness"],[52,"cN","_user_agent_full_version_list"],[52,"cO","_user_agent_mobile"],[52,"cP","_user_agent_model"],[52,"cQ","_user_agent_platform"],[52,"cR","_user_agent_platform_version"],[52,"cS","_user_agent_wow64"],[52,"cT","user_data_auto_latency"],[52,"cU","user_data_auto_meta"],[52,"cV","user_data_auto_multi"],[52,"cW","user_data_auto_selectors"],[52,"cX","user_data_auto_status"],[52,"cY","user_data_mode"],[52,"cZ","user_id"],[52,"dA","user_properties"],[52,"dB","us_privacy_string"],[52,"dC","value"],[52,"dD","_fpm_parameters"],[52,"dE","_host_name"],[52,"dF","_in_page_command"],[52,"dG","non_personalized_ads"],[52,"dH","conversion_label"],[52,"dI","page_location"],[52,"dJ","global_developer_id_string"],[52,"dK","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DG",[15,"dH"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AS",[15,"aT"],"AT",[15,"aU"],"AU",[15,"aV"],"AV",[15,"aW"],"AW",[15,"aX"],"AX",[15,"aY"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"DI",[15,"dJ"],"BD",[15,"bE"],"BE",[15,"bF"],"BF",[15,"bG"],"BG",[15,"bH"],"BH",[15,"bI"],"BI",[15,"bJ"],"BJ",[15,"bK"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"DH",[15,"dI"],"BT",[15,"bU"],"BU",[15,"bV"],"BV",[15,"bW"],"BW",[15,"bX"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"DJ",[15,"dK"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"CN",[15,"cO"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"CS",[15,"cT"],"CT",[15,"cU"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"B",[15,"c"],"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_text",[44]]],[2,[15,"j"],"setHitData",[7,"file_name",[44]]],[2,[15,"j"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_downloads"],[52,"f","file_download"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmFormActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k","l"],[22,[20,[15,"k"],[44]],[46,[53,[3,"k",[20,[2,[15,"j"],"indexOf",[7,"AW-"]],0]]]]],["c",[15,"j"],[51,"",[7,"m"],[52,"n",[2,[15,"m"],"getEventName",[7]]],[52,"o",[30,[20,[15,"n"],[15,"g"]],[20,[15,"n"],[15,"f"]]]],[22,[30,[28,[15,"o"]],[28,[2,[15,"m"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[22,[1,[28,[15,"k"]],[2,[15,"m"],"getMetadata",[7,[15,"h"]]]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"l"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_id",[44]]],[2,[15,"m"],"setHitData",[7,"form_name",[44]]],[2,[15,"m"],"setHitData",[7,"form_destination",[44]]],[2,[15,"m"],"setHitData",[7,"form_length",[44]]],[22,[20,[15,"n"],[15,"f"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_submit_text",[44]]]]],[46,[22,[20,[15,"n"],[15,"g"]],[46,[53,[2,[15,"m"],"setHitData",[7,"first_field_id",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_name",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_type",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_position",[44]]]]]]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_form"],[52,"f","form_submit"],[52,"g","form_start"],[52,"h","form_event_canceled"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_classes",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_domain",[44]]],[2,[15,"j"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_outbound_click"],[52,"f","click"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"Y"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"BU"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"BU"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"AG"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"O"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_scroll"],[52,"f","scroll"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k"],["c",[15,"j"],[51,"",[7,"l"],[52,"m",[2,[15,"l"],"getEventName",[7]]],[52,"n",[30,[30,[20,[15,"m"],[15,"f"]],[20,[15,"m"],[15,"g"]]],[20,[15,"m"],[15,"h"]]]],[22,[30,[28,[15,"n"]],[28,[2,[15,"l"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"l"],"abort",[7]],[36]]]],[2,[15,"l"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"k"]],[46,[53,[2,[15,"l"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"l"],"setHitData",[7,"video_duration",[44]]],[2,[15,"l"],"setHitData",[7,"video_percent",[44]]],[2,[15,"l"],"setHitData",[7,"video_provider",[44]]],[2,[15,"l"],"setHitData",[7,"video_title",[44]]],[2,[15,"l"],"setHitData",[7,"video_url",[44]]],[2,[15,"l"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"m",[46,"p","q","r"],[50,"w",[46,"y"],[52,"z",[16,[15,"l"],[15,"y"]]],[22,[28,[15,"z"]],[46,[36]]],[53,[41,"aA"],[3,"aA",0],[63,[7,"aA"],[23,[15,"aA"],[17,[15,"z"],"length"]],[33,[15,"aA"],[3,"aA",[0,[15,"aA"],1]]],[46,[53,[52,"aB",[16,[15,"z"],[15,"aA"]]],["t",[15,"s"],[17,[15,"aB"],"name"],[17,[15,"aB"],"value"]]]]]]],[50,"x",[46,"y"],[22,[30,[28,[15,"u"]],[21,[17,[15,"u"],"length"],2]],[46,[53,[36,false]]]],[41,"z"],[3,"z",[16,[15,"y"],[15,"v"]]],[22,[20,[15,"z"],[44]],[46,[53,[3,"z",[16,[15,"y"],[15,"u"]]]]]],[36,[28,[28,[15,"z"]]]]],[22,[28,[15,"q"]],[46,[36]]],[52,"s",[30,[17,[15,"p"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"t",["h",[15,"f"],[15,"r"]]],[52,"u",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"r"]]],["$0"]]],[52,"v",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"r"]]],["$0"]]],[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[15,"q"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[16,[15,"q"],[15,"y"]]],[22,[30,[17,[15,"z"],"disallowAllRegions"],["x",[17,[15,"z"],"disallowedRegions"]]],[46,[53,["w",[17,[15,"z"],"redactFieldGroup"]]]]]]]]]],[50,"n",[46,"p"],[52,"q",[8]],[22,[28,[15,"p"]],[46,[36,[15,"q"]]]],[52,"r",[2,[15,"p"],"split",[7,","]]],[53,[41,"s"],[3,"s",0],[63,[7,"s"],[23,[15,"s"],[17,[15,"r"],"length"]],[33,[15,"s"],[3,"s",[0,[15,"s"],1]]],[46,[53,[52,"t",[2,[16,[15,"r"],[15,"s"]],"trim",[7]]],[22,[28,[15,"t"]],[46,[6]]],[52,"u",[2,[15,"t"],"split",[7,"-"]]],[52,"v",[16,[15,"u"],0]],[52,"w",[39,[20,[17,[15,"u"],"length"],2],[15,"t"],[44]]],[22,[30,[28,[15,"v"]],[21,[17,[15,"v"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"w"],[44]],[30,[23,[17,[15,"w"],"length"],4],[18,[17,[15,"w"],"length"],6]]],[46,[53,[6]]]],[43,[15,"q"],[15,"t"],true]]]]],[36,[15,"q"]]],[50,"o",[46,"p"],[22,[28,[17,[15,"p"],"settingsTable"]],[46,[36,[7]]]],[52,"q",[8]],[53,[41,"r"],[3,"r",0],[63,[7,"r"],[23,[15,"r"],[17,[17,[15,"p"],"settingsTable"],"length"]],[33,[15,"r"],[3,"r",[0,[15,"r"],1]]],[46,[53,[52,"s",[16,[17,[15,"p"],"settingsTable"],[15,"r"]]],[52,"t",[17,[15,"s"],"redactFieldGroup"]],[22,[28,[16,[15,"l"],[15,"t"]]],[46,[6]]],[43,[15,"q"],[15,"t"],[8,"redactFieldGroup",[15,"t"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"u",[16,[15,"q"],[15,"t"]]],[22,[17,[15,"s"],"disallowAllRegions"],[46,[53,[43,[15,"u"],"disallowAllRegions",true],[6]]]],[43,[15,"u"],"disallowedRegions",["n",[17,[15,"s"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"q"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[41,"i"],[41,"j"],[41,"k"],[52,"l",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"m"],"B",[15,"o"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_auto_redact":{"2":true,"5":true}
,
"__ccd_conversion_marking":{"2":true,"5":true}
,
"__ccd_em_download":{"2":true,"5":true}
,
"__ccd_em_form":{"2":true,"5":true}
,
"__ccd_em_outbound_click":{"2":true,"5":true}
,
"__ccd_em_page_view":{"2":true,"5":true}
,
"__ccd_em_scroll":{"2":true,"5":true}
,
"__ccd_em_site_search":{"2":true,"5":true}
,
"__ccd_em_video":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__ccd_ga_regscope":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__ogt_ip_mark":{"2":true,"5":true}
,
"__set_product_settings":{"2":true,"5":true}


}
,"blob":{"1":"2"}
,"permissions":{
"__c":{}
,
"__ccd_auto_redact":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_form":{"access_template_storage":{},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.formInteract","gtm.formSubmit"]},"detect_form_submit_events":{"allowWaitForTags":""},"detect_form_interaction_events":{}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__ogt_ip_mark":{}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_auto_redact"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_form"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__ogt_ip_mark"
,
"__set_product_settings"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=da(this),fa=function(a,b){if(b)a:{for(var c=ea,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
fa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ha=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;
if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ma;a:{var na={a:!0},pa={};try{pa.__proto__=na;ma=pa.a;break a}catch(a){}ma=!1}ja=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=ja,ra=function(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Eq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},sa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ta=function(a){return a instanceof Array?a:sa(l(a))},va=function(a){return ua(a,a)},ua=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},wa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};fa("Object.assign",function(a){return a||wa});
var xa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ya=this||self,za=function(a,b){function c(){}c.prototype=b.prototype;a.Eq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Cr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Aa=function(a,b){this.type=a;this.data=b};var Ba=function(){this.map=new Map;this.C=new Set};k=Ba.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.C.has(a)||this.map.set(String(a),b)};k.Bl=function(a,b){this.set(a,b);this.C.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.C.has(a)||this.map.delete(String(a))};var Ca=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Ba.prototype.wa=function(){return Ca(this,1)};Ba.prototype.ac=function(){return Ca(this,2)};Ba.prototype.Jb=function(){return Ca(this,3)};var Da=function(){this.map={};this.C={}};k=Da.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};k.Bl=function(a,b){this.set(a,b);this.C["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ea=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Da.prototype.wa=function(){return Ea(this,1)};Da.prototype.ac=function(){return Ea(this,2)};Da.prototype.Jb=function(){return Ea(this,3)};var Fa=function(){};Fa.prototype.reset=function(){};var Ha=[],Ia={};function Ja(a){return Ha[a]===void 0?!1:Ha[a]};var Ka=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.ub=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=Ja(16)?new Ba:new Da};k=Ka.prototype;k.add=function(a,b){this.ub||this.values.set(a,b)};k.sh=function(a,b){this.ub||this.values.Bl(a,b)};k.set=function(a,b){this.ub||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};
k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.sb=function(){var a=new Ka(this.P,this);this.C&&a.Ob(this.C);a.Xc(this.H);a.Ld(this.N);return a};k.Dd=function(){return this.P};k.Ob=function(a){this.C=a};k.lm=function(){return this.C};k.Xc=function(a){this.H=a};k.kj=function(){return this.H};k.Ua=function(){this.ub=!0};k.Ld=function(a){this.N=a};k.tb=function(){return this.N};var La=function(a,b){this.ba=a;this.parent=b;this.P=this.H=void 0;this.ub=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.sh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.ub||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.ub||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.sb=function(){var a=new La(this.ba,this);this.H&&a.Ob(this.H);a.Xc(this.N);a.Ld(this.P);return a};k.Dd=function(){return this.ba};k.Ob=function(a){this.H=a};k.lm=function(){return this.H};k.Xc=function(a){this.N=a};k.kj=function(){return this.N};k.Ua=function(){this.ub=!0};k.Ld=function(a){this.P=a};k.tb=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.zm=a;this.dm=c===void 0?!1:c;this.debugInfo=[];this.C=b};ra(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=new Map;function Qa(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ra(a,e.value),c instanceof Aa);e=d.next());return c}function Ra(a,b){try{var c=l(b),d=c.next().value,e=sa(c),f,g=String(d);Ja(18)?(f=Pa.get(g))||(f=a.get(g)):f=a.get(g);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ta(e)))}catch(m){var h=a.lm();h&&h(m,b.context?{id:b[0],line:b.context.line}:null);throw m;}};var Ta=function(){this.H=new Fa;this.C=Ja(17)?new La(this.H):new Ka(this.H)};k=Ta.prototype;k.Dd=function(){return this.H};k.Ob=function(a){this.C.Ob(a)};k.Xc=function(a){this.C.Xc(a)};k.execute=function(a){return this.Mj([a].concat(ta(xa.apply(1,arguments))))};k.Mj=function(){for(var a,b=l(xa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ra(this.C,c.value);return a};
k.oo=function(a){var b=xa.apply(1,arguments),c=this.C.sb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ra(c,f.value);return d};k.Ua=function(){this.C.Ua()};var Ua=function(){this.Ca=!1;this.aa=new Da};k=Ua.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};k.Ua=function(){this.Ca=!0};k.ub=function(){return this.Ca};function Va(){for(var a=Wa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Za(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Wa,$a;function ab(a){Wa=Wa||Za();$a=$a||Va();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Wa[m],Wa[n],Wa[p],Wa[q])}return b.join("")}
function bb(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=$a[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Wa=Wa||Za();$a=$a||Va();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var cb={};function db(a,b){cb[a]=cb[a]||[];cb[a][b]=!0}function eb(){cb.GTAG_EVENT_FEATURE_CHANNEL=fb}function gb(a){var b=cb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return ab(c.join("")).replace(/\.+$/,"")}function hb(){for(var a=[],b=cb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function ib(){}function jb(a){return typeof a==="function"}function lb(a){return typeof a==="string"}function mb(a){return typeof a==="number"&&!isNaN(a)}function nb(a){return Array.isArray(a)?a:[a]}function ob(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function pb(a,b){if(!mb(a)||!mb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function qb(a,b){for(var c=new rb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function sb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function tb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function ub(a){return Math.round(Number(a))||0}function vb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function wb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function xb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function yb(){return new Date(Date.now())}function zb(){return yb().getTime()}var rb=function(){this.prefix="gtm.";this.values={}};rb.prototype.set=function(a,b){this.values[this.prefix+a]=b};rb.prototype.get=function(a){return this.values[this.prefix+a]};rb.prototype.contains=function(a){return this.get(a)!==void 0};
function Ab(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Bb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Cb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Db(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Eb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Fb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Gb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Hb=/^\w{1,9}$/;function Ib(a,b){a=a||{};b=b||",";var c=[];sb(a,function(d,e){Hb.test(d)&&e&&c.push(d)});return c.join(b)}function Jb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Kb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Lb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Mb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Nb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ta(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ob=globalThis.trustedTypes,Pb;function Qb(){var a=null;if(!Ob)return a;try{var b=function(c){return c};a=Ob.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Rb(){Pb===void 0&&(Pb=Qb());return Pb};var Tb=function(a){this.C=a};Tb.prototype.toString=function(){return this.C+""};function Ub(a){var b=a,c=Rb(),d=c?c.createScriptURL(b):b;return new Tb(d)}function Vb(a){if(a instanceof Tb)return a.C;throw Error("");};var Wb=va([""]),Xb=ua(["\x00"],["\\0"]),Yb=ua(["\n"],["\\n"]),Zb=ua(["\x00"],["\\u0000"]);function $b(a){return a.toString().indexOf("`")===-1}$b(function(a){return a(Wb)})||$b(function(a){return a(Xb)})||$b(function(a){return a(Yb)})||$b(function(a){return a(Zb)});var ac=function(a){this.C=a};ac.prototype.toString=function(){return this.C};var bc=function(a){this.Xp=a};function cc(a){return new bc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var dc=[cc("data"),cc("http"),cc("https"),cc("mailto"),cc("ftp"),new bc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function ec(a){var b;b=b===void 0?dc:b;if(a instanceof ac)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof bc&&d.Xp(a))return new ac(a)}}var fc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function hc(a){var b;if(a instanceof ac)if(a instanceof ac)b=a.C;else throw Error("");else b=fc.test(a)?a:void 0;return b};function ic(a,b){var c=hc(b);c!==void 0&&(a.action=c)};function jc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var kc=function(a){this.C=a};kc.prototype.toString=function(){return this.C+""};var mc=function(){this.C=lc[0].toLowerCase()};mc.prototype.toString=function(){return this.C};function nc(a,b){var c=[new mc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof mc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var oc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function pc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,qc=window.history,A=document,rc=navigator;function sc(){var a;try{a=rc.serviceWorker}catch(b){return}return a}var tc=A.currentScript,uc=tc&&tc.src;function vc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function wc(a){return(rc.userAgent||"").indexOf(a)!==-1}function xc(){return wc("Firefox")||wc("FxiOS")}function yc(){return(wc("GSA")||wc("GoogleApp"))&&(wc("iPhone")||wc("iPad"))}function zc(){return wc("Edg/")||wc("EdgA/")||wc("EdgiOS/")}
var Ac={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Bc={onload:1,src:1,width:1,height:1,style:1};function Cc(a,b,c){b&&sb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Dc(a,b,c,d,e){var f=A.createElement("script");Cc(f,d,Ac);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Ub(pc(a));f.src=Vb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function Ec(){if(uc){var a=uc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Fc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);Cc(g,c,Bc);d&&sb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Gc(a,b,c,d){return Hc(a,b,c,d)}function Ic(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Jc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Kc(a){x.setTimeout(a,0)}function Lc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Mc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Nc(a){var b=A.createElement("div"),c=b,d,e=pc("A<div>"+a+"</div>"),f=Rb(),g=f?f.createHTML(e):e;d=new kc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof kc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Oc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Pc(a,b,c){var d;try{d=rc.sendBeacon&&rc.sendBeacon(a)}catch(e){db("TAGGING",15)}d?b==null||b():Hc(a,b,c)}function Qc(a,b){try{return rc.sendBeacon(a,b)}catch(c){db("TAGGING",15)}return!1}var Rc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Tc(a,b,c,d,e){if(Uc()){var f=Object.assign({},Rc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Hh)return e==null||e(),!1;if(b){var h=
Qc(a,b);h?d==null||d():e==null||e();return h}Vc(a,d,e);return!0}function Uc(){return typeof x.fetch==="function"}function Wc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Xc(){var a=x.performance;if(a&&jb(a.now))return a.now()}
function Yc(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Zc(){return x.performance||void 0}function $c(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Hc=function(a,b,c,d){var e=new Image(1,1);Cc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Vc=Pc;function ad(a,b){return this.evaluate(a)&&this.evaluate(b)}function bd(a,b){return this.evaluate(a)===this.evaluate(b)}function cd(a,b){return this.evaluate(a)||this.evaluate(b)}function dd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function ed(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function fd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ua&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var gd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,hd=function(a){if(a==null)return String(a);var b=gd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},id=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},jd=function(a){if(!a||hd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!id(a,"constructor")&&!id(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
id(a,b)},kd=function(a,b){var c=b||(hd(a)=="array"?[]:{}),d;for(d in a)if(id(a,d)){var e=a[d];hd(e)=="array"?(hd(c[d])!="array"&&(c[d]=[]),c[d]=kd(e,c[d])):jd(e)?(jd(c[d])||(c[d]={}),c[d]=kd(e,c[d])):c[d]=e}return c};function ld(a){if(a==void 0||Array.isArray(a)||jd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function md(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var nd=function(a){a=a===void 0?[]:a;this.aa=new Da;this.values=[];this.Ca=!1;for(var b in a)a.hasOwnProperty(b)&&(md(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=nd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof nd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ca)if(a==="length"){if(!md(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else md(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():md(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.wa=function(){for(var a=this.aa.wa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Jb=function(){for(var a=this.aa.Jb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){md(a)?delete this.values[Number(a)]:this.Ca||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ta(xa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=xa.apply(2,arguments);return b===void 0&&c.length===0?new nd(this.values.splice(a)):new nd(this.values.splice.apply(this.values,[a,b||0].concat(ta(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ta(xa.apply(0,arguments)))};k.has=function(a){return md(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ua=function(){this.Ca=!0;Object.freeze(this.values)};k.ub=function(){return this.Ca};
function od(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var pd=function(a,b){this.functionName=a;this.Ae=b;this.aa=new Da;this.Ca=!1};k=pd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new nd(this.wa())};k.invoke=function(a){return this.Ae.call.apply(this.Ae,[new qd(this,a)].concat(ta(xa.apply(1,arguments))))};k.Mb=function(a){var b=xa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ta(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};
k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};k.Ua=function(){this.Ca=!0};k.ub=function(){return this.Ca};var rd=function(a,b){pd.call(this,a,b)};ra(rd,pd);var sd=function(a,b){pd.call(this,a,b)};ra(sd,pd);var qd=function(a,b){this.Ae=a;this.K=b};
qd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ra(b,a):a};qd.prototype.getName=function(){return this.Ae.getName()};qd.prototype.Dd=function(){return this.K.Dd()};var td=function(){this.map=new Map};td.prototype.set=function(a,b){this.map.set(a,b)};td.prototype.get=function(a){return this.map.get(a)};var ud=function(){this.keys=[];this.values=[]};ud.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};ud.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function vd(){try{return Map?new td:new ud}catch(a){return new ud}};var wd=function(a){if(a instanceof wd)return a;if(ld(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};wd.prototype.getValue=function(){return this.value};wd.prototype.toString=function(){return String(this.value)};var yd=function(a){this.promise=a;this.Ca=!1;this.aa=new Da;this.aa.set("then",xd(this));this.aa.set("catch",xd(this,!0));this.aa.set("finally",xd(this,!1,!0))};k=yd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};
var xd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new rd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof rd||(d=void 0);e instanceof rd||(e=void 0);var f=this.K.sb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new wd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new yd(h)})};yd.prototype.Ua=function(){this.Ca=!0};yd.prototype.ub=function(){return this.Ca};function zd(a,b,c){var d=vd(),e=function(g,h){for(var m=g.wa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof nd){var m=[];d.set(g,m);for(var n=g.wa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof yd)return g.promise.then(function(v){return zd(v,b,1)},function(v){return Promise.reject(zd(v,b,1))});if(g instanceof Ua){var q={};d.set(g,q);e(g,q);return q}if(g instanceof rd){var r=function(){for(var v=
xa.apply(0,arguments),u=[],w=0;w<v.length;w++)u[w]=Ad(v[w],b,c);var y=new Ka(b?b.Dd():new Fa);b&&y.Ld(b.tb());return f(g.invoke.apply(g,[y].concat(ta(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof wd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Ad(a,b,c){var d=vd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||tb(g)){var m=new nd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(jd(g)){var p=new Ua;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new rd("",function(){for(var v=xa.apply(0,arguments),u=[],w=0;w<v.length;w++)u[w]=zd(this.evaluate(v[w]),b,c);return f(this.K.kj()(g,g,u))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new wd(g)};return f(a)};var Bd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof nd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new nd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new nd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new nd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ta(xa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=od(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new nd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=od(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ta(xa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ta(xa.apply(1,arguments)))}};var Cd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Dd=new Aa("break"),Ed=new Aa("continue");function Fd(a,b){return this.evaluate(a)+this.evaluate(b)}function Gd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Hd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof nd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=zd(f.get(0));try{return d.toString(h)}catch(u){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Cd.hasOwnProperty(e)){var m=2;m=1;var n=zd(f,void 0,m);return Ad(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof nd){if(d.has(e)){var p=d.get(String(e));if(p instanceof rd){var q=od(f);return p.invoke.apply(p,[this.K].concat(ta(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(Bd.supportedMethods.indexOf(e)>=
0){var r=od(f);return Bd[e].call.apply(Bd[e],[d,this.K].concat(ta(r)))}}if(d instanceof rd||d instanceof Ua||d instanceof yd){if(d.has(e)){var t=d.get(e);if(t instanceof rd){var v=od(f);return t.invoke.apply(t,[this.K].concat(ta(v)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof rd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof wd&&e==="toString")return d.toString();throw Oa(Error("TypeError: Object has no '"+
e+"' property."));}function Id(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Jd(){var a=xa.apply(0,arguments),b=this.K.sb(),c=Qa(b,a);if(c instanceof Aa)return c}function Kd(){return Dd}function Ld(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Aa)return d}}
function Md(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.sh(c,d)}}}function Nd(){return Ed}function Od(a,b){return new Aa(a,this.evaluate(b))}function Pd(a,b){for(var c=xa.apply(2,arguments),d=new nd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ta(c));this.K.add(a,this.evaluate(g))}function Qd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Rd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof wd,f=d instanceof wd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Sd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Td(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Qa(f,d);if(g instanceof Aa){if(g.type==="break")break;if(g.type==="return")return g}}}
function Ud(a,b,c){if(typeof b==="string")return Td(a,function(){return b.length},function(f){return f},c);if(b instanceof Ua||b instanceof yd||b instanceof nd||b instanceof rd){var d=b.wa(),e=d.length;return Td(a,function(){return e},function(f){return d[f]},c)}}function Vd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Ud(function(h){g.set(d,h);return g},e,f)}
function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Ud(function(h){var m=g.sb();m.sh(d,h);return m},e,f)}function Xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Ud(function(h){var m=g.sb();m.add(d,h);return m},e,f)}function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){g.set(d,h);return g},e,f)}
function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){var m=g.sb();m.sh(d,h);return m},e,f)}function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){var m=g.sb();m.add(d,h);return m},e,f)}
function Zd(a,b,c){if(typeof b==="string")return Td(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof nd)return Td(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function be(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var v=f.get(t);r.add(v,q.get(v))}}var f=this.evaluate(a);if(!(f instanceof nd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.sb();for(e(g,m);Ra(m,b);){var n=Qa(m,h);if(n instanceof Aa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.sb();e(m,p);Ra(p,c);m=p}}
function ce(a,b){var c=xa.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof nd))throw Error("Error: non-List value given for Fn argument names.");return new rd(a,function(){return function(){var f=xa.apply(0,arguments),g=d.sb();g.tb()===void 0&&g.Ld(this.K.tb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new nd(h));var r=Qa(g,c);if(r instanceof Aa)return r.type===
"return"?r.data:r}}())}function de(a){var b=this.evaluate(a),c=this.K;if(ee&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function fe(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ua||d instanceof yd||d instanceof nd||d instanceof rd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:md(e)&&(c=d[e]);else if(d instanceof wd)return;return c}function ge(a,b){return this.evaluate(a)>this.evaluate(b)}function he(a,b){return this.evaluate(a)>=this.evaluate(b)}
function ie(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof wd&&(c=c.getValue());d instanceof wd&&(d=d.getValue());return c===d}function je(a,b){return!ie.call(this,a,b)}function ke(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Qa(this.K,d);if(e instanceof Aa)return e}var ee=!1;
function le(a,b){return this.evaluate(a)<this.evaluate(b)}function me(a,b){return this.evaluate(a)<=this.evaluate(b)}function ne(){for(var a=new nd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function oe(){for(var a=new Ua,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function pe(a,b){return this.evaluate(a)%this.evaluate(b)}
function qe(a,b){return this.evaluate(a)*this.evaluate(b)}function re(a){return-this.evaluate(a)}function se(a){return!this.evaluate(a)}function te(a,b){return!Rd.call(this,a,b)}function ue(){return null}function ve(a,b){return this.evaluate(a)||this.evaluate(b)}function we(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function xe(a){return this.evaluate(a)}function ye(){return xa.apply(0,arguments)}function ze(a){return new Aa("return",this.evaluate(a))}
function Ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof rd||d instanceof nd||d instanceof Ua)&&d.set(String(e),f);return f}function Be(a,b){return this.evaluate(a)-this.evaluate(b)}
function Ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Aa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Aa&&(g.type==="return"||g.type==="continue")))return g}
function De(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Ee(a){var b=this.evaluate(a);return b instanceof rd?"function":typeof b}function Fe(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Ge(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Qa(this.K,e);if(f instanceof Aa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Qa(this.K,e);if(g instanceof Aa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function He(a){return~Number(this.evaluate(a))}function Ie(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Je(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ke(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Le(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ne(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Oe(){}
function Pe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Aa)return d}catch(h){if(!(h instanceof Na&&h.dm))throw h;var e=this.K.sb();a!==""&&(h instanceof Na&&(h=h.zm),e.add(a,new wd(h)));var f=this.evaluate(c),g=Qa(e,f);if(g instanceof Aa)return g}}function Qe(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.dm))throw f;c=f}var e=this.evaluate(b);if(e instanceof Aa)return e;if(c)throw c;if(d instanceof Aa)return d};var Se=function(){this.C=new Ta;Re(this)};Se.prototype.execute=function(a){return this.C.Mj(a)};var Re=function(a){var b=function(c,d){var e=new sd(String(c),d);e.Ua();var f=String(c);a.C.C.set(f,e);Pa.set(f,e)};b("map",oe);b("and",ad);b("contains",dd);b("equals",bd);b("or",cd);b("startsWith",ed);b("variable",fd)};Se.prototype.Ob=function(a){this.C.Ob(a)};var Ue=function(){this.H=!1;this.C=new Ta;Te(this);this.H=!0};Ue.prototype.execute=function(a){return Ve(this.C.Mj(a))};var We=function(a,b,c){return Ve(a.C.oo(b,c))};Ue.prototype.Ua=function(){this.C.Ua()};
var Te=function(a){var b=function(c,d){var e=String(c),f=new sd(e,d);f.Ua();a.C.C.set(e,f);Pa.set(e,f)};b(0,Fd);b(1,Gd);b(2,Hd);b(3,Id);b(56,Le);b(57,Ie);b(58,He);b(59,Ne);b(60,Je);b(61,Ke);b(62,Me);b(53,Jd);b(4,Kd);b(5,Ld);b(68,Pe);b(52,Md);b(6,Nd);b(49,Od);b(7,ne);b(8,oe);b(9,Ld);b(50,Pd);b(10,Qd);b(12,Rd);b(13,Sd);b(67,Qe);b(51,ce);b(47,Vd);b(54,Wd);b(55,Xd);b(63,be);b(64,Yd);b(65,$d);b(66,ae);b(15,de);b(16,fe);b(17,fe);b(18,ge);b(19,he);b(20,ie);b(21,je);b(22,ke);b(23,le);b(24,me);b(25,pe);b(26,
qe);b(27,re);b(28,se);b(29,te);b(45,ue);b(30,ve);b(32,we);b(33,we);b(34,xe);b(35,xe);b(46,ye);b(36,ze);b(43,Ae);b(37,Be);b(38,Ce);b(39,De);b(40,Ee);b(44,Oe);b(41,Fe);b(42,Ge)};Ue.prototype.Dd=function(){return this.C.Dd()};Ue.prototype.Ob=function(a){this.C.Ob(a)};Ue.prototype.Xc=function(a){this.C.Xc(a)};
function Ve(a){if(a instanceof Aa||a instanceof rd||a instanceof nd||a instanceof Ua||a instanceof yd||a instanceof wd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Xe=function(a){this.message=a};function Ye(a){a.Jr=!0;return a};var Ze=Ye(function(a){return typeof a==="string"});function $e(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Xe("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function af(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var bf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function cf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+$e(e)+c}a<<=2;d||(a|=32);return c=""+$e(a|b)+c}
function df(a,b){var c;var d=a.Wc,e=a.Uc;d===void 0?c="":(e||(e=0),c=""+cf(1,1)+$e(d<<2|e));var f=a.bm,g=a.Ro,h="4"+c+(f?""+cf(2,1)+$e(f):"")+(g?""+cf(12,1)+$e(g):""),m,n=a.Nj;m=n&&bf.test(n)?""+cf(3,2)+n:"";var p,q=a.Jj;p=q?""+cf(4,1)+$e(q):"";var r;var t=a.ctid;if(t&&b){var v=cf(5,3),u=t.split("-"),w=u[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=u[1];r=""+v+$e(1+y.length)+(a.rm||0)+y}}else r="";var z=a.Dq,C=a.xe,D=a.Ma,F=a.Nr,H=h+m+p+r+(z?""+cf(6,1)+$e(z):"")+(C?""+cf(7,3)+$e(C.length)+
C:"")+(D?""+cf(8,3)+$e(D.length)+D:"")+(F?""+cf(9,3)+$e(F.length)+F:""),M;var S=a.fm;S=S===void 0?{}:S;for(var ca=[],U=l(Object.keys(S)),oa=U.next();!oa.done;oa=U.next()){var T=oa.value;ca[Number(T)]=S[T]}if(ca.length){var Z=cf(10,3),Y;if(ca.length===0)Y=$e(0);else{for(var V=[],ka=0,ia=!1,la=0;la<ca.length;la++){ia=!0;var Sa=la%6;ca[la]&&(ka|=1<<Sa);Sa===5&&(V.push($e(ka)),ka=0,ia=!1)}ia&&V.push($e(ka));Y=V.join("")}var Ya=Y;M=""+Z+$e(Ya.length)+Ya}else M="";var Ga=a.Am,Xa=a.tq;return H+M+(Ga?""+
cf(11,3)+$e(Ga.length)+Ga:"")+(Xa?""+cf(13,3)+$e(Xa.length)+Xa:"")};var ef=function(){function a(b){return{toString:function(){return b}}}return{Ym:a("consent"),ek:a("convert_case_to"),fk:a("convert_false_to"),gk:a("convert_null_to"),hk:a("convert_true_to"),ik:a("convert_undefined_to"),Pq:a("debug_mode_metadata"),Ra:a("function"),Ei:a("instance_name"),ro:a("live_only"),so:a("malware_disabled"),METADATA:a("metadata"),wo:a("original_activity_id"),kr:a("original_vendor_template_id"),jr:a("once_on_load"),vo:a("once_per_event"),Dl:a("once_per_load"),mr:a("priority_override"),
rr:a("respected_consent_types"),Ml:a("setup_tags"),qh:a("tag_id"),Ul:a("teardown_tags")}}();var Bf;var Cf=[],Df=[],Ef=[],Ff=[],Gf=[],Hf,Jf,Kf;function Lf(a){Kf=Kf||a}
function Mf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Cf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Ff.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Ef.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Nf(p[r])}Df.push(p)}}
function Nf(a){}var Of,Pf=[],Qf=[];function Rf(a,b){var c={};c[ef.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Sf(a,b,c){try{return Jf(Tf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Tf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Uf(a[e],b,c));return d},Uf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Uf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Cf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[ef.Ei]);try{var m=Tf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Vf(m,{event:b,index:f,type:2,
name:h});Of&&(d=Of.Uo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Uf(a[n],b,c)]=Uf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Uf(a[q],b,c);Kf&&(p=p||Kf.Up(r));d.push(r)}return Kf&&p?Kf.Zo(d):d.join("");case "escape":d=Uf(a[1],b,c);if(Kf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Kf.Vp(a))return Kf.lq(d);d=String(d);for(var t=2;t<a.length;t++)mf[a[t]]&&(d=mf[a[t]](d));return d;
case "tag":var v=a[1];if(!Ff[v])throw Error("Unable to resolve tag reference "+v+".");return{jm:a[2],index:v};case "zb":var u={arg0:a[2],arg1:a[3],ignore_case:a[5]};u[ef.Ra]=a[1];var w=Sf(u,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Vf=function(a,b){var c=a[ef.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Hf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Pf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Eb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Cf[q];break;case 1:r=Ff[q];break;default:n="";break a}var t=r&&r[ef.Ei];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var v,u,w;if(f&&Qf.indexOf(c)===-1){Qf.push(c);
var y=zb();v=e(g);var z=zb()-y,C=zb();u=Bf(c,h,b);w=z-(zb()-C)}else if(e&&(v=e(g)),!e||f)u=Bf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),ld(v)?(Array.isArray(v)?Array.isArray(u):jd(v)?jd(u):typeof v==="function"?typeof u==="function":v===u)||d.reportMacroDiscrepancy(d.id,c):v!==u&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?v:u};var Wf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ra(Wf,Error);Wf.prototype.getMessage=function(){return this.message};function Xf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Xf(a[c],b[c])}};function Yf(){return function(a,b){var c;var d=Zf;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Zf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)mb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function $f(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=ag(a),f=0;f<Df.length;f++){var g=Df[f],h=bg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Ff.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function bg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function ag(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Sf(Ef[c],a));return b[c]}};function cg(a,b){b[ef.ek]&&typeof a==="string"&&(a=b[ef.ek]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(ef.gk)&&a===null&&(a=b[ef.gk]);b.hasOwnProperty(ef.ik)&&a===void 0&&(a=b[ef.ik]);b.hasOwnProperty(ef.hk)&&a===!0&&(a=b[ef.hk]);b.hasOwnProperty(ef.fk)&&a===!1&&(a=b[ef.fk]);return a};var dg=function(){this.C={}},fg=function(a,b){var c=eg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ta(xa.apply(0,arguments)))})};function gg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Wf(c,d,g);}}
function hg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ta(xa.apply(1,arguments))));gg(e,b,d,g);gg(f,b,d,g)}}}};var lg=function(){var a=data.permissions||{},b=ig.ctid,c=this;this.H={};this.C=new dg;var d={},e={},f=hg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ta(xa.apply(1,arguments)))):{}});sb(a,function(g,h){function m(p){var q=xa.apply(1,arguments);if(!n[p])throw jg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ta(q)))}var n={};sb(h,function(p,q){var r=kg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Zl&&!e[p]&&(e[p]=r.Zl)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw jg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var v=e[p];v&&v.apply(null,[m].concat(ta(t.slice(1))))}})},mg=function(a){return eg.H[a]||function(){}};
function kg(a,b){var c=Rf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=jg;try{return Vf(c)}catch(d){return{assert:function(e){throw new Wf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Wf(a,{},"Permission "+a+" is unknown.");}}}}function jg(a,b,c){return new Wf(a,b,c)};var ng=!1;var og={};og.Rm=vb('');og.lp=vb('');
var sg=function(a){var b={},c=0;sb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(pg.hasOwnProperty(e))b[pg[e]]=g;else if(qg.hasOwnProperty(e)){var h=qg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=rg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];sb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
pg={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},qg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},rg=["ca",
"c2","c3","c4","c5"];function tg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var ug=[];function vg(a){switch(a){case 1:return 0;case 38:return 13;case 53:return 1;case 54:return 2;case 52:return 7;case 211:return 18;case 75:return 3;case 103:return 14;case 197:return 15;case 203:return 16;case 114:return 12;case 115:return 4;case 116:return 5;case 209:return 17;case 135:return 9;case 136:return 6}}function wg(a,b){ug[a]=b;var c=vg(a);c!==void 0&&(Ha[c]=b)}function B(a){wg(a,!0)}
B(39);B(34);B(35);B(36);
B(56);B(145);B(153);B(144);B(120);
B(5);B(111);B(139);B(87);
B(92);B(159);B(132);
B(20);B(72);B(113);
B(154);B(116);B(143);
wg(23,!1),B(24);
Ia[1]=tg('1',6E4);Ia[3]=tg('10',1);Ia[2]=tg('',50);B(29);
xg(26,25);
B(37);B(9);
B(91);B(123);
B(158);B(71);B(136);B(127);B(27);B(69);B(135);
B(95);B(38);B(103);B(112);
B(63);
B(152);
B(101);B(122);B(121);
B(108);
B(134);B(115);B(31);
B(22);B(97);B(19);B(28);
B(90);
B(59);B(13);

B(175);B(176);
B(183);B(185);B(187);B(192);
B(201);


function E(a){return!!ug[a]}function xg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};
var yg=function(){this.events=[];this.C="";this.ra={};this.baseUrl="";this.N=0;this.P=this.H=!1;this.endpoint=0;E(89)&&(this.P=!0)};yg.prototype.add=function(a){return this.R(a)?(this.events.push(a),this.C=a.H,this.ra=a.ra,this.baseUrl=a.baseUrl,this.N+=a.P,this.H=a.N,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ba=a.eventId,this.ka=a.priorityId,!0):!1};yg.prototype.R=function(a){return this.events.length?this.events.length>=20||a.P+this.N>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.N&&this.Ba(a):!0};yg.prototype.Ba=function(a){var b=this;if(!this.P)return this.C===a.H;var c=Object.keys(this.ra);return c.length===Object.keys(a.ra).length&&c.every(function(d){return a.ra.hasOwnProperty(d)&&String(b.ra[d])===String(a.ra[d])})};var zg={},Ag=(zg.uaa=!0,zg.uab=!0,zg.uafvl=!0,zg.uamb=!0,zg.uam=!0,zg.uap=!0,zg.uapv=!0,zg.uaw=!0,zg);
var Dg=function(a,b){var c=a.events;if(c.length===1)return Bg(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)sb(c[f].Md,function(t,v){v!=null&&(e[t]=e[t]||{},e[t][String(v)]=e[t][String(v)]+1||1)});var g={};sb(e,function(t,v){var u,w=-1,y=0;sb(v,function(z,C){y+=C;var D=(z.length+t.length+2)*(C-1);D>w&&(u=z,w=D)});y===c.length&&(g[t]=u)});Cg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={Aj:void 0},p++){var q=[];n.Aj={};sb(c[p].Md,function(t){return function(v,
u){g[v]!==""+u&&(t.Aj[v]=u)}}(n));c[p].C&&q.push(c[p].C);Cg(n.Aj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Bg=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);Cg(a.Md,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Cg=function(a,b){sb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Eg=function(a){var b=[];sb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Fg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.ra=a.ra;this.Md=a.Md;this.ij=a.ij;this.N=d;this.H=Eg(a.ra);this.C=Eg(a.ij);this.P=this.C.length;if(e&&this.P>16384)throw Error("EVENT_TOO_LARGE");};
var Ig=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Gg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Hg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Eb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Hg=/^[a-z$_][\w-$]*$/i,Gg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Jg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Kg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Lg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Mg=new rb;function Ng(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Mg.get(e);f||(f=new RegExp(b,d),Mg.set(e,f));return f.test(a)}catch(g){return!1}}function Og(a,b){return String(a).indexOf(String(b))>=0}
function Pg(a,b){return String(a)===String(b)}function Qg(a,b){return Number(a)>=Number(b)}function Rg(a,b){return Number(a)<=Number(b)}function Sg(a,b){return Number(a)>Number(b)}function Tg(a,b){return Number(a)<Number(b)}function Ug(a,b){return Eb(String(a),String(b))};var ah=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,bh={Fn:"function",PixieMap:"Object",List:"Array"};
function ch(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=ah.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof rd?n="Fn":m instanceof nd?n="List":m instanceof Ua?n="PixieMap":m instanceof yd?n="PixiePromise":m instanceof wd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((bh[n]||n)+", which does not match required type ")+
((bh[h]||h)+"."));}}}function G(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof rd?d.push("function"):g instanceof nd?d.push("Array"):g instanceof Ua?d.push("Object"):g instanceof yd?d.push("Promise"):g instanceof wd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function dh(a){return a instanceof Ua}function eh(a){return dh(a)||a===null||fh(a)}
function gh(a){return a instanceof rd}function hh(a){return gh(a)||a===null||fh(a)}function ih(a){return a instanceof nd}function jh(a){return a instanceof wd}function kh(a){return typeof a==="string"}function lh(a){return kh(a)||a===null||fh(a)}function mh(a){return typeof a==="boolean"}function nh(a){return mh(a)||fh(a)}function oh(a){return mh(a)||a===null||fh(a)}function ph(a){return typeof a==="number"}function fh(a){return a===void 0};function qh(a){return""+a}
function rh(a,b){var c=[];return c};function sh(a,b){var c=new rd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Ua();return c}
function th(a,b){var c=new Ua,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];jb(e)?c.set(d,sh(a+"_"+d,e)):jd(e)?c.set(d,th(a+"_"+d,e)):(mb(e)||lb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ua();return c};function uh(a,b){if(!kh(a))throw G(this.getName(),["string"],arguments);if(!lh(b))throw G(this.getName(),["string","undefined"],arguments);var c={},d=new Ua;return d=th("AssertApiSubject",
c)};function vh(a,b){if(!lh(b))throw G(this.getName(),["string","undefined"],arguments);if(a instanceof yd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ua;return d=th("AssertThatSubject",c)};function wh(a){return function(){for(var b=xa.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(zd(b[e],d));return Ad(a.apply(null,c))}}function xh(){for(var a=Math,b=yh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=wh(a[e].bind(a)))}return c};function zh(a){return a!=null&&Eb(a,"__cvt_")};function Ah(a){var b;return b};function Bh(a){var b;if(!kh(a))throw G(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Ch(a){try{return encodeURI(a)}catch(b){}};function Dh(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Eh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Fh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Eh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Eh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Hh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Fh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Gh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Gh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Hh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Ng(d(c[0]),d(c[1]),!1);case 5:return Pg(d(c[0]),d(c[1]));case 6:return Ug(d(c[0]),d(c[1]));case 7:return Kg(d(c[0]),d(c[1]));case 8:return Og(d(c[0]),d(c[1]));case 9:return Tg(d(c[0]),d(c[1]));case 10:return Rg(d(c[0]),d(c[1]));case 11:return Sg(d(c[0]),d(c[1]));case 12:return Qg(d(c[0]),d(c[1]));case 13:return Lg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Ih(a){if(!lh(a))throw G(this.getName(),["string|undefined"],arguments);};function Jh(a,b){if(!ph(a)||!ph(b))throw G(this.getName(),["number","number"],arguments);return pb(a,b)};function Kh(){return(new Date).getTime()};function Lh(a){if(a===null)return"null";if(a instanceof nd)return"array";if(a instanceof rd)return"function";if(a instanceof wd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Mh(a){function b(c){return function(d){try{return c(d)}catch(e){(ng||og.Rm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Ad(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(zd(c))}),publicName:"JSON"}};function Nh(a){return ub(zd(a,this.K))};function Oh(a){return Number(zd(a,this.K))};function Ph(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Qh(a,b,c){var d=null,e=!1;return e?d:null};var yh="floor ceil round max min abs pow sqrt".split(" ");function Rh(){var a={};return{xp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Mm:function(b,c){a[b]=c},reset:function(){a={}}}}function Sh(a,b){return function(){return rd.prototype.invoke.apply(a,[b].concat(ta(xa.apply(0,arguments))))}}
function Th(a,b){if(!kh(a))throw G(this.getName(),["string","any"],arguments);}
function Uh(a,b){if(!kh(a)||!dh(b))throw G(this.getName(),["string","PixieMap"],arguments);};var Vh={};var Wh=function(a){var b=new Ua;if(a instanceof nd)for(var c=a.wa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof rd)for(var f=a.wa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Vh.keys=function(a){ch(this.getName(),arguments);if(a instanceof nd||a instanceof rd||typeof a==="string")a=Wh(a);if(a instanceof Ua||a instanceof yd)return new nd(a.wa());return new nd};
Vh.values=function(a){ch(this.getName(),arguments);if(a instanceof nd||a instanceof rd||typeof a==="string")a=Wh(a);if(a instanceof Ua||a instanceof yd)return new nd(a.ac());return new nd};
Vh.entries=function(a){ch(this.getName(),arguments);if(a instanceof nd||a instanceof rd||typeof a==="string")a=Wh(a);if(a instanceof Ua||a instanceof yd)return new nd(a.Jb().map(function(b){return new nd(b)}));return new nd};
Vh.freeze=function(a){(a instanceof Ua||a instanceof yd||a instanceof nd||a instanceof rd)&&a.Ua();return a};Vh.delete=function(a,b){if(a instanceof Ua&&!a.ub())return a.remove(b),!0;return!1};function I(a,b){var c=xa.apply(2,arguments),d=a.K.tb();if(!d)throw Error("Missing program state.");if(d.rq){try{d.am.apply(null,[b].concat(ta(c)))}catch(e){throw db("TAGGING",21),e;}return}d.am.apply(null,[b].concat(ta(c)))};var Xh=function(){this.H={};this.C={};this.N=!0;};Xh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Xh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Xh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:jb(b)?sh(a,b):th(a,b)};function Yh(a,b){var c=void 0;return c};function Zh(){var a={};return a};var J={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ia:"analytics_storage",jc:"region",ja:"consent_updated",vg:"wait_for_update",mn:"app_remove",nn:"app_store_refund",on:"app_store_subscription_cancel",pn:"app_store_subscription_convert",qn:"app_store_subscription_renew",rn:"consent_update",mk:"add_payment_info",nk:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",pk:"view_cart",Zc:"begin_checkout",Rd:"select_item",mc:"view_item_list",Ic:"select_promotion",nc:"view_promotion",
lb:"purchase",Sd:"refund",yb:"view_item",qk:"add_to_wishlist",sn:"exception",tn:"first_open",un:"first_visit",qa:"gtag.config",Db:"gtag.get",vn:"in_app_purchase",bd:"page_view",wn:"screen_view",xn:"session_start",yn:"source_update",zn:"timing_complete",An:"track_social",Td:"user_engagement",Bn:"user_id_update",Me:"gclid_link_decoration_source",Ne:"gclid_storage_source",oc:"gclgb",mb:"gclid",rk:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",za:"ads_data_redaction",Oe:"gad_source",Pe:"gad_source_src",
dd:"gclid_url",sk:"gclsrc",Qe:"gbraid",Xd:"wbraid",Ea:"allow_ad_personalization_signals",Cg:"allow_custom_scripts",Re:"allow_direct_google_requests",Dg:"allow_display_features",Eg:"allow_enhanced_conversions",Pb:"allow_google_signals",nb:"allow_interest_groups",Cn:"app_id",Dn:"app_installer_id",En:"app_name",Gn:"app_version",Qb:"auid",Hn:"auto_detection_enabled",ed:"aw_remarketing",Th:"aw_remarketing_only",Fg:"discount",Gg:"aw_feed_country",Hg:"aw_feed_language",sa:"items",Ig:"aw_merchant_id",tk:"aw_basket_type",
Se:"campaign_content",Te:"campaign_id",Ue:"campaign_medium",Ve:"campaign_name",We:"campaign",Xe:"campaign_source",Ye:"campaign_term",Rb:"client_id",uk:"rnd",Uh:"consent_update_type",In:"content_group",Jn:"content_type",Sb:"conversion_cookie_prefix",Ze:"conversion_id",Oa:"conversion_linker",Vh:"conversion_linker_disabled",fd:"conversion_api",Jg:"cookie_deprecation",ob:"cookie_domain",pb:"cookie_expires",zb:"cookie_flags",gd:"cookie_name",Tb:"cookie_path",eb:"cookie_prefix",Jc:"cookie_update",Yd:"country",
Va:"currency",Wh:"customer_buyer_stage",af:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",bf:"custom_map",Zh:"gcldc",hd:"dclid",vk:"debug_mode",oa:"developer_id",Kn:"disable_merchant_reported_purchases",jd:"dc_custom_params",Ln:"dc_natural_search",wk:"dynamic_event_settings",xk:"affiliation",Kg:"checkout_option",ai:"checkout_step",yk:"coupon",cf:"item_list_name",bi:"list_name",Mn:"promotions",df:"shipping",di:"tax",Lg:"engagement_time_msec",Mg:"enhanced_client_id",ei:"enhanced_conversions",
zk:"enhanced_conversions_automatic_settings",Ng:"estimated_delivery_date",fi:"euid_logged_in_state",ef:"event_callback",Nn:"event_category",Ub:"event_developer_id_string",On:"event_label",kd:"event",Og:"event_settings",Pg:"event_timeout",Pn:"description",Qn:"fatal",Rn:"experiments",gi:"firebase_id",Zd:"first_party_collection",Qg:"_x_20",rc:"_x_19",Ak:"fledge_drop_reason",Bk:"fledge",Ck:"flight_error_code",Dk:"flight_error_message",Ek:"fl_activity_category",Fk:"fl_activity_group",hi:"fl_advertiser_id",
Gk:"fl_ar_dedupe",ff:"match_id",Hk:"fl_random_number",Ik:"tran",Jk:"u",Rg:"gac_gclid",ae:"gac_wbraid",Kk:"gac_wbraid_multiple_conversions",Lk:"ga_restrict_domain",ii:"ga_temp_client_id",Sn:"ga_temp_ecid",ld:"gdpr_applies",Mk:"geo_granularity",Kc:"value_callback",sc:"value_key",uc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Nk:"google_tld",hf:"gpp_sid",jf:"gpp_string",Sg:"groups",Ok:"gsa_experiment_id",kf:"gtag_event_feature_usage",Pk:"gtm_up",Lc:"iframe_state",lf:"ignore_referrer",
ji:"internal_traffic_results",Qk:"_is_fpm",Mc:"is_legacy_converted",Nc:"is_legacy_loaded",Tg:"is_passthrough",md:"_lps",Ab:"language",Ug:"legacy_developer_id_string",Pa:"linker",de:"accept_incoming",vc:"decorate_forms",ma:"domains",Oc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Rk:"method",Tn:"name",Sk:"navigation_type",nf:"new_customer",Vg:"non_interaction",Un:"optimize_id",Tk:"page_hostname",pf:"page_path",Wa:"page_referrer",Eb:"page_title",Uk:"passengers",
Vk:"phone_conversion_callback",Vn:"phone_conversion_country_code",Wk:"phone_conversion_css_class",Wn:"phone_conversion_ids",Xk:"phone_conversion_number",Yk:"phone_conversion_options",Xn:"_platinum_request_status",Yn:"_protected_audience_enabled",qf:"quantity",Wg:"redact_device_info",ki:"referral_exclusion_definition",Sq:"_request_start_time",Wb:"restricted_data_processing",Zn:"retoken",ao:"sample_rate",li:"screen_name",Pc:"screen_resolution",Zk:"_script_source",bo:"search_term",qb:"send_page_view",
nd:"send_to",od:"server_container_url",rf:"session_duration",Xg:"session_engaged",mi:"session_engaged_time",wc:"session_id",Yg:"session_number",tf:"_shared_user_id",uf:"delivery_postal_code",Tq:"_tag_firing_delay",Uq:"_tag_firing_time",Vq:"temporary_client_id",ni:"_timezone",oi:"topmost_url",co:"tracking_id",ri:"traffic_type",Xa:"transaction_id",xc:"transport_url",al:"trip_type",rd:"update",Fb:"url_passthrough",bl:"uptgs",vf:"_user_agent_architecture",wf:"_user_agent_bitness",xf:"_user_agent_full_version_list",
yf:"_user_agent_mobile",zf:"_user_agent_model",Af:"_user_agent_platform",Bf:"_user_agent_platform_version",Cf:"_user_agent_wow64",fb:"user_data",si:"user_data_auto_latency",ui:"user_data_auto_meta",wi:"user_data_auto_multi",xi:"user_data_auto_selectors",yi:"user_data_auto_status",yc:"user_data_mode",Zg:"user_data_settings",Qa:"user_id",Xb:"user_properties",fl:"_user_region",Df:"us_privacy_string",Fa:"value",il:"wbraid_multiple_conversions",ud:"_fpm_parameters",Ci:"_host_name",sl:"_in_page_command",
tl:"_ip_override",yl:"_is_passthrough_cid",zc:"non_personalized_ads",Oi:"_sst_parameters",qc:"conversion_label",Aa:"page_location",Vb:"global_developer_id_string",pd:"tc_privacy_string"}};var $h={},ai=($h[J.m.ja]="gcu",$h[J.m.oc]="gclgb",$h[J.m.mb]="gclaw",$h[J.m.rk]="gclid_len",$h[J.m.Ud]="gclgs",$h[J.m.Vd]="gcllp",$h[J.m.Wd]="gclst",$h[J.m.Qb]="auid",$h[J.m.Fg]="dscnt",$h[J.m.Gg]="fcntr",$h[J.m.Hg]="flng",$h[J.m.Ig]="mid",$h[J.m.tk]="bttype",$h[J.m.Rb]="gacid",$h[J.m.qc]="label",$h[J.m.fd]="capi",$h[J.m.Jg]="pscdl",$h[J.m.Va]="currency_code",$h[J.m.Wh]="clobs",$h[J.m.af]="vdltv",$h[J.m.Xh]="clolo",$h[J.m.Yh]="clolb",$h[J.m.vk]="_dbg",$h[J.m.Ng]="oedeld",$h[J.m.Ub]="edid",$h[J.m.Ak]=
"fdr",$h[J.m.Bk]="fledge",$h[J.m.Rg]="gac",$h[J.m.ae]="gacgb",$h[J.m.Kk]="gacmcov",$h[J.m.ld]="gdpr",$h[J.m.Vb]="gdid",$h[J.m.be]="_ng",$h[J.m.hf]="gpp_sid",$h[J.m.jf]="gpp",$h[J.m.Ok]="gsaexp",$h[J.m.kf]="_tu",$h[J.m.Lc]="frm",$h[J.m.Tg]="gtm_up",$h[J.m.md]="lps",$h[J.m.Ug]="did",$h[J.m.ee]="fcntr",$h[J.m.fe]="flng",$h[J.m.he]="mid",$h[J.m.nf]=void 0,$h[J.m.Eb]="tiba",$h[J.m.Wb]="rdp",$h[J.m.wc]="ecsid",$h[J.m.tf]="ga_uid",$h[J.m.uf]="delopc",$h[J.m.pd]="gdpr_consent",$h[J.m.Xa]="oid",$h[J.m.bl]=
"uptgs",$h[J.m.vf]="uaa",$h[J.m.wf]="uab",$h[J.m.xf]="uafvl",$h[J.m.yf]="uamb",$h[J.m.zf]="uam",$h[J.m.Af]="uap",$h[J.m.Bf]="uapv",$h[J.m.Cf]="uaw",$h[J.m.si]="ec_lat",$h[J.m.ui]="ec_meta",$h[J.m.wi]="ec_m",$h[J.m.xi]="ec_sel",$h[J.m.yi]="ec_s",$h[J.m.yc]="ec_mode",$h[J.m.Qa]="userId",$h[J.m.Df]="us_privacy",$h[J.m.Fa]="value",$h[J.m.il]="mcov",$h[J.m.Ci]="hn",$h[J.m.sl]="gtm_ee",$h[J.m.zc]="npa",$h[J.m.Ze]=null,$h[J.m.Pc]=null,$h[J.m.Ab]=null,$h[J.m.sa]=null,$h[J.m.Aa]=null,$h[J.m.Wa]=null,$h[J.m.oi]=
null,$h[J.m.ud]=null,$h[J.m.Me]=null,$h[J.m.Ne]=null,$h[J.m.uc]=null,$h);function bi(a,b){if(a){var c=a.split("x");c.length===2&&(ci(b,"u_w",c[0]),ci(b,"u_h",c[1]))}}
function di(a){var b=ei;b=b===void 0?fi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(gi(q.value)),r.push(gi(q.quantity)),r.push(gi(q.item_id)),r.push(gi(q.start_date)),r.push(gi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function fi(a){return hi(a.item_id,a.id,a.item_name)}function hi(){for(var a=l(xa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ii(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function ci(a,b,c){c===void 0||c===null||c===""&&!Ag[b]||(a[b]=c)}function gi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ji={},li={wq:ki};function mi(a,b){var c=ji[b],d=c.Om;if(!(ji[b].active||ji[b].percent>50||c.percent<=0||(a.studies||{})[d])){var e=a.studies||{};e[d]=!0;a.studies=e;li.wq(a,b)}}function ki(a,b){var c=ji[b];if(!(pb(0,9999)<c.percent*2*100))return a;ni(a,{experimentId:c.experimentId,controlId:c.controlId,experimentCallback:function(){}});return a}
function ni(a,b){if((a.exp||{})[b.experimentId])b.experimentCallback();else if(!(a.exp||{})[b.controlId]){var c;a:{for(var d=!1,e=!1,f=0;d===e;)if(d=pb(0,1)===0,e=pb(0,1)===0,f++,f>30){c=void 0;break a}c=d}var g=c;if(g!==void 0)if(g){b.experimentCallback();var h=a.exp||{};h[b.experimentId]=!0;a.exp=h}else{var m=a.exp||{};m[b.controlId]=!0;a.exp=m}}};var K={J:{Wj:"call_conversion",W:"conversion",eo:"floodlight",Ff:"ga_conversion",Ki:"landing_page",Ga:"page_view",na:"remarketing",Ta:"user_data_lead",Ja:"user_data_web"}};function qi(a){return ri?A.querySelectorAll(a):null}
function si(a,b){if(!ri)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var ti=!1;
if(A.querySelectorAll)try{var ui=A.querySelectorAll(":root");ui&&ui.length==1&&ui[0]==A.documentElement&&(ti=!0)}catch(a){}var ri=ti;function vi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function wi(){this.blockSize=-1};function xi(a,b){this.blockSize=-1;this.blockSize=64;this.N=ya.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ba=a;this.R=b;this.ka=ya.Int32Array?new Int32Array(64):Array(64);yi===void 0&&(ya.Int32Array?yi=new Int32Array(zi):yi=zi);this.reset()}za(xi,wi);for(var Ai=[],Bi=0;Bi<63;Bi++)Ai[Bi]=0;var Ci=[].concat(128,Ai);
xi.prototype.reset=function(){this.P=this.H=0;var a;if(ya.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Di=function(a){for(var b=a.N,c=a.ka,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,v=a.C[6]|0,u=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(u+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&v)+(yi[w]|0)|0)+(c[w]|0)|0)|0;u=v;v=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+v|0;a.C[7]=a.C[7]+u|0};
xi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Di(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Di(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};xi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Ci,56-this.H):this.update(Ci,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Di(this);for(var d=0,e=0;e<this.ba;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var zi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],yi;function Ei(){xi.call(this,8,Fi)}za(Ei,xi);var Fi=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Gi=/^[0-9A-Fa-f]{64}$/;function Hi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Ii(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Gi.test(a))return Promise.resolve(a);try{var d=Hi(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Ji(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ji(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ki=[],Li;function Mi(a){Li?Li(a):Ki.push(a)}function Ni(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Mi(a),b):c}function Oi(a,b){if(!E(190))return b;var c=Pi(a,"");return c!==b?(Mi(a),b):c}function Pi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Qi(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Mi(a),b)}function Ri(){Li=Si;for(var a=l(Ki),b=a.next();!b.done;b=a.next())Li(b.value);Ki.length=0};var Ti={jn:'512',kn:'0',ln:'1000',jo:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',ko:'US-CO',Fo:Oi(44,'101509157~103116026~103200004~103233427~103308216~103308218~103351869~103351871~104684208~104684211~104718208~104784387~104784389')},Ui={fp:Number(Ti.jn)||0,hp:Number(Ti.kn)||0,kp:Number(Ti.ln)||0,Bp:Ti.jo.split("~"),Cp:Ti.ko.split("~"),Mq:Ti.Fo};Object.assign({},Ui);function L(a){db("GTM",a)};
var Zi=function(a,b){var c=["tv.1"],d=Vi(a);if(d)return c.push(d),{Za:!1,Oj:c.join("~"),rg:{}};var e={},f=0;var g=Wi(a,function(p,q,r){var t=p.value,v;if(r){var u=q+"__"+f++;v="${userData."+u+"|sha256}";e[u]=t}else v=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+v)}).Za;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{Za:g,Oj:h,rg:m,jp:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?Xi():Yi()}:{Za:g,Oj:h,rg:m}},aj=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=$i(a);return Wi(b,function(){}).Za},Wi=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=bj[g.name];if(h){var m=cj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{Za:d,oj:c}},cj=function(a){var b=dj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(ej.test(e)||
Gi.test(e))}return d},dj=function(a){return fj.indexOf(a)!==-1},Yi=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BAVbRMqUOiJUQLIinBC3YVZcD9vNRXKwHiFYJlOwvV3oWdq6MPmHrYHH3Tsb4ArLTG2yrq9GR2+ngbmMaPzzZAI\x3d\x22,\x22version\x22:0},\x22id\x22:\x22f13fb26f-adc9-4df0-922e-d1cd05f94e51\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BC8F8UNF0Lt6iZosdyVL93v42+R8xwRnKtLiDw2ck6H/dR9X1HNdkZB6KzzXdoj06uZz1jIddA02tofa7AABhaA\x3d\x22,\x22version\x22:0},\x22id\x22:\x22ebff13c1-8294-4f85-87da-39aa2f9658c2\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BJoZFhiTSB5LuGZOcrxFF2IfcxKMhEzJAcnPZUh2VQDy5sN28BXf/ix29/P+8W/hjDGq/6VnvZUpf3NMTGbD8R4\x3d\x22,\x22version\x22:0},\x22id\x22:\x22c81a7607-ad9d-44c8-892f-a56fc2d0f53a\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BGRGKUQTzvTvmyJOfyvxkd9kM1OWbNuNvw1MQGczwHUCkzVD08LsTxn46XzwBe7viDdzcC8BopySeDSJFjsS6Xc\x3d\x22,\x22version\x22:0},\x22id\x22:\x227d68cd60-2fce-40c7-b59c-59f400022449\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BFYk2nokkvPJdLz7H6jjcrrGR7+bPmBQtlXh4riP4Q8cJAbUDKsoLmRZ99mq5NoEk8Mmsl6x1z9Tix0vd4FhrTA\x3d\x22,\x22version\x22:0},\x22id\x22:\x2213f6b2d0-1bfb-4d3e-8a3e-e5c5d7ada173\x22}]}'},ij=function(a){if(x.Promise){var b=void 0;return b}},nj=function(a,b,c,d,e){if(x.Promise)try{var f=$i(a),g=jj(f,e).then(kj);return g}catch(p){}},pj=function(a){try{return kj(oj($i(a)))}catch(b){}},hj=function(a,b){var c=void 0;return c},kj=function(a){var b=a.Vc,c=a.time,d=["tv.1"],e=Vi(b);if(e)return d.push(e),{Lb:encodeURIComponent(d.join("~")),oj:!1,Za:!1,time:c,nj:!0};var f=b.filter(function(n){return!cj(n)}),g=Wi(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.oj,m=g.Za;return{Lb:encodeURIComponent(d.join("~")),oj:h,Za:m,time:c,nj:!1}},Vi=function(a){if(a.length===1&&a[0].name==="error_code")return bj.error_code+
"."+a[0].value},mj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(bj[d.name]&&d.value)return!0}return!1},$i=function(a){function b(r,t,v,u){var w=qj(r);w!==""&&(Gi.test(w)?h.push({name:t,value:w,index:u}):h.push({name:t,value:v(w),index:u}))}function c(r,t){var v=r;if(lb(v)||Array.isArray(v)){v=nb(r);for(var u=0;u<v.length;++u){var w=qj(v[u]),y=Gi.test(w);t&&!y&&L(89);!t&&y&&L(88)}}}function d(r,t){var v=r[t];c(v,!1);var u=
rj[t];r[u]&&(r[t]&&L(90),v=r[u],c(v,!0));return v}function e(r,t,v){for(var u=nb(d(r,t)),w=0;w<u.length;++w)b(u[w],t,v)}function f(r,t,v,u){var w=d(r,t);b(w,t,v,u)}function g(r){return function(t){L(64);return r(t)}}var h=[];if(x.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",sj);e(a,"phone_number",tj);e(a,"first_name",g(uj));e(a,"last_name",g(uj));var m=a.home_address||{};e(m,"street",g(vj));e(m,"city",g(vj));e(m,"postal_code",g(wj));e(m,"region",
g(vj));e(m,"country",g(wj));for(var n=nb(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",uj,p);f(q,"last_name",uj,p);f(q,"street",vj,p);f(q,"city",vj,p);f(q,"postal_code",wj,p);f(q,"region",vj,p);f(q,"country",wj,p)}return h},xj=function(a){var b=a?$i(a):[];return kj({Vc:b})},yj=function(a){return a&&a!=null&&Object.keys(a).length>0&&x.Promise?$i(a).some(function(b){return b.value&&dj(b.name)&&!Gi.test(b.value)}):!1},qj=function(a){return a==null?"":lb(a)?xb(String(a)):"e0"},wj=function(a){return a.replace(zj,
"")},uj=function(a){return vj(a.replace(/\s/g,""))},vj=function(a){return xb(a.replace(Aj,"").toLowerCase())},tj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Bj.test(a)?a:"e0"},sj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Cj.test(c))return c}return"e0"},oj=function(a){var b=Xc();try{a.forEach(function(e){if(e.value&&dj(e.name)){var f;var g=e.value,h=x;if(g===""||
g==="e0"||Gi.test(g))f=g;else try{var m=new Ei;m.update(Hi(g));f=Ji(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Vc:a};if(b!==void 0){var d=Xc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Vc:[]}}},jj=function(a,b){if(!a.some(function(d){return d.value&&dj(d.name)}))return Promise.resolve({Vc:a});if(!x.Promise)return Promise.resolve({Vc:[]});var c=b?Xc():void 0;return Promise.all(a.map(function(d){return d.value&&dj(d.name)?Ii(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Vc:a};if(c!==void 0){var e=Xc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Vc:[]}})},Aj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Cj=/^\S+@\S+\.\S+$/,Bj=/^\+\d{10,15}$/,zj=/[.~]/g,ej=/^[0-9A-Za-z_-]{43}$/,Dj={},bj=(Dj.email="em",Dj.phone_number="pn",Dj.first_name="fn",Dj.last_name="ln",Dj.street="sa",Dj.city="ct",Dj.region="rg",Dj.country="co",Dj.postal_code="pc",Dj.error_code="ec",Dj),Ej={},rj=(Ej.email="sha256_email_address",Ej.phone_number="sha256_phone_number",
Ej.first_name="sha256_first_name",Ej.last_name="sha256_last_name",Ej.street="sha256_street",Ej);var fj=Object.freeze(["email","phone_number","first_name","last_name","street"]);
var Fj={},Gj=(Fj[J.m.nb]=1,Fj[J.m.od]=2,Fj[J.m.xc]=2,Fj[J.m.za]=3,Fj[J.m.af]=4,Fj[J.m.Cg]=5,Fj[J.m.Jc]=6,Fj[J.m.eb]=6,Fj[J.m.ob]=6,Fj[J.m.gd]=6,Fj[J.m.Tb]=6,Fj[J.m.zb]=6,Fj[J.m.pb]=7,Fj[J.m.Wb]=9,Fj[J.m.Dg]=10,Fj[J.m.Pb]=11,Fj),Hj={},Ij=(Hj.unknown=13,Hj.standard=14,Hj.unique=15,Hj.per_session=16,Hj.transactions=17,Hj.items_sold=18,Hj);var fb=[];function Jj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Gj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Gj[f],h=b;h=h===void 0?!1:h;db("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(fb[g]=!0)}}};var Kj=function(){this.C=new Set;this.H=new Set},Mj=function(a){var b=Lj.R;a=a===void 0?[]:a;var c=[].concat(ta(b.C)).concat([].concat(ta(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Nj=function(){var a=[].concat(ta(Lj.R.C));a.sort(function(b,c){return b-c});return a},Oj=function(){var a=Lj.R,b=Ui.Mq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Pj={},Qj=Oi(14,"56r1"),Rj=Qi(15,Number("0")),Sj=Oi(19,"dataLayer");Oi(20,"");Oi(16,"ChAI8I6OwwYQ3pudh+6i9os5EiUAuqGxaVDW0PzQ93pqG+uh/E4yZkYvulcE0a4AEgCLfRHj1aOCGgK5DQ\x3d\x3d");var Tj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Uj={__paused:1,__tg:1},Vj;for(Vj in Tj)Tj.hasOwnProperty(Vj)&&(Uj[Vj]=1);var Wj=Ni(11,vb("true")),Xj=!1,Yj,Zj=!1;Zj=!0;
Yj=Zj;var ak,bk=!1;ak=bk;Pj.Ag=Oi(3,"www.googletagmanager.com");var ck=""+Pj.Ag+(Yj?"/gtag/js":"/gtm.js"),dk=null,ek=null,fk={},gk={};Pj.Zm=Ni(2,vb(""));var hk="";Pj.Pi=hk;
var Lj=new function(){this.R=new Kj;this.C=this.N=!1;this.H=0;this.Ba=this.Sa=this.rb=this.P="";this.ba=this.ka=!1};function ik(){var a;a=a===void 0?[]:a;return Mj(a).join("~")}function jk(){var a=Lj.P.length;return Lj.P[a-1]==="/"?Lj.P.substring(0,a-1):Lj.P}function kk(){return Lj.C?E(84)?Lj.H===0:Lj.H!==1:!1}function lk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var mk=new rb,nk={},ok={},rk={name:Sj,set:function(a,b){kd(Gb(a,b),nk);pk()},get:function(a){return qk(a,2)},reset:function(){mk=new rb;nk={};pk()}};function qk(a,b){return b!=2?mk.get(a):sk(a)}function sk(a,b){var c=a.split(".");b=b||[];for(var d=nk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function tk(a,b){ok.hasOwnProperty(a)||(mk.set(a,b),kd(Gb(a,b),nk),pk())}
function uk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=qk(c,1);if(Array.isArray(d)||jd(d))d=kd(d,null);ok[c]=d}}function pk(a){sb(ok,function(b,c){mk.set(b,c);kd(Gb(b),nk);kd(Gb(b,c),nk);a&&delete ok[b]})}function vk(a,b){var c,d=(b===void 0?2:b)!==1?sk(a):mk.get(a);hd(d)==="array"||hd(d)==="object"?c=kd(d,null):c=d;return c};
var xk=function(a){for(var b=[],c=Object.keys(wk),d=0;d<c.length;d++){var e=c[d],f=wk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},yk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},zk=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!Eb(w,"#")&&!Eb(w,".")}),h=0;h<g.length;h++){var m=g[h];if(Eb(m,"dataLayer."))f=qk(m.substring(10));
else{var n=m.split(".");f=x[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&ri)try{var q=qi(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Mc(q[r])||xb(q[r].value));f=f.length===1?f[0]:f}}catch(w){L(149)}if(E(60)){for(var t,v=0;v<g.length&&(t=qk(g[v]),t===void 0);v++);var u=f!==void 0;d[b]=yk(t!==void 0,u);u||(f=t)}return f?(a[b]=f,!0):!1},Ak=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=zk(c,"email",
a.email,b)||d;d=zk(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=zk(g,"first_name",e[f].first_name,b)||d;d=zk(g,"last_name",e[f].last_name,b)||d;d=zk(g,"street",e[f].street,b)||d;d=zk(g,"city",e[f].city,b)||d;d=zk(g,"region",e[f].region,b)||d;d=zk(g,"country",e[f].country,b)||d;d=zk(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},Bk=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&jd(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=x.enhanced_conversion_data;d&&db("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return Ak(a[J.m.zk])}},Ck=function(a){return jd(a)?!!a.enable_code:!1},wk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Fk=/:[0-9]+$/,Gk=/^\d+\.fls\.doubleclick\.net$/;function Hk(a,b,c,d){var e=Ik(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Ik(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=sa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Jk(a){try{return decodeURIComponent(a)}catch(b){}}function Kk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Lk(a.protocol)||Lk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Fk,"").toLowerCase());return Mk(a,b,c,d,e)}
function Mk(a,b,c,d,e){var f,g=Lk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Nk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Fk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||db("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Hk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Lk(a){return a?a.replace(":","").toLowerCase():""}function Nk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Ok={},Pk=0;
function Qk(a){var b=Ok[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||db("TAGGING",1),d="/"+d);var e=c.hostname.replace(Fk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Pk<5&&(Ok[a]=b,Pk++)}return b}function Rk(a,b,c){var d=Qk(a);return Lb(b,d,c)}
function Sk(a){var b=Qk(x.location.href),c=Kk(b,"host",!1);if(c&&c.match(Gk)){var d=Kk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Tk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Uk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Vk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Qk(""+c+b).href}}function Wk(a,b){if(kk()||Lj.N)return Vk(a,b)}
function Xk(){return!!Pj.Pi&&Pj.Pi.split("@@").join("")!=="SGTM_TOKEN"}function Yk(a){for(var b=l([J.m.od,J.m.xc]),c=b.next();!c.done;c=b.next()){var d=N(a,c.value);if(d)return d}}function Zk(a,b,c){c=c===void 0?"":c;if(!kk())return a;var d=b?Tk[a]||"":"";d==="/gs"&&(c="");return""+jk()+d+c}function $k(a){if(!kk())return a;for(var b=l(Uk),c=b.next();!c.done;c=b.next())if(Eb(a,""+jk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function al(a){var b=String(a[ef.Ra]||"").replace(/_/g,"");return Eb(b,"cvt")?"cvt":b}var bl=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var cl={sq:Qi(27,Number("0.005000")),cp:Qi(42,Number("0.010000"))},dl=Math.random(),el=bl||dl<Number(cl.sq),fl=bl||dl>=1-Number(cl.cp);var gl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},hl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var il,jl;a:{for(var kl=["CLOSURE_FLAGS"],ll=ya,ml=0;ml<kl.length;ml++)if(ll=ll[kl[ml]],ll==null){jl=null;break a}jl=ll}var nl=jl&&jl[610401301];il=nl!=null?nl:!1;function ol(){var a=ya.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var pl,ql=ya.navigator;pl=ql?ql.userAgentData||null:null;function rl(a){if(!il||!pl)return!1;for(var b=0;b<pl.brands.length;b++){var c=pl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function sl(a){return ol().indexOf(a)!=-1};function tl(){return il?!!pl&&pl.brands.length>0:!1}function ul(){return tl()?!1:sl("Opera")}function vl(){return sl("Firefox")||sl("FxiOS")}function wl(){return tl()?rl("Chromium"):(sl("Chrome")||sl("CriOS"))&&!(tl()?0:sl("Edge"))||sl("Silk")};var xl=function(a){xl[" "](a);return a};xl[" "]=function(){};var yl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function zl(){return il?!!pl&&!!pl.platform:!1}function Al(){return sl("iPhone")&&!sl("iPod")&&!sl("iPad")}function Bl(){Al()||sl("iPad")||sl("iPod")};ul();tl()||sl("Trident")||sl("MSIE");sl("Edge");!sl("Gecko")||ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")||sl("Trident")||sl("MSIE")||sl("Edge");ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")&&sl("Mobile");zl()||sl("Macintosh");zl()||sl("Windows");(zl()?pl.platform==="Linux":sl("Linux"))||zl()||sl("CrOS");zl()||sl("Android");Al();sl("iPad");sl("iPod");Bl();ol().toLowerCase().indexOf("kaios");var Cl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{xl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Dl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},El=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Fl=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Cl(b.top)?1:2},Gl=function(a){a=a===void 0?document:a;return a.createElement("img")},Hl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Cl(a)&&(b=a);return b};function Il(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Jl(){return Il("join-ad-interest-group")&&jb(rc.joinAdInterestGroup)}
function Kl(a,b,c){var d=Ia[3]===void 0?1:Ia[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ia[2]===void 0?50:Ia[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&zb()-q<(Ia[1]===void 0?6E4:Ia[1])?(db("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ll(f[0]);else{if(n)return db("TAGGING",10),!1}else f.length>=d?Ll(f[0]):n&&Ll(m[0]);Fc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:zb()});return!0}function Ll(a){try{a.parentNode.removeChild(a)}catch(b){}};function Ml(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Nl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};vl();Al()||sl("iPod");sl("iPad");!sl("Android")||wl()||vl()||ul()||sl("Silk");wl();!sl("Safari")||wl()||(tl()?0:sl("Coast"))||ul()||(tl()?0:sl("Edge"))||(tl()?rl("Microsoft Edge"):sl("Edg/"))||(tl()?rl("Opera"):sl("OPR"))||vl()||sl("Silk")||sl("Android")||Bl();var Ol={},Pl=null,Ql=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Pl){Pl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Ol[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Pl[q]===void 0&&(Pl[q]=p)}}}for(var r=Ol[f],t=Array(Math.floor(b.length/3)),v=r[64]||"",u=0,w=0;u<b.length-2;u+=3){var y=b[u],
z=b[u+1],C=b[u+2],D=r[y>>2],F=r[(y&3)<<4|z>>4],H=r[(z&15)<<2|C>>6],M=r[C&63];t[w++]=""+D+F+H+M}var S=0,ca=v;switch(b.length-u){case 2:S=b[u+1],ca=r[(S&15)<<2]||v;case 1:var U=b[u];t[w]=""+r[U>>2]+r[(U&3)<<4|S>>4]+ca+v}return t.join("")};var Rl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Sl=/#|$/,Tl=function(a,b){var c=a.search(Sl),d=Rl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return yl(a.slice(d,e!==-1?e:0))},Ul=/[?&]($|#)/,Vl=function(a,b,c){for(var d,e=a.search(Sl),f=0,g,h=[];(g=Rl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Ul,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),v;t<0||t>r?(t=r,v=""):v=d.substring(t+1,r);q=[d.slice(0,t),v,d.slice(r)];var u=q[1];q[1]=p?u?u+"&"+p:p:u;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Wl(a,b,c,d,e,f){var g=Tl(c,"fmt");if(d){var h=Tl(c,"random"),m=Tl(c,"label")||"";if(!h)return!1;var n=Ql(yl(m)+":"+yl(h));if(!Ml(a,n,d))return!1}g&&Number(g)!==4&&(c=Vl(c,"rfmt",g));var p=Vl(c,"fmt",4);Dc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Xl={},Yl=(Xl[1]={},Xl[2]={},Xl[3]={},Xl[4]={},Xl);function Zl(a,b,c){var d=$l(b,c);if(d){var e=Yl[b][d];e||(e=Yl[b][d]=[]);e.push(Object.assign({},a))}}function am(a,b){var c=$l(a,b);if(c){var d=Yl[a][c];d&&(Yl[a][c]=d.filter(function(e){return!e.Im}))}}function bm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function $l(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function cm(a){var b=xa.apply(1,arguments);fl&&(Zl(a,2,b[0]),Zl(a,3,b[0]));Pc.apply(null,ta(b))}function dm(a){var b=xa.apply(1,arguments);fl&&Zl(a,2,b[0]);return Qc.apply(null,ta(b))}function em(a){var b=xa.apply(1,arguments);fl&&Zl(a,3,b[0]);Gc.apply(null,ta(b))}
function fm(a){var b=xa.apply(1,arguments),c=b[0];fl&&(Zl(a,2,c),Zl(a,3,c));return Tc.apply(null,ta(b))}function gm(a){var b=xa.apply(1,arguments);fl&&Zl(a,1,b[0]);Dc.apply(null,ta(b))}function hm(a){var b=xa.apply(1,arguments);b[0]&&fl&&Zl(a,4,b[0]);Fc.apply(null,ta(b))}function im(a){var b=xa.apply(1,arguments);fl&&Zl(a,1,b[2]);return Wl.apply(null,ta(b))}function jm(a){var b=xa.apply(1,arguments);fl&&Zl(a,4,b[0]);Kl.apply(null,ta(b))};var km=/gtag[.\/]js/,lm=/gtm[.\/]js/,mm=!1;function nm(a){if(mm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(km.test(c))return"3";if(lm.test(c))return"2"}return"0"};function om(a,b,c){var d=pm(),e=qm().container[a];e&&e.state!==3||(qm().container[a]={state:1,context:b,parent:d},rm({ctid:a,isDestination:!1},c))}function rm(a,b){var c=qm();c.pending||(c.pending=[]);ob(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function sm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var tm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=sm()};
function qm(){var a=vc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new tm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=sm());return c};var um={},ig={ctid:Oi(5,"G-06RDJQW848"),canonicalContainerId:Oi(6,"123662157"),Bm:Oi(10,"G-06RDJQW848|GT-K52285D"),Cm:Oi(9,"G-06RDJQW848")};um.qe=Ni(7,vb(""));function vm(){return um.qe&&wm().some(function(a){return a===ig.ctid})}function xm(){return ig.canonicalContainerId||"_"+ig.ctid}function ym(){return ig.Bm?ig.Bm.split("|"):[ig.ctid]}
function wm(){return ig.Cm?ig.Cm.split("|").filter(function(a){return E(108)?a.indexOf("GTM-")!==0:!0}):[]}function zm(){var a=Am(pm()),b=a&&a.parent;if(b)return Am(b)}function Bm(){var a=Am(pm());if(a){for(;a.parent;){var b=Am(a.parent);if(!b)break;a=b}return a}}function Am(a){var b=qm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Cm(){var a=qm();if(a.pending){for(var b,c=[],d=!1,e=ym(),f=wm(),g={},h=0;h<a.pending.length;g={mg:void 0},h++)g.mg=a.pending[h],ob(g.mg.target.isDestination?f:e,function(m){return function(n){return n===m.mg.target.ctid}}(g))?d||(b=g.mg.onLoad,d=!0):c.push(g.mg);a.pending=c;if(b)try{b(xm())}catch(m){}}}
function Dm(){for(var a=ig.ctid,b=ym(),c=wm(),d=function(n,p){var q={canonicalContainerId:ig.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};tc&&(q.scriptElement=tc);uc&&(q.scriptSource=uc);if(zm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var v,u=(v=q.scriptElement)==null?void 0:v.src;if(u){for(var w=Lj.C,y=Qk(u),z=w?y.pathname:""+y.hostname+y.pathname,C=A.scripts,D="",F=0;F<C.length;++F){var H=C[F];if(!(H.innerHTML.length===
0||!w&&H.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||H.innerHTML.indexOf(z)<0)){if(H.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}D=String(F)}}if(D){t=D;break b}}t=void 0}var M=t;if(M){mm=!0;r=M;break a}}var S=[].slice.call(A.scripts);r=q.scriptElement?String(S.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=nm(q)}var ca=p?e.destination:e.container,U=ca[n];U?(p&&U.state===0&&L(93),Object.assign(U,q)):ca[n]=q},e=qm(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[xm()]={};Cm()}function Em(){var a=xm();return!!qm().canonical[a]}function Fm(a){return!!qm().container[a]}function Gm(a){var b=qm().destination[a];return!!b&&!!b.state}function pm(){return{ctid:ig.ctid,isDestination:um.qe}}function Hm(){var a=qm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Im(){var a={};sb(qm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Jm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Km(){for(var a=qm(),b=l(ym()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Lm={Ia:{je:0,pe:1,Li:2}};Lm.Ia[Lm.Ia.je]="FULL_TRANSMISSION";Lm.Ia[Lm.Ia.pe]="LIMITED_TRANSMISSION";Lm.Ia[Lm.Ia.Li]="NO_TRANSMISSION";var Mm={X:{Gb:0,Da:1,Hc:2,Qc:3}};Mm.X[Mm.X.Gb]="NO_QUEUE";Mm.X[Mm.X.Da]="ADS";Mm.X[Mm.X.Hc]="ANALYTICS";Mm.X[Mm.X.Qc]="MONITORING";function Nm(){var a=vc("google_tag_data",{});return a.ics=a.ics||new Om}var Om=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Om.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;db("TAGGING",19);b==null?db("TAGGING",18):Pm(this,a,b==="granted",c,d,e,f,g)};Om.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Pm(this,a[d],void 0,void 0,"","",b,c)};
var Pm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&lb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(db("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Om.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Qm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Qm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&lb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Ae:b})};var Qm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Dm=!0)}};Om.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Dm){d.Dm=!1;try{d.Ae({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Rm=!1,Sm=!1,Tm={},Um={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Tm.ad_storage=1,Tm.analytics_storage=1,Tm.ad_user_data=1,Tm.ad_personalization=1,Tm),usedContainerScopedDefaults:!1};function Vm(a){var b=Nm();b.accessedAny=!0;return(lb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Um)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Wm(a){var b=Nm();b.accessedAny=!0;return b.getConsentState(a,Um)}function Xm(a){var b=Nm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function Ym(){if(!Ja(8))return!1;var a=Nm();a.accessedAny=!0;if(a.active)return!0;if(!Um.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Um.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Um.containerScopedDefaults[c.value]!==1)return!0;return!1}function Zm(a,b){Nm().addListener(a,b)}
function $m(a,b){Nm().notifyListeners(a,b)}function an(a,b){function c(){for(var e=0;e<b.length;e++)if(!Xm(b[e]))return!0;return!1}if(c()){var d=!1;Zm(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function bn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Vm(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=lb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),Zm(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var cn={},dn=(cn[Mm.X.Gb]=Lm.Ia.je,cn[Mm.X.Da]=Lm.Ia.je,cn[Mm.X.Hc]=Lm.Ia.je,cn[Mm.X.Qc]=Lm.Ia.je,cn),en=function(a,b){this.C=a;this.consentTypes=b};en.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Vm(a)});case 1:return this.consentTypes.some(function(a){return Vm(a)});default:jc(this.C,"consentsRequired had an unknown type")}};
var fn={},gn=(fn[Mm.X.Gb]=new en(0,[]),fn[Mm.X.Da]=new en(0,["ad_storage"]),fn[Mm.X.Hc]=new en(0,["analytics_storage"]),fn[Mm.X.Qc]=new en(1,["ad_storage","analytics_storage"]),fn);var jn=function(a){var b=this;this.type=a;this.C=[];Zm(gn[a].consentTypes,function(){hn(b)||b.flush()})};jn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var hn=function(a){return dn[a.type]===Lm.Ia.Li&&!gn[a.type].isConsentGranted()},kn=function(a,b){hn(a)?a.C.push(b):b()},ln=new Map;function mn(a){ln.has(a)||ln.set(a,new jn(a));return ln.get(a)};var nn={Z:{Wm:"aw_user_data_cache",Ph:"cookie_deprecation_label",Bg:"diagnostics_page_id",fo:"fl_user_data_cache",io:"ga4_user_data_cache",Gf:"ip_geo_data_cache",Fi:"ip_geo_fetch_in_progress",Cl:"nb_data",yo:"page_experiment_ids",Of:"pt_data",El:"pt_listener_set",Ll:"service_worker_endpoint",Nl:"shared_user_id",Ol:"shared_user_id_requested",ph:"shared_user_id_source"}};var on=function(a){return Ye(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(nn.Z);
function pn(a,b){b=b===void 0?!1:b;if(on(a)){var c,d,e=(d=(c=vc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function qn(a,b){var c=pn(a,!0);c&&c.set(b)}function rn(a){var b;return(b=pn(a))==null?void 0:b.get()}function sn(a){var b={},c=pn(a);if(!c){c=pn(a,!0);if(!c)return;c.set(b)}return c.get()}function tn(a,b){if(typeof b==="function"){var c;return(c=pn(a,!0))==null?void 0:c.subscribe(b)}}function un(a,b){var c=pn(a);return c?c.unsubscribe(b):!1};var vn="https://"+Oi(21,"www.googletagmanager.com"),wn="/td?id="+ig.ctid,xn={},yn=(xn.tdp=1,xn.exp=1,xn.pid=1,xn.dl=1,xn.seq=1,xn.t=1,xn.v=1,xn),zn=["mcc"],An={},Bn={},Cn=!1,Dn=void 0;function En(a,b,c){Bn[a]=b;(c===void 0||c)&&Fn(a)}function Fn(a,b){An[a]!==void 0&&(b===void 0||!b)||Eb(ig.ctid,"GTM-")&&a==="mcc"||(An[a]=!0)}
function Gn(a){a=a===void 0?!1:a;var b=Object.keys(An).filter(function(c){return An[c]===!0&&Bn[c]!==void 0&&(a||!zn.includes(c))}).map(function(c){var d=Bn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Zk(vn)+wn+(""+b+"&z=0")}function Hn(){Object.keys(An).forEach(function(a){yn[a]||(An[a]=!1)})}
function In(a){a=a===void 0?!1:a;if(Lj.ba&&fl&&ig.ctid){var b=mn(Mm.X.Qc);if(hn(b))Cn||(Cn=!0,kn(b,In));else{var c=Gn(a),d={destinationId:ig.ctid,endpoint:61};a?fm(d,c,void 0,{Hh:!0},void 0,function(){em(d,c+"&img=1")}):em(d,c);Hn();Cn=!1}}}var Jn={};
function Kn(a){var b=String(a);Jn.hasOwnProperty(b)||(Jn[b]=!0,En("csp",Object.keys(Jn).join("~")),Fn("csp",!0),Dn===void 0&&E(171)&&(Dn=x.setTimeout(function(){var c=An.csp;An.csp=!0;An.seq=!1;var d=Gn(!1);An.csp=c;An.seq=!0;Dc(d+"&script=1");Dn=void 0},500)))}function Ln(){Object.keys(An).filter(function(a){return An[a]&&!yn[a]}).length>0&&In(!0)}var Mn;
function Nn(){if(rn(nn.Z.Bg)===void 0){var a=function(){qn(nn.Z.Bg,pb());Mn=0};a();x.setInterval(a,864E5)}else tn(nn.Z.Bg,function(){Mn=0});Mn=0}function On(){Nn();En("v","3");En("t","t");En("pid",function(){return String(rn(nn.Z.Bg))});En("seq",function(){return String(++Mn)});En("exp",ik());Ic(x,"pagehide",Ln)};var Pn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Qn=[J.m.od,J.m.xc,J.m.Zd,J.m.Rb,J.m.wc,J.m.Qa,J.m.Pa,J.m.eb,J.m.ob,J.m.Tb],Rn=!1,Sn=!1,Tn={},Un={};function Vn(){!Sn&&Rn&&(Pn.some(function(a){return Um.containerScopedDefaults[a]!==1})||Wn("mbc"));Sn=!0}function Wn(a){fl&&(En(a,"1"),In())}function Xn(a,b){if(!Tn[b]&&(Tn[b]=!0,Un[b]))for(var c=l(Qn),d=c.next();!d.done;d=c.next())if(N(a,d.value)){Wn("erc");break}};function Yn(a){db("HEALTH",a)};var Zn={wp:Oi(22,"eyIwIjoiRVMiLCIxIjoiRVMtQ1QiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5lcyIsIjQiOiJyZWdpb24xIiwiNSI6ZmFsc2UsIjYiOnRydWUsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ")},$n={},ao=!1;function bo(){function a(){c!==void 0&&un(nn.Z.Gf,c);try{var e=rn(nn.Z.Gf);$n=JSON.parse(e)}catch(f){L(123),Yn(2),$n={}}ao=!0;b()}var b=co,c=void 0,d=rn(nn.Z.Gf);d?a(d):(c=tn(nn.Z.Gf,a),eo())}
function eo(){function a(c){qn(nn.Z.Gf,c||"{}");qn(nn.Z.Fi,!1)}if(!rn(nn.Z.Fi)){qn(nn.Z.Fi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function fo(){var a=Zn.wp;try{return JSON.parse(bb(a))}catch(b){return L(123),Yn(2),{}}}function go(){return $n["0"]||""}function ho(){return $n["1"]||""}function io(){var a=!1;a=!!$n["2"];return a}function jo(){return $n["6"]!==!1}function ko(){var a="";a=$n["4"]||"";return a}
function lo(){var a=!1;a=!!$n["5"];return a}function mo(){var a="";a=$n["3"]||"";return a};var no={},oo=Object.freeze((no[J.m.Ea]=1,no[J.m.Dg]=1,no[J.m.Eg]=1,no[J.m.Pb]=1,no[J.m.sa]=1,no[J.m.ob]=1,no[J.m.pb]=1,no[J.m.zb]=1,no[J.m.gd]=1,no[J.m.Tb]=1,no[J.m.eb]=1,no[J.m.Jc]=1,no[J.m.bf]=1,no[J.m.oa]=1,no[J.m.wk]=1,no[J.m.ef]=1,no[J.m.Og]=1,no[J.m.Pg]=1,no[J.m.Zd]=1,no[J.m.Lk]=1,no[J.m.uc]=1,no[J.m.ce]=1,no[J.m.Nk]=1,no[J.m.Sg]=1,no[J.m.ji]=1,no[J.m.Mc]=1,no[J.m.Nc]=1,no[J.m.Pa]=1,no[J.m.ki]=1,no[J.m.Wb]=1,no[J.m.qb]=1,no[J.m.nd]=1,no[J.m.od]=1,no[J.m.rf]=1,no[J.m.mi]=1,no[J.m.uf]=1,no[J.m.xc]=
1,no[J.m.rd]=1,no[J.m.Zg]=1,no[J.m.Xb]=1,no[J.m.ud]=1,no[J.m.Oi]=1,no));Object.freeze([J.m.Aa,J.m.Wa,J.m.Eb,J.m.Ab,J.m.li,J.m.Qa,J.m.gi,J.m.In]);
var po={},qo=Object.freeze((po[J.m.mn]=1,po[J.m.nn]=1,po[J.m.on]=1,po[J.m.pn]=1,po[J.m.qn]=1,po[J.m.tn]=1,po[J.m.un]=1,po[J.m.vn]=1,po[J.m.xn]=1,po[J.m.Td]=1,po)),ro={},so=Object.freeze((ro[J.m.mk]=1,ro[J.m.nk]=1,ro[J.m.Pd]=1,ro[J.m.Qd]=1,ro[J.m.pk]=1,ro[J.m.Zc]=1,ro[J.m.Rd]=1,ro[J.m.mc]=1,ro[J.m.Ic]=1,ro[J.m.nc]=1,ro[J.m.lb]=1,ro[J.m.Sd]=1,ro[J.m.yb]=1,ro[J.m.qk]=1,ro)),to=Object.freeze([J.m.Ea,J.m.Re,J.m.Pb,J.m.Jc,J.m.Zd,J.m.lf,J.m.qb,J.m.rd]),uo=Object.freeze([].concat(ta(to))),vo=Object.freeze([J.m.pb,
J.m.Pg,J.m.rf,J.m.mi,J.m.Lg]),wo=Object.freeze([].concat(ta(vo))),xo={},yo=(xo[J.m.U]="1",xo[J.m.ia]="2",xo[J.m.V]="3",xo[J.m.La]="4",xo),zo={},Ao=Object.freeze((zo.search="s",zo.youtube="y",zo.playstore="p",zo.shopping="h",zo.ads="a",zo.maps="m",zo));function Bo(a){return typeof a!=="object"||a===null?{}:a}function Co(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Do(a){if(a!==void 0&&a!==null)return Co(a)}function Eo(a){return typeof a==="number"?a:Do(a)};function Fo(a){return a&&a.indexOf("pending:")===0?Go(a.substr(8)):!1}function Go(a){if(a==null||a.length===0)return!1;var b=Number(a),c=zb();return b<c+3E5&&b>c-9E5};var Ho=!1,Io=!1,Jo=!1,Ko=0,Lo=!1,Mo=[];function No(a){if(Ko===0)Lo&&Mo&&(Mo.length>=100&&Mo.shift(),Mo.push(a));else if(Oo()){var b=Oi(41,'google.tagmanager.ta.prodqueue'),c=vc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Po(){Qo();Jc(A,"TAProdDebugSignal",Po)}function Qo(){if(!Io){Io=!0;Ro();var a=Mo;Mo=void 0;a==null||a.forEach(function(b){No(b)})}}
function Ro(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Go(a)?Ko=1:!Fo(a)||Ho||Jo?Ko=2:(Jo=!0,Ic(A,"TAProdDebugSignal",Po,!1),x.setTimeout(function(){Qo();Ho=!0},200))}function Oo(){if(!Lo)return!1;switch(Ko){case 1:case 0:return!0;case 2:return!1;default:return!1}};var So=!1;function To(a,b){var c=ym(),d=wm();if(Oo()){var e=Uo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;No(e)}}
function Vo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ya;e=a.isBatched;var f;if(f=Oo()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=Uo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);No(h)}}function Wo(a){Oo()&&Vo(a())}
function Uo(a,b){b=b===void 0?{}:b;b.groupId=Xo;var c,d=b,e={publicId:Yo};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'2',messageType:a};c.containerProduct=So?"OGT":"GTM";c.key.targetRef=Zo;return c}var Yo="",Zo={ctid:"",isDestination:!1},Xo;
function $o(a){var b=ig.ctid,c=vm();Ko=0;Lo=!0;Ro();Xo=a;Yo=b;So=Yj;Zo={ctid:b,isDestination:c}};var ap=[J.m.U,J.m.ia,J.m.V,J.m.La],bp,cp;function dp(a){var b=a[J.m.jc];b||(b=[""]);for(var c={dg:0};c.dg<b.length;c={dg:c.dg},++c.dg)sb(a,function(d){return function(e,f){if(e!==J.m.jc){var g=Co(f),h=b[d.dg],m=go(),n=ho();Sm=!0;Rm&&db("TAGGING",20);Nm().declare(e,g,h,m,n)}}}(c))}
function ep(a){Vn();!cp&&bp&&Wn("crc");cp=!0;var b=a[J.m.vg];b&&L(41);var c=a[J.m.jc];c?L(40):c=[""];for(var d={eg:0};d.eg<c.length;d={eg:d.eg},++d.eg)sb(a,function(e){return function(f,g){if(f!==J.m.jc&&f!==J.m.vg){var h=Do(g),m=c[e.eg],n=Number(b),p=go(),q=ho();n=n===void 0?0:n;Rm=!0;Sm&&db("TAGGING",20);Nm().default(f,h,m,p,q,n,Um)}}}(d))}
function fp(a){Um.usedContainerScopedDefaults=!0;var b=a[J.m.jc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ho())&&!c.includes(go()))return}sb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Um.usedContainerScopedDefaults=!0;Um.containerScopedDefaults[d]=e==="granted"?3:2})}
function gp(a,b){Vn();bp=!0;sb(a,function(c,d){var e=Co(d);Rm=!0;Sm&&db("TAGGING",20);Nm().update(c,e,Um)});$m(b.eventId,b.priorityId)}function hp(a){a.hasOwnProperty("all")&&(Um.selectedAllCorePlatformServices=!0,sb(Ao,function(b){Um.corePlatformServices[b]=a.all==="granted";Um.usedCorePlatformServices=!0}));sb(a,function(b,c){b!=="all"&&(Um.corePlatformServices[b]=c==="granted",Um.usedCorePlatformServices=!0)})}function ip(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Vm(b)})}
function jp(a,b){Zm(a,b)}function kp(a,b){bn(a,b)}function lp(a,b){an(a,b)}function mp(){var a=[J.m.U,J.m.La,J.m.V];Nm().waitForUpdate(a,500,Um)}function np(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Nm().clearTimeout(d,void 0,Um)}$m()}function op(){if(!ak)for(var a=jo()?lk(Lj.Sa):lk(Lj.rb),b=0;b<ap.length;b++){var c=ap[b],d=c,e=a[c]?"granted":"denied";Nm().implicit(d,e)}};var pp=!1,qp=[];function rp(){if(!pp){pp=!0;for(var a=qp.length-1;a>=0;a--)qp[a]();qp=[]}};var sp=x.google_tag_manager=x.google_tag_manager||{};function tp(a,b){return sp[a]=sp[a]||b()}function up(){var a=ig.ctid,b=vp;sp[a]=sp[a]||b}function wp(){var a=sp.sequence||1;sp.sequence=a+1;return a};function xp(){if(sp.pscdl!==void 0)rn(nn.Z.Ph)===void 0&&qn(nn.Z.Ph,sp.pscdl);else{var a=function(c){sp.pscdl=c;qn(nn.Z.Ph,c)},b=function(){a("error")};try{rc.cookieDeprecationLabel?(a("pending"),rc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var yp=0;function zp(a){fl&&a===void 0&&yp===0&&(En("mcc","1"),yp=1)};var Ap={Ef:{bn:"cd",dn:"ce",fn:"cf",gn:"cpf",hn:"cu"}};var Bp=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Cp=/\s/;
function Dp(a,b){if(lb(a)){a=xb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Bp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Cp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Ep(a,b){for(var c={},d=0;d<a.length;++d){var e=Dp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Fp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Gp={},Fp=(Gp[0]=0,Gp[1]=1,Gp[2]=2,Gp[3]=0,Gp[4]=1,Gp[5]=0,Gp[6]=0,Gp[7]=0,Gp);var Hp=Number('')||500,Ip={},Jp={},Kp={initialized:11,complete:12,interactive:13},Lp={},Mp=Object.freeze((Lp[J.m.qb]=!0,Lp)),Np=void 0;function Op(a,b){if(b.length&&fl){var c;(c=Ip)[a]!=null||(c[a]=[]);Jp[a]!=null||(Jp[a]=[]);var d=b.filter(function(e){return!Jp[a].includes(e)});Ip[a].push.apply(Ip[a],ta(d));Jp[a].push.apply(Jp[a],ta(d));!Np&&d.length>0&&(Fn("tdc",!0),Np=x.setTimeout(function(){In();Ip={};Np=void 0},Hp))}}
function Pp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Qp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var v;hd(t)==="object"?v=t[r]:hd(t)==="array"&&(v=t[r]);return v===void 0?Mp[r]:v},f=Pp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=hd(m)==="object"||hd(m)==="array",q=hd(n)==="object"||hd(n)==="array";if(p&&q)Qp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Rp(){En("tdc",function(){Np&&(x.clearTimeout(Np),Np=void 0);var a=[],b;for(b in Ip)Ip.hasOwnProperty(b)&&a.push(b+"*"+Ip[b].join("."));return a.length?a.join("!"):void 0},!1)};var Sp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Tp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},N=function(a,b,c,d){for(var e=l(Tp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Up=function(a){for(var b={},c=Tp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Sp.prototype.getMergedValues=function(a,b,c){function d(n){jd(n)&&sb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Tp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Vp=function(a){for(var b=[J.m.We,J.m.Se,J.m.Te,J.m.Ue,J.m.Ve,J.m.Xe,J.m.Ye],c=Tp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Wp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ba={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Xp=function(a,
b){a.H=b;return a},Yp=function(a,b){a.R=b;return a},Zp=function(a,b){a.C=b;return a},$p=function(a,b){a.N=b;return a},aq=function(a,b){a.ba=b;return a},bq=function(a,b){a.P=b;return a},cq=function(a,b){a.eventMetadata=b||{};return a},dq=function(a,b){a.onSuccess=b;return a},eq=function(a,b){a.onFailure=b;return a},fq=function(a,b){a.isGtmEvent=b;return a},gq=function(a){return new Sp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var O={A:{Tj:"accept_by_default",ug:"add_tag_timing",Lh:"allow_ad_personalization",Vj:"batch_on_navigation",Xj:"client_id_source",Ie:"consent_event_id",Je:"consent_priority_id",Oq:"consent_state",ja:"consent_updated",Yc:"conversion_linker_enabled",ya:"cookie_options",xg:"create_dc_join",yg:"create_fpm_geo_join",zg:"create_fpm_signals_join",Od:"create_google_join",Le:"em_event",Rq:"endpoint_for_debug",lk:"enhanced_client_id_source",Sh:"enhanced_match_result",ie:"euid_mode_enabled",hb:"event_start_timestamp_ms",
nl:"event_usage",bh:"extra_tag_experiment_ids",Yq:"add_parameter",Ai:"attribution_reporting_experiment",Bi:"counting_method",eh:"send_as_iframe",Zq:"parameter_order",fh:"parsed_target",ho:"ga4_collection_subdomain",ql:"gbraid_cookie_marked",fa:"hit_type",vd:"hit_type_override",mo:"is_config_command",Hf:"is_consent_update",If:"is_conversion",vl:"is_ecommerce",wd:"is_external_event",Gi:"is_fallback_aw_conversion_ping_allowed",Jf:"is_first_visit",wl:"is_first_visit_conversion",gh:"is_fl_fallback_conversion_flow_allowed",
ke:"is_fpm_encryption",hh:"is_fpm_split",me:"is_gcp_conversion",Hi:"is_google_signals_allowed",xd:"is_merchant_center",xl:"is_new_join_id_required",ih:"is_new_to_site",jh:"is_server_side_destination",ne:"is_session_start",zl:"is_session_start_conversion",gr:"is_sgtm_ga_ads_conversion_study_control_group",hr:"is_sgtm_prehit",Al:"is_sgtm_service_worker",Ii:"is_split_conversion",no:"is_syn",oe:"join_id",Ji:"join_elapsed",Kf:"join_timer_sec",se:"tunnel_updated",lr:"prehit_for_retry",nr:"promises",qr:"record_aw_latency",
Ac:"redact_ads_data",te:"redact_click_ids",zo:"remarketing_only",Jl:"send_ccm_parallel_ping",oh:"send_fledge_experiment",ur:"send_ccm_parallel_test_ping",Pf:"send_to_destinations",Ni:"send_to_targets",Kl:"send_user_data_hit",ib:"source_canonical_id",Ha:"speculative",Pl:"speculative_in_message",Ql:"suppress_script_load",Rl:"syn_or_mod",Vl:"transient_ecsid",Qf:"transmission_type",jb:"user_data",xr:"user_data_from_automatic",yr:"user_data_from_automatic_getter",ve:"user_data_from_code",rh:"user_data_from_manual",
Xl:"user_data_mode",Rf:"user_id_updated"}};var hq={Vm:Number("5"),Pr:Number("")},iq=[],jq=!1;function kq(a){iq.push(a)}var lq="?id="+ig.ctid,mq=void 0,nq={},oq=void 0,pq=new function(){var a=5;hq.Vm>0&&(a=hq.Vm);this.H=a;this.C=0;this.N=[]},qq=1E3;
function rq(a,b){var c=mq;if(c===void 0)if(b)c=wp();else return"";for(var d=[Zk("https://www.googletagmanager.com"),"/a",lq],e=l(iq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function sq(){if(Lj.ba&&(oq&&(x.clearTimeout(oq),oq=void 0),mq!==void 0&&tq)){var a=mn(Mm.X.Qc);if(hn(a))jq||(jq=!0,kn(a,sq));else{var b;if(!(b=nq[mq])){var c=pq;b=c.C<c.H?!1:zb()-c.N[c.C%c.H]<1E3}if(b||qq--<=0)L(1),nq[mq]=!0;else{var d=pq,e=d.C++%d.H;d.N[e]=zb();var f=rq(!0);em({destinationId:ig.ctid,endpoint:56,eventId:mq},f);jq=tq=!1}}}}function uq(){if(el&&Lj.ba){var a=rq(!0,!0);em({destinationId:ig.ctid,endpoint:56,eventId:mq},a)}}var tq=!1;
function vq(a){nq[a]||(a!==mq&&(sq(),mq=a),tq=!0,oq||(oq=x.setTimeout(sq,500)),rq().length>=2022&&sq())}var wq=pb();function xq(){wq=pb()}function yq(){return[["v","3"],["t","t"],["pid",String(wq)]]};var zq={};function Aq(a,b,c){el&&a!==void 0&&(zq[a]=zq[a]||[],zq[a].push(c+b),vq(a))}function Bq(a){var b=a.eventId,c=a.Nd,d=[],e=zq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete zq[b];return d};function Cq(a,b,c,d){var e=Dp(a,!0);e&&Dq.register(e,b,c,d)}function Eq(a,b,c,d){var e=Dp(c,d.isGtmEvent);e&&(Xj&&(d.deferrable=!0),Dq.push("event",[b,a],e,d))}function Fq(a,b,c,d){var e=Dp(c,d.isGtmEvent);e&&Dq.push("get",[a,b],e,d)}function Gq(a){var b=Dp(a,!0),c;b?c=Hq(Dq,b).C:c={};return c}function Iq(a,b){var c=Dp(a,!0);c&&Jq(Dq,c,b)}
var Kq=function(){this.R={};this.C={};this.H={};this.ba=null;this.P={};this.N=!1;this.status=1},Lq=function(a,b,c,d){this.H=zb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Mq=function(){this.destinations={};this.C={};this.commands=[]},Hq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Kq},Nq=function(a,b,c,d){if(d.C){var e=Hq(a,d.C),f=e.ba;if(f){var g=kd(c,null),h=kd(e.R[d.C.id],null),m=kd(e.P,null),n=kd(e.C,null),p=kd(a.C,null),q={};if(el)try{q=
kd(nk,null)}catch(w){L(72)}var r=d.C.prefix,t=function(w){Aq(d.messageContext.eventId,r,w)},v=gq(fq(eq(dq(cq(aq($p(bq(Zp(Yp(Xp(new Wp(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),u=function(){try{Aq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(fl&&w==="config"){var z,C=(z=Dp(y))==null?void 0:z.ids;if(!(C&&C.length>1)){var D,F=vc("google_tag_data",{});F.td||(F.td={});D=F.td;var H=kd(v.P);kd(v.C,H);var M=[],S;for(S in D)D.hasOwnProperty(S)&&Qp(D[S],H).length&&M.push(S);M.length&&(Op(y,M),db("TAGGING",Kp[A.readyState]||14));D[y]=H}}f(d.C.id,b,d.H,v)}catch(ca){Aq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?u():kn(e.ka,u)}}};
Mq.prototype.register=function(a,b,c,d){var e=Hq(this,a);e.status!==3&&(e.ba=b,e.status=3,e.ka=mn(c),Jq(this,a,d||{}),this.flush())};
Mq.prototype.push=function(a,b,c,d){c!==void 0&&(Hq(this,c).status===1&&(Hq(this,c).status=2,this.push("require",[{}],c,{})),Hq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[O.A.Pf]||(d.eventMetadata[O.A.Pf]=[c.destinationId]),d.eventMetadata[O.A.Ni]||(d.eventMetadata[O.A.Ni]=[c.id]));this.commands.push(new Lq(a,c,b,d));d.deferrable||this.flush()};
Mq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Sc:void 0,xh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Hq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Hq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];sb(h,function(t,v){kd(Gb(t,v),b.C)});Jj(h,!0);break;case "config":var m=Hq(this,g);
e.Sc={};sb(f.args[0],function(t){return function(v,u){kd(Gb(v,u),t.Sc)}}(e));var n=!!e.Sc[J.m.rd];delete e.Sc[J.m.rd];var p=g.destinationId===g.id;Jj(e.Sc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Nq(this,J.m.qa,e.Sc,f);m.N=!0;p?kd(e.Sc,m.P):(kd(e.Sc,m.R[g.id]),L(70));d=!0;break;case "event":e.xh={};sb(f.args[0],function(t){return function(v,u){kd(Gb(v,u),t.xh)}}(e));Jj(e.xh);Nq(this,f.args[1],e.xh,f);break;case "get":var q={},r=(q[J.m.sc]=f.args[0],q[J.m.Kc]=f.args[1],q);Nq(this,J.m.Db,r,f)}this.commands.shift();
Oq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Oq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Hq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Jq=function(a,b,c){var d=kd(c,null);kd(Hq(a,b).C,d);Hq(a,b).C=d},Dq=new Mq;function Pq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Qq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Rq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Gl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=oc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Qq(e,"load",f);Qq(e,"error",f)};Pq(e,"load",f);Pq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Sq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Dl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Tq(c,b)}
function Tq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Rq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Uq=function(){this.ba=this.ba;this.P=this.P};Uq.prototype.ba=!1;Uq.prototype.dispose=function(){this.ba||(this.ba=!0,this.N())};Uq.prototype[Symbol.dispose]=function(){this.dispose()};Uq.prototype.addOnDisposeCallback=function(a,b){this.ba?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};Uq.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function Vq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Wq=function(a,b){b=b===void 0?{}:b;Uq.call(this);this.C=null;this.ka={};this.rb=0;this.R=null;this.H=a;var c;this.Sa=(c=b.timeoutMs)!=null?c:500;var d;this.Ba=(d=b.Er)!=null?d:!1};ra(Wq,Uq);Wq.prototype.N=function(){this.ka={};this.R&&(Qq(this.H,"message",this.R),delete this.R);delete this.ka;delete this.H;delete this.C;Uq.prototype.N.call(this)};var Yq=function(a){return typeof a.H.__tcfapi==="function"||Xq(a)!=null};
Wq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ba},d=hl(function(){return a(c)}),e=0;this.Sa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Sa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Vq(c),c.internalBlockOnErrors=b.Ba,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{Zq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Wq.prototype.removeEventListener=function(a){a&&a.listenerId&&Zq(this,"removeEventListener",null,a.listenerId)};
var ar=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=$q(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&$q(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?$q(a.purpose.legitimateInterests,
b)&&$q(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},$q=function(a,b){return!(!a||!a[b])},Zq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Xq(a)){br(a);var g=++a.rb;a.ka[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Xq=function(a){if(a.C)return a.C;a.C=El(a.H,"__tcfapiLocator");return a.C},br=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ka[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Pq(a.H,"message",b)}},cr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Vq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Sq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var dr={1:0,3:0,4:0,7:3,9:3,10:3};function er(){return tp("tcf",function(){return{}})}var fr=function(){return new Wq(x,{timeoutMs:-1})};
function gr(){var a=er(),b=fr();Yq(b)&&!hr()&&!ir()&&L(124);if(!a.active&&Yq(b)){hr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Nm().active=!0,a.tcString="tcunavailable");mp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)jr(a),np([J.m.U,J.m.La,J.m.V]),Nm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,ir()&&(a.active=!0),!kr(c)||hr()||ir()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in dr)dr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(kr(c)){var g={},h;for(h in dr)if(dr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={vp:!0};p=p===void 0?{}:p;m=cr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.vp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?ar(n,"1",0):!0:!1;g["1"]=m}else g[h]=ar(c,h,dr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(np([J.m.U,J.m.La,J.m.V]),Nm().active=!0):(r[J.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":np([J.m.V]),gp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:lr()||""}))}}else np([J.m.U,J.m.La,J.m.V])})}catch(c){jr(a),np([J.m.U,J.m.La,J.m.V]),Nm().active=!0}}}
function jr(a){a.type="e";a.tcString="tcunavailable"}function kr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function hr(){return x.gtag_enable_tcf_support===!0}function ir(){return er().enableAdvertiserConsentMode===!0}function lr(){var a=er();if(a.active)return a.tcString}function mr(){var a=er();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function nr(a){if(!dr.hasOwnProperty(String(a)))return!0;var b=er();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var or=[J.m.U,J.m.ia,J.m.V,J.m.La],pr={},qr=(pr[J.m.U]=1,pr[J.m.ia]=2,pr);function rr(a){if(a===void 0)return 0;switch(N(a,J.m.Ea)){case void 0:return 1;case !1:return 3;default:return 2}}function sr(){return(E(183)?Ui.Bp:Ui.Cp).indexOf(ho())!==-1&&rc.globalPrivacyControl===!0}function tr(a){if(sr())return!1;var b=rr(a);if(b===3)return!1;switch(Wm(J.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function ur(){return Ym()||!Vm(J.m.U)||!Vm(J.m.ia)}function vr(){var a={},b;for(b in qr)qr.hasOwnProperty(b)&&(a[qr[b]]=Wm(b));return"G1"+af(a[1]||0)+af(a[2]||0)}var wr={},xr=(wr[J.m.U]=0,wr[J.m.ia]=1,wr[J.m.V]=2,wr[J.m.La]=3,wr);function yr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function zr(a){for(var b="1",c=0;c<or.length;c++){var d=b,e,f=or[c],g=Um.delegatedConsentTypes[f];e=g===void 0?0:xr.hasOwnProperty(g)?12|xr[g]:8;var h=Nm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|yr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[yr(m.declare)<<4|yr(m.default)<<2|yr(m.update)])}var n=b,p=(sr()?1:0)<<3,q=(Ym()?1:0)<<2,r=rr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Um.containerScopedDefaults.ad_storage<<4|Um.containerScopedDefaults.analytics_storage<<2|Um.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Um.usedContainerScopedDefaults?1:0)<<2|Um.containerScopedDefaults.ad_personalization]}
function Ar(){if(!Vm(J.m.V))return"-";for(var a=Object.keys(Ao),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Um.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Ao[m])}(Um.usedCorePlatformServices?Um.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Br(){return jo()||(hr()||ir())&&mr()==="1"?"1":"0"}function Cr(){return(jo()?!0:!(!hr()&&!ir())&&mr()==="1")||!Vm(J.m.V)}
function Dr(){var a="0",b="0",c;var d=er();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=er();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;jo()&&(h|=1);mr()==="1"&&(h|=2);hr()&&(h|=4);var m;var n=er();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Nm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Er(){return ho()==="US-CO"};var eg;function Fr(){var a=!1;return a}function Gr(){E(212)&&Yj&&fg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}})};var Hr;function Ir(){if(uc===null)return 0;var a=Zc();if(!a)return 0;var b=a.getEntriesByName(uc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Jr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Kr(a){a=a===void 0?{}:a;var b=ig.ctid.split("-")[0].toUpperCase(),c={ctid:ig.ctid,Jj:Rj,Nj:Qj,rm:um.qe?2:1,Dq:a.Lm,xe:ig.canonicalContainerId};if(E(210)){var d;c.tq=(d=Bm())==null?void 0:d.canonicalContainerId}if(E(204)){var e;c.Ro=(e=Hr)!=null?e:Hr=Ir()}c.xe!==a.Ma&&(c.Ma=a.Ma);var f=zm();c.Am=f?f.canonicalContainerId:void 0;Yj?(c.Wc=Jr[b],c.Wc||(c.Wc=0)):c.Wc=ak?13:10;Lj.C?(c.Uc=0,c.bm=2):Lj.N?c.Uc=1:Fr()?c.Uc=2:c.Uc=3;var g={6:!1};Lj.H===2?g[7]=!0:Lj.H===1&&(g[2]=!0);if(uc){var h=Kk(Qk(uc),
"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.fm=g;return df(c,a.th)}
function Lr(){if(!E(192))return Kr();if(E(193))return df({Jj:Rj,Nj:Qj});var a=ig.ctid.split("-")[0].toUpperCase(),b={ctid:ig.ctid,Jj:Rj,Nj:Qj,rm:um.qe?2:1,xe:ig.canonicalContainerId},c=zm();b.Am=c?c.canonicalContainerId:void 0;Yj?(b.Wc=Jr[a],b.Wc||(b.Wc=0)):b.Wc=ak?13:10;Lj.C?(b.Uc=0,b.bm=2):Lj.N?b.Uc=1:Fr()?b.Uc=2:b.Uc=3;var d={6:!1};Lj.H===2?d[7]=!0:Lj.H===1&&(d[2]=!0);if(uc){var e=Kk(Qk(uc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.fm=d;return df(b)};function Mr(a,b,c,d){var e,f=Number(a.Dc!=null?a.Dc:void 0);f!==0&&(e=new Date((b||zb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Fc:d}};var Nr=["ad_storage","ad_user_data"];function Or(a,b){if(!a)return db("TAGGING",32),10;if(b===null||b===void 0||b==="")return db("TAGGING",33),11;var c=Pr(!1);if(c.error!==0)return db("TAGGING",34),c.error;if(!c.value)return db("TAGGING",35),2;c.value[a]=b;var d=Qr(c);d!==0&&db("TAGGING",36);return d}
function Rr(a){if(!a)return db("TAGGING",27),{error:10};var b=Pr();if(b.error!==0)return db("TAGGING",29),b;if(!b.value)return db("TAGGING",30),{error:2};if(!(a in b.value))return db("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(db("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Pr(a){a=a===void 0?!0:a;if(!Vm(Nr))return db("TAGGING",43),{error:3};try{if(!x.localStorage)return db("TAGGING",44),{error:1}}catch(f){return db("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return db("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return db("TAGGING",47),{error:12}}}catch(f){return db("TAGGING",48),{error:8}}if(b.schema!=="gcl")return db("TAGGING",49),{error:4};
if(b.version!==1)return db("TAGGING",50),{error:5};try{var e=Sr(b);a&&e&&Qr({value:b,error:0})}catch(f){return db("TAGGING",48),{error:8}}return{value:b,error:0}}
function Sr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,db("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Sr(a[e.value])||c;return c}return!1}
function Qr(a){if(a.error)return a.error;if(!a.value)return db("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return db("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return db("TAGGING",53),7}return 0};var Tr={xj:"value",Hb:"conversionCount"},Ur=[Tr,{qm:10,Fm:11,xj:"timeouts",Hb:"timeouts"}];function Vr(){var a=Tr;if(!Wr(a))return{};var b=Xr(Ur),c=b[a.Hb];if(c===void 0||c===-1)return b;var d={},e=Object.assign({},b,(d[a.Hb]=c+1,d));return Yr(e)?e:b}
function Xr(a){var b;a:{var c=Rr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Wr(m)){var n=e[m.xj];n===void 0||Number.isNaN(n)?f[m.Hb]=-1:f[m.Hb]=Number(n)}else f[m.Hb]=-1}return f}
function Yr(a,b){b=b||{};for(var c=zb(),d=Mr(b,c,!0),e={},f=l(Ur),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.Hb];m!==void 0&&m!==-1&&(e[h.xj]=m)}e.creationTimeMs=c;return Or("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Wr(a){return Vm(["ad_storage","ad_user_data"])?!a.Fm||Ja(a.Fm):!1}function Zr(a){return Vm(["ad_storage","ad_user_data"])?!a.qm||Ja(a.qm):!1};function $r(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var as={O:{Ao:0,Uj:1,wg:2,bk:3,Nh:4,Yj:5,Zj:6,dk:7,Oh:8,kl:9,jl:10,zi:11,ml:12,ah:13,pl:14,Mf:15,xo:16,ue:17,Si:18,Ti:19,Ui:20,Tl:21,Vi:22,Qh:23,kk:24}};as.O[as.O.Ao]="RESERVED_ZERO";as.O[as.O.Uj]="ADS_CONVERSION_HIT";as.O[as.O.wg]="CONTAINER_EXECUTE_START";as.O[as.O.bk]="CONTAINER_SETUP_END";as.O[as.O.Nh]="CONTAINER_SETUP_START";as.O[as.O.Yj]="CONTAINER_BLOCKING_END";as.O[as.O.Zj]="CONTAINER_EXECUTE_END";as.O[as.O.dk]="CONTAINER_YIELD_END";as.O[as.O.Oh]="CONTAINER_YIELD_START";as.O[as.O.kl]="EVENT_EXECUTE_END";
as.O[as.O.jl]="EVENT_EVALUATION_END";as.O[as.O.zi]="EVENT_EVALUATION_START";as.O[as.O.ml]="EVENT_SETUP_END";as.O[as.O.ah]="EVENT_SETUP_START";as.O[as.O.pl]="GA4_CONVERSION_HIT";as.O[as.O.Mf]="PAGE_LOAD";as.O[as.O.xo]="PAGEVIEW";as.O[as.O.ue]="SNIPPET_LOAD";as.O[as.O.Si]="TAG_CALLBACK_ERROR";as.O[as.O.Ti]="TAG_CALLBACK_FAILURE";as.O[as.O.Ui]="TAG_CALLBACK_SUCCESS";as.O[as.O.Tl]="TAG_EXECUTE_END";as.O[as.O.Vi]="TAG_EXECUTE_START";as.O[as.O.Qh]="CUSTOM_PERFORMANCE_START";as.O[as.O.kk]="CUSTOM_PERFORMANCE_END";var bs=[],cs={},ds={};var es=["1"];function fs(a){return a.origin!=="null"};function gs(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ja(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function hs(a,b,c,d){if(!is(d))return[];if(bs.includes("1")){var e;(e=Zc())==null||e.mark("1-"+as.O.Qh+"-"+(ds["1"]||0))}var f=gs(a,String(b||js()),c);if(bs.includes("1")){var g="1-"+as.O.kk+"-"+(ds["1"]||0),h={start:"1-"+as.O.Qh+"-"+(ds["1"]||0),end:g},m;(m=Zc())==null||m.mark(g);var n,p,q=(p=(n=Zc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(ds["1"]=(ds["1"]||0)+1,cs["1"]=q+(cs["1"]||0))}return f}
function ks(a,b,c,d,e){if(is(e)){var f=ls(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=ms(f,function(g){return g.ep},b);if(f.length===1)return f[0];f=ms(f,function(g){return g.hq},c);return f[0]}}}function ns(a,b,c,d){var e=js(),f=window;fs(f)&&(f.document.cookie=a);var g=js();return e!==g||c!==void 0&&hs(b,g,!1,d).indexOf(c)>=0}
function os(a,b,c,d){function e(w,y,z){if(z==null)return delete h[y],w;h[y]=z;return w+"; "+y+"="+z}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!is(c.Fc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ps(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.bq);g=e(g,"samesite",c.uq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=qs(),q=void 0,r=!1,t=0;t<p.length;++t){var v=p[t]!=="none"?p[t]:void 0,u=e(g,"domain",v);u=f(u,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!rs(v,c.path)&&ns(u,a,b,c.Fc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return rs(n,c.path)?1:ns(g,a,b,c.Fc)?0:1}function ss(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return os(a,b,c)}
function ms(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ls(a,b,c){for(var d=[],e=hs(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Vo:e[f],Wo:g.join("."),ep:Number(n[0])||1,hq:Number(n[1])||1})}}}return d}function ps(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var ts=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,us=/(^|\.)doubleclick\.net$/i;function rs(a,b){return a!==void 0&&(us.test(window.document.location.hostname)||b==="/"&&ts.test(a))}function vs(a){if(!a)return 1;var b=a;Ja(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function ws(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function xs(a,b){var c=""+vs(a),d=ws(b);d>1&&(c+="-"+d);return c}
var js=function(){return fs(window)?window.document.cookie:""},is=function(a){return a&&Ja(8)?(Array.isArray(a)?a:[a]).every(function(b){return Xm(b)&&Vm(b)}):!0},qs=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;us.test(e)||ts.test(e)||a.push("none");return a};function ys(a){var b=Math.round(Math.random()*2147483647);return a?String(b^$r(a)&2147483647):String(b)}function zs(a){return[ys(a),Math.round(zb()/1E3)].join(".")}function As(a,b,c,d,e){var f=vs(b),g;return(g=ks(a,f,ws(c),d,e))==null?void 0:g.Wo};var Bs;function Cs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ds,d=Es,e=Fs();if(!e.init){Ic(A,"mousedown",a);Ic(A,"keyup",a);Ic(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Gs(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Fs().decorators.push(f)}
function Hs(a,b,c){for(var d=Fs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Cb(e,g.callback())}}return e}
function Fs(){var a=vc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Is=/(.*?)\*(.*?)\*(.*)/,Js=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ks=/^(?:www\.|m\.|amp\.)+/,Ls=/([^?#]+)(\?[^#]*)?(#.*)?/;function Ms(a){var b=Ls.exec(a);if(b)return{Dj:b[1],query:b[2],fragment:b[3]}}function Ns(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Os(a,b){var c=[rc.userAgent,(new Date).getTimezoneOffset(),rc.userLanguage||rc.language,Math.floor(zb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Bs)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Bs=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Bs[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ps(a){return function(b){var c=Qk(x.location.href),d=c.search.replace("?",""),e=Hk(d,"_gl",!1,!0)||"";b.query=Qs(e)||{};var f=Kk(c,"fragment"),g;var h=-1;if(Eb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Qs(g||"")||{};a&&Rs(c,d,f)}}function Ss(a,b){var c=Ns(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Rs(a,b,c){function d(g,h){var m=Ss("_gl",g);m.length&&(m=h+m);return m}if(qc&&qc.replaceState){var e=Ns("_gl");if(e.test(b)||e.test(c)){var f=Kk(a,"path");b=d(b,"?");c=d(c,"#");qc.replaceState({},"",""+f+b+c)}}}function Ts(a,b){var c=Ps(!!b),d=Fs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Cb(e,f.query),a&&Cb(e,f.fragment));return e}
var Qs=function(a){try{var b=Us(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=bb(d[e+1]);c[f]=g}db("TAGGING",6);return c}}catch(h){db("TAGGING",8)}};function Us(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Is.exec(d);if(f){c=f;break a}d=Jk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Os(h,p)){m=!0;break a}m=!1}if(m)return h;db("TAGGING",7)}}}
function Vs(a,b,c,d,e){function f(p){p=Ss(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Ms(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Dj+h+m}
function Ws(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var v,u=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(u.push(w),u.push(ab(String(y))))}var z=u.join("*");v=["1",Os(z),z].join("*");d?(Ja(3)||Ja(1)||!p)&&Xs("_gl",v,a,p,q):Ys("_gl",v,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Hs(b,1,d),f=Hs(b,2,d),g=Hs(b,4,d),h=Hs(b,3,d);c(e,!1,!1);c(f,!0,!1);Ja(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
Zs(m,h[m],a)}function Zs(a,b,c){c.tagName.toLowerCase()==="a"?Ys(a,b,c):c.tagName.toLowerCase()==="form"&&Xs(a,b,c)}function Ys(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ja(5)||d)){var h=x.location.href,m=Ms(c.href),n=Ms(h);g=!(m&&n&&m.Dj===n.Dj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Vs(a,b,c.href,d,e);fc.test(p)&&(c.href=p)}}
function Xs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Vs(a,b,f,d,e);fc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ds(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Ws(e,e.hostname)}}catch(g){}}function Es(a){try{var b=a.getAttribute("action");if(b){var c=Kk(Qk(b),"host");Ws(a,c)}}catch(d){}}function $s(a,b,c,d){Cs();var e=c==="fragment"?2:1;d=!!d;Gs(a,b,e,d,!1);e===2&&db("TAGGING",23);d&&db("TAGGING",24)}
function at(a,b){Cs();Gs(a,[Mk(x.location,"host",!0)],b,!0,!0)}function bt(){var a=A.location.hostname,b=Js.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Jk(f[2])||"":Jk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ks,""),m=e.replace(Ks,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ct(a,b){return a===!1?!1:a||b||bt()};var dt=["1"],et={},ft={};function gt(a,b){b=b===void 0?!0:b;var c=ht(a.prefix);if(et[c])it(a);else if(jt(c,a.path,a.domain)){var d=ft[ht(a.prefix)]||{id:void 0,Fh:void 0};b&&kt(a,d.id,d.Fh);it(a)}else{var e=Sk("auiddc");if(e)db("TAGGING",17),et[c]=e;else if(b){var f=ht(a.prefix),g=zs();lt(f,g,a);jt(c,a.path,a.domain);it(a,!0)}}}
function it(a,b){if((b===void 0?0:b)&&Wr(Tr)){var c=Pr(!1);c.error!==0?db("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Qr(c)!==0&&db("TAGGING",41)):db("TAGGING",40):db("TAGGING",39)}if(Zr(Tr)&&Xr([Tr])[Tr.Hb]===-1){for(var d={},e=(d[Tr.Hb]=0,d),f=l(Ur),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Tr&&Zr(h)&&(e[h.Hb]=0)}Yr(e,a)}}
function kt(a,b,c){var d=ht(a.prefix),e=et[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(zb()/1E3)));lt(d,h,a,g*1E3)}}}}function lt(a,b,c,d){var e;e=["1",xs(c.domain,c.path),b].join(".");var f=Mr(c,d);f.Fc=mt();ss(a,e,f)}function jt(a,b,c){var d=As(a,b,c,dt,mt());if(!d)return!1;nt(a,d);return!0}
function nt(a,b){var c=b.split(".");c.length===5?(et[a]=c.slice(0,2).join("."),ft[a]={id:c.slice(2,4).join("."),Fh:Number(c[4])||0}):c.length===3?ft[a]={id:c.slice(0,2).join("."),Fh:Number(c[2])||0}:et[a]=b}function ht(a){return(a||"_gcl")+"_au"}function ot(a){function b(){Vm(c)&&a()}var c=mt();an(function(){b();Vm(c)||bn(b,c)},c)}
function pt(a){var b=Ts(!0),c=ht(a.prefix);ot(function(){var d=b[c];if(d){nt(c,d);var e=Number(et[c].split(".")[1])*1E3;if(e){db("TAGGING",16);var f=Mr(a,e);f.Fc=mt();var g=["1",xs(a.domain,a.path),d].join(".");ss(c,g,f)}}})}function qt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=As(a,e.path,e.domain,dt,mt());h&&(g[a]=h);return g};ot(function(){$s(f,b,c,d)})}function mt(){return["ad_storage","ad_user_data"]};function rt(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Qj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function st(a,b){var c=rt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Qj]||(d[c[e].Qj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Qj].push(g)}}return d};var tt={},ut=(tt.k={da:/^[\w-]+$/},tt.b={da:/^[\w-]+$/,Kj:!0},tt.i={da:/^[1-9]\d*$/},tt.h={da:/^\d+$/},tt.t={da:/^[1-9]\d*$/},tt.d={da:/^[A-Za-z0-9_-]+$/},tt.j={da:/^\d+$/},tt.u={da:/^[1-9]\d*$/},tt.l={da:/^[01]$/},tt.o={da:/^[1-9]\d*$/},tt.g={da:/^[01]$/},tt.s={da:/^.+$/},tt);var vt={},At=(vt[5]={Kh:{2:wt},wj:"2",uh:["k","i","b","u"]},vt[4]={Kh:{2:wt,GCL:xt},wj:"2",uh:["k","i","b"]},vt[2]={Kh:{GS2:wt,GS1:zt},wj:"GS2",uh:"sogtjlhd".split("")},vt);function Bt(a,b,c){var d=At[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Kh[e];if(f)return f(a,b)}}}
function wt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=At[b];if(f){for(var g=f.uh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=ut[p];r&&(r.Kj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Ct(a,b,c){var d=At[b];if(d)return[d.wj,c||"1",Dt(a,b)].join(".")}
function Dt(a,b){var c=At[b];if(c){for(var d=[],e=l(c.uh),f=e.next();!f.done;f=e.next()){var g=f.value,h=ut[g];if(h){var m=a[g];if(m!==void 0)if(h.Kj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function xt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function zt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Et=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Ft(a,b,c){if(At[b]){for(var d=[],e=hs(a,void 0,void 0,Et.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Bt(g.value,b,c);h&&d.push(Gt(h))}return d}}function Ht(a,b,c,d,e){d=d||{};var f=xs(d.domain,d.path),g=Ct(b,c,f);if(!g)return 1;var h=Mr(d,e,void 0,Et.get(c));return ss(a,g,h)}function It(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Gt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Uf:void 0},c=b.next()){var e=c.value,f=a[e];d.Uf=ut[e];d.Uf?d.Uf.Kj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return It(h,g.Uf)}}(d)):void 0:typeof f==="string"&&It(f,d.Uf)||(a[e]=void 0):a[e]=void 0}return a};var Jt=function(){this.value=0};Jt.prototype.set=function(a){return this.value|=1<<a};var Kt=function(a,b){b<=0||(a.value|=1<<b-1)};Jt.prototype.get=function(){return this.value};Jt.prototype.clear=function(a){this.value&=~(1<<a)};Jt.prototype.clearAll=function(){this.value=0};Jt.prototype.equals=function(a){return this.value===a.value};function Lt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Mt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]}
function Nt(a){if(!a||a.length<50||a.length>200)return!1;var b=Lt(a),c;if(b)a:{if(b&&b.length!==0){var d=0;try{for(;d<b.length;){var e=Mt(b,d);if(e===void 0)break;var f=l(e),g=f.next().value,h=f.next().value,m=g,n=h,p=m&7;if(m>>3===16382){if(p!==0)break;var q=Mt(b,n);if(q===void 0)break;c=l(q).next().value===1;break a}var r;b:{var t=void 0;switch(p){case 0:r=(t=Mt(b,n))==null?void 0:t[1];break b;case 1:r=n+8;break b;case 2:var v=Mt(b,n);if(v===void 0)break;var u=l(v),w=u.next().value;r=u.next().value+
w;break b;case 5:r=n+4;break b}r=void 0}var y=r;if(y===void 0||y>b.length)break;d=y}}catch(z){}}c=!1}else c=!1;return c};function Ot(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Mb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Mb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a($r((""+b+e).toLowerCase()))};var Pt={},Qt=(Pt.gclid=!0,Pt.dclid=!0,Pt.gbraid=!0,Pt.wbraid=!0,Pt),Rt=/^\w+$/,St=/^[\w-]+$/,Tt={},Ut=(Tt.aw="_aw",Tt.dc="_dc",Tt.gf="_gf",Tt.gp="_gp",Tt.gs="_gs",Tt.ha="_ha",Tt.ag="_ag",Tt.gb="_gb",Tt),Vt=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Wt=/^www\.googleadservices\.com$/;function Xt(){return["ad_storage","ad_user_data"]}function Yt(a){return!Ja(8)||Vm(a)}function Zt(a,b){function c(){var d=Yt(b);d&&a();return d}an(function(){c()||bn(c,b)},b)}
function $t(a){return au(a).map(function(b){return b.gclid})}function bu(a){return cu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function cu(a){var b=du(a.prefix),c=eu("gb",b),d=eu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=au(c).map(e("gb")),g=fu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function gu(a,b,c,d,e,f){var g=ob(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Gd=f),g.labels=hu(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Gd:f})}function fu(a){for(var b=Ft(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=iu(f);if(n){var p=void 0;Ja(9)&&(p=f.u);gu(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}
function au(a){for(var b=[],c=hs(a,A.cookie,void 0,Xt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=ju(e.value);if(f!=null){var g=f;gu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ku(b)}function lu(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function mu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Jt,q=(n=b.Ka)!=null?n:new Jt;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Gd=b.Gd);d.labels=lu(d.labels||[],b.labels||[]);d.Cb=lu(d.Cb||[],b.Cb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function nu(a){if(!a)return new Jt;var b=new Jt;if(a===1)return Kt(b,2),Kt(b,3),b;Kt(b,a);return b}
function ou(){var a=Rr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(St))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Jt;typeof e==="number"?g=nu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Cb:[2]}}catch(h){return null}}
function pu(){var a=Rr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(St))return b;var f=new Jt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Cb:[2]});return b},[])}catch(b){return null}}
function qu(a){for(var b=[],c=hs(a,A.cookie,void 0,Xt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=ju(e.value);f!=null&&(f.Gd=void 0,f.Ka=new Jt,f.Cb=[1],mu(b,f))}var g=ou();g&&(g.Gd=void 0,g.Cb=g.Cb||[2],mu(b,g));if(Ja(14)){var h=pu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Gd=void 0;p.Cb=p.Cb||[2];mu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ku(b)}
function hu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function du(a){return a&&typeof a==="string"&&a.match(Rt)?a:"_gcl"}function ru(a,b){if(a){var c={value:a,Ka:new Jt};Kt(c.Ka,b);return c}}
function su(a,b,c,d){var e=Qk(a),f=Kk(e,"query",!1,void 0,"gclsrc"),g=ru(Kk(e,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!g||!f)){var h=e.hash.replace("#","");g||(g=ru(Hk(h,"gclid",!1),3));f||(f=Hk(h,"gclsrc",!1))}var m;if(d&&!Nt((m=g)==null?void 0:m.value)){var n;a:{for(var p=Ik(Kk(e,"query")),q=l(Object.keys(p)),r=q.next();!r.done;r=q.next()){var t=r.value;if(!Qt[t]){var v=p[t][0]||"";if(Nt(v)){n=v;break a}}}n=void 0}var u=n,w;u&&u!==((w=g)==null?void 0:w.value)&&(g=ru(u,7))}return!g||f!==void 0&&
f!=="aw"&&f!=="aw.ds"?[]:[g]}function tu(a,b){var c=Qk(a),d=Kk(c,"query",!1,void 0,"gclid"),e=Kk(c,"query",!1,void 0,"gclsrc"),f=Kk(c,"query",!1,void 0,"wbraid");f=Kb(f);var g=Kk(c,"query",!1,void 0,"gbraid"),h=Kk(c,"query",!1,void 0,"gad_source"),m=Kk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Hk(n,"gclid",!1);e=e||Hk(n,"gclsrc",!1);f=f||Hk(n,"wbraid",!1);g=g||Hk(n,"gbraid",!1);h=h||Hk(n,"gad_source",!1)}return uu(d,e,m,f,g,h)}
function vu(){return tu(x.location.href,!0)}
function uu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(St))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&St.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&St.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&St.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function wu(a){for(var b=vu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=tu(x.document.referrer,!1),b.gad_source=void 0);xu(b,!1,a)}
function yu(a){wu(a);var b=su(x.location.href,!0,!1,Ja(15)?zu(Au())||!!Bu():!1);b.length||(b=su(x.document.referrer,!1,!0,!1));if(b.length){var c=b[0];a=a||{};var d=zb(),e=Mr(a,d,!0),f=Xt(),g=function(){Yt(f)&&e.expires!==void 0&&Or("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};an(function(){g();Yt(f)||bn(g,f)},f)}}
function Cu(a,b,c){c=c||{};var d=zb(),e=Mr(c,d,!0),f=Xt(),g=function(){if(Yt(f)&&e.expires!==void 0){var h=pu()||[];mu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:nu(b)},!0);Or("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};an(function(){Yt(f)?g():bn(g,f)},f)}
function xu(a,b,c,d,e){c=c||{};e=e||[];var f=du(c.prefix),g=d||zb(),h=Math.round(g/1E3),m=Xt(),n=!1,p=!1,q=function(){if(Yt(m)){var r=Mr(c,g,!0);r.Fc=m;for(var t=function(S,ca){var U=eu(S,f);U&&(ss(U,ca,r),S!=="gb"&&(n=!0))},v=function(S){var ca=["GCL",h,S];e.length>0&&ca.push(e.join("."));return ca.join(".")},u=l(["aw","dc","gf","ha","gp"]),w=u.next();!w.done;w=u.next()){var y=w.value;a[y]&&t(y,v(a[y][0]))}if(!n&&a.gb){var z=a.gb[0],C=eu("gb",f);!b&&au(C).some(function(S){return S.gclid===z&&S.labels&&
S.labels.length>0})||t("gb",v(z))}}if(!p&&a.gbraid&&Yt("ad_storage")&&(p=!0,!n)){var D=a.gbraid,F=eu("ag",f);if(b||!fu(F).some(function(S){return S.gclid===D&&S.labels&&S.labels.length>0})){var H={},M=(H.k=D,H.i=""+h,H.b=e,H);Ht(F,M,5,c,g)}}Du(a,f,g,c)};an(function(){q();Yt(m)||bn(q,m)},m)}
function Du(a,b,c,d){if(a.gad_source!==void 0&&Yt("ad_storage")){if(Ja(4)){var e=Yc();if(e==="r"||e==="h")return}var f=a.gad_source,g=eu("gs",b);if(g){var h=Math.floor((zb()-(Xc()||0))/1E3),m;if(Ja(9)){var n=Ot(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Ht(g,m,5,d,c)}}}
function Eu(a,b){var c=Ts(!0);Zt(function(){for(var d=du(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Ut[f]!==void 0){var g=eu(f,d),h=c[g];if(h){var m=Math.min(Fu(h),zb()),n;b:{for(var p=m,q=hs(g,A.cookie,void 0,Xt()),r=0;r<q.length;++r)if(Fu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Mr(b,m,!0);t.Fc=Xt();ss(g,h,t)}}}}xu(uu(c.gclid,c.gclsrc),!1,b)},Xt())}
function Gu(a){var b=["ag"],c=Ts(!0),d=du(a.prefix);Zt(function(){for(var e=0;e<b.length;++e){var f=eu(b[e],d);if(f){var g=c[f];if(g){var h=Bt(g,5);if(h){var m=iu(h);m||(m=zb());var n;a:{for(var p=m,q=Ft(f,5),r=0;r<q.length;++r)if(iu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ht(f,h,5,a,m)}}}}},["ad_storage"])}function eu(a,b){var c=Ut[a];if(c!==void 0)return b+c}function Fu(a){return Hu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function iu(a){return a?(Number(a.i)||0)*1E3:0}function ju(a){var b=Hu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Hu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!St.test(a[2])?[]:a}
function Iu(a,b,c,d,e){if(Array.isArray(b)&&fs(x)){var f=du(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=eu(a[m],f);if(n){var p=hs(n,A.cookie,void 0,Xt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};Zt(function(){$s(g,b,c,d)},Xt())}}
function Ju(a,b,c,d){if(Array.isArray(a)&&fs(x)){var e=["ag"],f=du(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=eu(e[m],f);if(!n)return{};var p=Ft(n,5);if(p.length){var q=p.sort(function(r,t){return iu(t)-iu(r)})[0];h[n]=Ct(q,5)}}return h};Zt(function(){$s(g,a,b,c)},["ad_storage"])}}function ku(a){return a.filter(function(b){return St.test(b.gclid)})}
function Ku(a,b){if(fs(x)){for(var c=du(b.prefix),d={},e=0;e<a.length;e++)Ut[a[e]]&&(d[a[e]]=Ut[a[e]]);Zt(function(){sb(d,function(f,g){var h=hs(c+g,A.cookie,void 0,Xt());h.sort(function(t,v){return Fu(v)-Fu(t)});if(h.length){var m=h[0],n=Fu(m),p=Hu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Hu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];xu(q,!0,b,n,p)}})},Xt())}}
function Lu(a){var b=["ag"],c=["gbraid"];Zt(function(){for(var d=du(a.prefix),e=0;e<b.length;++e){var f=eu(b[e],d);if(!f)break;var g=Ft(f,5);if(g.length){var h=g.sort(function(q,r){return iu(r)-iu(q)})[0],m=iu(h),n=h.b,p={};p[c[e]]=h.k;xu(p,!0,a,m,n)}}},["ad_storage"])}function Mu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Nu(a){function b(h,m,n){n&&(h[m]=n)}if(Ym()){var c=vu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Ts(!1)._gs);if(Mu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);at(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);at(function(){return g},1)}}}function Bu(){var a=Qk(x.location.href);return Kk(a,"query",!1,void 0,"gad_source")}
function Ou(a){if(!Ja(1))return null;var b=Ts(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ja(2)){b=Bu();if(b!=null)return b;var c=vu();if(Mu(c,a))return"0"}return null}function Pu(a){var b=Ou(a);b!=null&&at(function(){var c={};return c.gad_source=b,c},4)}function Qu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Ru(a,b,c,d){var e=[];c=c||{};if(!Yt(Xt()))return e;var f=au(a),g=Qu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Mr(c,p,!0);r.Fc=Xt();ss(a,q,r)}return e}
function Su(a,b){var c=[];b=b||{};var d=cu(b),e=Qu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=du(b.prefix),n=eu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,v=p.timestamp,u=Math.round(v/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+u,w.b=(t||[]).concat([a]),w);Ht(n,y,5,b,v)}else if(h.type==="gb"){var z=[q,u,r].concat(t||[],[a]).join("."),C=Mr(b,v,!0);C.Fc=Xt();ss(n,z,C)}}return c}
function Tu(a,b){var c=du(b),d=eu(a,c);if(!d)return 0;var e;e=a==="ag"?fu(d):au(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Uu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Vu(a){var b=Math.max(Tu("aw",a),Uu(Yt(Xt())?st():{})),c=Math.max(Tu("gb",a),Uu(Yt(Xt())?st("_gac_gb",!0):{}));c=Math.max(c,Tu("ag",a));return c>b}
function zu(a){return Vt.test(a)||Wt.test(a)}function Au(){return A.referrer?Kk(Qk(A.referrer),"host"):""};
var Wu=function(a,b){b=b===void 0?!1:b;var c=tp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},Xu=function(a){return Rk(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},dv=function(a,b,c,d,e){var f=du(a.prefix);if(Wu(f,!0)){var g=vu(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=Yu(),r=q.Zf,t=q.km;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Cd:p});n&&h.push({gclid:n,Cd:"ds"});h.length===2&&L(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Cd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Cd:"aw.ds"});Zu(function(){var v=ip($u());if(v){gt(a);var u=[],w=v?et[ht(a.prefix)]:void 0;w&&u.push("auid="+w);if(ip(J.m.V)){e&&u.push("userId="+e);var y=rn(nn.Z.Nl);if(y===void 0)qn(nn.Z.Ol,!0);else{var z=rn(nn.Z.ph);u.push("ga_uid="+z+"."+y)}}var C=Au(),D=v||!d?h:[];D.length===0&&zu(C)&&D.push({gclid:"",Cd:""});if(D.length!==0||r!==void 0){C&&u.push("ref="+encodeURIComponent(C));var F=av();u.push("url="+encodeURIComponent(F));
u.push("tft="+zb());var H=Xc();H!==void 0&&u.push("tfd="+Math.round(H));var M=Fl(!0);u.push("frm="+M);r!==void 0&&u.push("gad_source="+encodeURIComponent(r));t!==void 0&&u.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var S={};c=gq(Xp(new Wp(0),(S[J.m.Ea]=Dq.C[J.m.Ea],S)))}u.push("gtm="+Kr({Ma:b}));ur()&&u.push("gcs="+vr());u.push("gcd="+zr(c));Cr()&&u.push("dma_cps="+Ar());u.push("dma="+Br());tr(c)?u.push("npa=0"):u.push("npa=1");Er()&&u.push("_ng=1");Yq(fr())&&u.push("tcfd="+Dr());
var ca=mr();ca&&u.push("gdpr="+ca);var U=lr();U&&u.push("gdpr_consent="+U);E(23)&&u.push("apve=0");E(123)&&Ts(!1)._up&&u.push("gtm_up=1");ik()&&u.push("tag_exp="+ik());if(D.length>0)for(var oa=0;oa<D.length;oa++){var T=D[oa],Z=T.gclid,Y=T.Cd;if(!bv(a.prefix,Y+"."+Z,w!==void 0)){var V=cv+"?"+u.join("&");Z!==""?V=Y==="gb"?V+"&wbraid="+Z:V+"&gclid="+Z+"&gclsrc="+Y:Y==="aw.ds"&&(V+="&gclsrc=aw.ds");Pc(V)}}else if(r!==void 0&&!bv(a.prefix,"gad",w!==void 0)){var ka=cv+"?"+u.join("&");Pc(ka)}}}})}},bv=function(a,
b,c){var d=tp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},Yu=function(){var a=Qk(x.location.href),b=void 0,c=void 0,d=Kk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(ev);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Zf:b,km:c}},av=function(){var a=Fl(!1)===1?x.top.location.href:x.location.href;return a=a.replace(/[\?#].*$/,"")},fv=function(a){var b=[];sb(a,function(c,d){d=ku(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);
e.length&&b.push(c+":"+e.join(","))});return b.join(";")},hv=function(a,b){return gv("dc",a,b)},iv=function(a,b){return gv("aw",a,b)},gv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Sk("gcl"+a);if(d)return d.split(".")}var e=du(b);if(e==="_gcl"){var f=!ip($u())&&c,g;g=vu()[a]||[];if(g.length>0)return f?["0"]:g}var h=eu(a,e);return h?$t(h):[]},Zu=function(a){var b=$u();lp(function(){a();ip(b)||bn(a,b)},b)},$u=function(){return[J.m.U,J.m.V]},cv=Oi(36,'https://adservice.google.com/pagead/regclk'),
ev=/^gad_source[_=](\d+)$/;function jv(){return tp("dedupe_gclid",function(){return zs()})};var kv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,lv=/^www.googleadservices.com$/;function mv(a){a||(a=nv());return a.Lq?!1:a.Jp||a.Kp||a.Np||a.Lp||a.Zf||a.up||a.Mp||a.zp?!0:!1}function nv(){var a={},b=Ts(!0);a.Lq=!!b._up;var c=vu();a.Jp=c.aw!==void 0;a.Kp=c.dc!==void 0;a.Np=c.wbraid!==void 0;a.Lp=c.gbraid!==void 0;a.Mp=c.gclsrc==="aw.ds";a.Zf=Yu().Zf;var d=A.referrer?Kk(Qk(A.referrer),"host"):"";a.zp=kv.test(d);a.up=lv.test(d);return a};function ov(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function pv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function qv(){return["ad_storage","ad_user_data"]}function rv(a){if(E(38)&&!rn(nn.Z.Cl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{ov(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(qn(nn.Z.Cl,function(d){d.gclid&&Cu(d.gclid,5,a)}),pv(c)||L(178))})}catch(c){L(177)}};an(function(){Yt(qv())?b():bn(b,qv())},qv())}};var sv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function tv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?qn(nn.Z.Of,{gadSource:a.data.gadSource}):L(173)}
function uv(a,b){if(E(a)){if(rn(nn.Z.Of))return L(176),nn.Z.Of;if(rn(nn.Z.El))return L(170),nn.Z.Of;var c=Hl();if(!c)L(171);else if(c.opener){var d=function(g){if(sv.includes(g.origin)){a===119?tv(g):a===200&&(tv(g),g.data.gclid&&Cu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Qq(c,"message",d)}else L(172)};if(Pq(c,"message",d)){qn(nn.Z.El,!0);for(var e=l(sv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);L(174);return nn.Z.Of}L(175)}}}
;var vv=function(){this.C=this.gppString=void 0};vv.prototype.reset=function(){this.C=this.gppString=void 0};var wv=new vv;var xv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),yv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,zv=/^\d+\.fls\.doubleclick\.net$/,Av=/;gac=([^;?]+)/,Bv=/;gacgb=([^;?]+)/;
function Cv(a,b){if(zv.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(xv)?Jk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Dv(a,b,c){for(var d=Yt(Xt())?st("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Ru("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{tp:f?e.join(";"):"",rp:Cv(d,Bv)}}function Ev(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(yv)?b[1]:void 0}
function Fv(a){var b=Ja(9),c={},d,e,f;zv.test(A.location.host)&&(d=Ev("gclgs"),e=Ev("gclst"),b&&(f=Ev("gcllp")));if(d&&e&&(!b||f))c.yh=d,c.Ah=e,c.zh=f;else{var g=zb(),h=fu((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Gd}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.yh=m.join("."),c.Ah=n.join("."),b&&p.length>0&&(c.zh=p.join(".")))}return c}
function Gv(a,b,c,d){d=d===void 0?!1:d;if(zv.test(A.location.host)){var e=Ev(c);if(e){if(d){var f=new Jt;Kt(f,2);Kt(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Cb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?qu(g):au(g)}if(b==="wbraid")return au((a||"_gcl")+"_gb");if(b==="braids")return cu({prefix:a})}return[]}function Hv(a){return zv.test(A.location.host)?!(Ev("gclaw")||Ev("gac")):Vu(a)}
function Iv(a,b,c){var d;d=c?Su(a,b):Ru((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Jv(){var a=x.__uspapi;if(jb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var Ov=function(a){if(a.eventName===J.m.qa&&P(a,O.A.fa)===K.J.Ga)if(E(24)){Q(a,O.A.te,N(a.D,J.m.za)!=null&&N(a.D,J.m.za)!==!1&&!ip([J.m.U,J.m.V]));var b=Kv(a),c=N(a.D,J.m.Oa)!==!1;c||R(a,J.m.Vh,"1");var d=du(b.prefix),e=P(a,O.A.jh);if(!P(a,O.A.ja)&&!P(a,O.A.Rf)&&!P(a,O.A.se)){var f=N(a.D,J.m.Fb),g=N(a.D,J.m.Pa)||{};Lv({ye:c,Ee:g,He:f,Tc:b});if(!e&&!Wu(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{R(a,J.m.kd,J.m.bd);if(P(a,O.A.ja))R(a,J.m.kd,J.m.rn),R(a,J.m.ja,"1");else if(P(a,O.A.Rf))R(a,J.m.kd,
J.m.Bn);else if(P(a,O.A.se))R(a,J.m.kd,J.m.yn);else{var h=vu();R(a,J.m.dd,h.gclid);R(a,J.m.hd,h.dclid);R(a,J.m.sk,h.gclsrc);Mv(a,J.m.dd)||Mv(a,J.m.hd)||(R(a,J.m.Xd,h.wbraid),R(a,J.m.Qe,h.gbraid));R(a,J.m.Wa,Au());R(a,J.m.Aa,av());if(E(27)&&uc){var m=Kk(Qk(uc),"host");m&&R(a,J.m.Zk,m)}if(!P(a,O.A.se)){var n=Yu(),p=n.km;R(a,J.m.Oe,n.Zf);R(a,J.m.Pe,p)}R(a,J.m.Lc,Fl(!0));var q=nv();mv(q)&&R(a,J.m.md,"1");R(a,J.m.uk,jv());Ts(!1)._up==="1"&&R(a,J.m.Pk,"1")}Rn=!0;R(a,J.m.Eb);R(a,J.m.Qb);var r=ip([J.m.U,
J.m.V]);r&&(R(a,J.m.Eb,Nv()),c&&(gt(b),R(a,J.m.Qb,et[ht(b.prefix)])));R(a,J.m.oc);R(a,J.m.mb);if(!Mv(a,J.m.dd)&&!Mv(a,J.m.hd)&&Hv(d)){var t=bu(b);t.length>0&&R(a,J.m.oc,t.join("."))}else if(!Mv(a,J.m.Xd)&&r){var v=$t(d+"_aw");v.length>0&&R(a,J.m.mb,v.join("."))}E(31)&&R(a,J.m.Sk,Yc());a.D.isGtmEvent&&(a.D.C[J.m.Ea]=Dq.C[J.m.Ea]);tr(a.D)?R(a,J.m.zc,!1):R(a,J.m.zc,!0);Q(a,O.A.ug,!0);var u=Jv();u!==void 0&&R(a,J.m.Df,u||"error");var w=mr();w&&R(a,J.m.ld,w);if(E(137))try{var y=Intl.DateTimeFormat().resolvedOptions().timeZone;
R(a,J.m.ni,y||"-")}catch(F){R(a,J.m.ni,"e")}var z=lr();z&&R(a,J.m.pd,z);var C=wv.gppString;C&&R(a,J.m.jf,C);var D=wv.C;D&&R(a,J.m.hf,D);Q(a,O.A.Ha,!1)}}else a.isAborted=!0},Kv=function(a){var b={prefix:N(a.D,J.m.Sb)||N(a.D,J.m.eb),domain:N(a.D,J.m.ob),Dc:N(a.D,J.m.pb),flags:N(a.D,J.m.zb)};a.D.isGtmEvent&&(b.path=N(a.D,J.m.Tb));return b},Pv=function(a,b){var c,d,e,f,g,h,m,n;c=a.ye;d=a.Ee;e=a.He;f=a.Ma;g=a.D;h=a.Fe;m=a.Gr;n=a.Tm;Lv({ye:c,Ee:d,He:e,Tc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,dv(b,
f,g,h,n))},Qv=function(a,b){if(!P(a,O.A.se)){var c=uv(119);if(c){var d=rn(c),e=function(g){Q(a,O.A.se,!0);var h=Mv(a,J.m.Oe),m=Mv(a,J.m.Pe);R(a,J.m.Oe,String(g.gadSource));R(a,J.m.Pe,6);Q(a,O.A.ja);Q(a,O.A.Rf);R(a,J.m.ja);b();R(a,J.m.Oe,h);R(a,J.m.Pe,m);Q(a,O.A.se,!1)};if(d)e(d);else{var f=void 0;f=tn(c,function(g,h){e(h);un(c,f)})}}}},Lv=function(a){var b,c,d,e;b=a.ye;c=a.Ee;d=a.He;e=a.Tc;b&&(ct(c[J.m.de],!!c[J.m.ma])&&(Eu(Rv,e),Gu(e),pt(e)),Fl()!==2?(yu(e),rv(e),uv(200,e)):wu(e),Ku(Rv,e),Lu(e));
c[J.m.ma]&&(Iu(Rv,c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e.prefix),Ju(c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e.prefix),qt(ht(e.prefix),c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e),qt("FPAU",c[J.m.ma],c[J.m.Oc],!!c[J.m.vc],e));d&&(E(101)?Nu(Sv):Nu(Tv));Pu(Tv)},Uv=function(a,b,c,d){var e,f,g;e=a.Um;f=a.callback;g=a.tm;if(typeof f==="function")if(e===J.m.mb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===J.m.Qb?(L(65),gt(b,!1),f(et[ht(b.prefix)])):f(g)},Vv=function(a,b){Array.isArray(b)||
(b=[b]);var c=P(a,O.A.fa);return b.indexOf(c)>=0},Rv=["aw","dc","gb"],Tv=["aw","dc","gb","ag"],Sv=["aw","dc","gb","ag","gad_source"];function Wv(a){var b=N(a.D,J.m.Nc),c=N(a.D,J.m.Mc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Td&&L(131),a.isAborted=!0):!b&&c&&(L(132),a.isAborted=!0)}function Xv(a){var b=ip(J.m.U)?sp.pscdl:"denied";b!=null&&R(a,J.m.Jg,b)}function Yv(a){var b=Fl(!0);R(a,J.m.Lc,b)}function Zv(a){Er()&&R(a,J.m.be,1)}
function Nv(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Jk(a.substring(0,b))===void 0;)b--;return Jk(a.substring(0,b))||""}function $v(a){aw(a,Ap.Ef.dn,N(a.D,J.m.pb))}function aw(a,b,c){Mv(a,J.m.ud)||R(a,J.m.ud,{});Mv(a,J.m.ud)[b]=c}function bw(a){Q(a,O.A.Qf,Mm.X.Da)}function cw(a){var b=gb("GTAG_EVENT_FEATURE_CHANNEL");b&&(R(a,J.m.kf,b),eb())}function dw(a){var b=a.D.getMergedValues(J.m.uc);b&&a.mergeHitDataForKey(J.m.uc,b)}
function ew(a,b){b=b===void 0?!1:b;if(E(108)){var c=P(a,O.A.Pf);if(c)if(c.indexOf(a.target.destinationId)<0){if(Q(a,O.A.Tj,!1),b||!fw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else Q(a,O.A.Tj,!0)}}function gw(a){fl&&(Rn=!0,a.eventName===J.m.qa?Xn(a.D,a.target.id):(P(a,O.A.Le)||(Un[a.target.id]=!0),zp(P(a,O.A.ib))))};function qw(a,b,c,d){var e=Ec(),f;if(e===1)a:{var g=ck;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function Cw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Mv(a,b)},setHitData:function(b,c){R(a,b,c)},setHitDataIfNotDefined:function(b,c){Mv(a,b)===void 0&&R(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return P(a,b)},setMetadata:function(b,c){Q(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return N(a.D,b)},Bb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return jd(c)?a.mergeHitDataForKey(b,c):!1}}};var Ew=function(a){var b=Dw[a.target.destinationId];if(!a.isAborted&&b)for(var c=Cw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Fw=function(a,b){var c=Dw[a];c||(c=Dw[a]=[]);c.push(b)},Dw={};function Hw(a,b){return arguments.length===1?Iw("set",a):Iw("set",a,b)}function Jw(a,b){return arguments.length===1?Iw("config",a):Iw("config",a,b)}function Kw(a,b,c){c=c||{};c[J.m.nd]=a;return Iw("event",b,c)}function Iw(){return arguments};var Mw=function(){this.messages=[];this.C=[]};Mw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Mw.prototype.listen=function(a){this.C.push(a)};
Mw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Mw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Nw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[O.A.ib]=ig.canonicalContainerId;Ow().enqueue(a,b,c)}
function Pw(){var a=Qw;Ow().listen(a)}function Ow(){return tp("mb",function(){return new Mw})};var Rw,Sw=!1;function Tw(){Sw=!0;Rw=Rw||{}}function Uw(a){Sw||Tw();return Rw[a]};function Vw(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Ww(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var Yw=function(a){var b=Xw(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},Xw=function(){var a=A.body,b=A.documentElement||a&&a.parentElement,c,d;if(A.compatMode&&A.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var ax=function(a){if(Zw){if(a>=0&&a<$w.length&&$w[a]){var b;(b=$w[a])==null||b.disconnect();$w[a]=void 0}}else x.clearInterval(a)},dx=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(Zw){var e=!1;Kc(function(){e||bx(a,b,c)()});return cx(function(f){e=!0;for(var g={fg:0};g.fg<f.length;g={fg:g.fg},g.fg++)Kc(function(h){return function(){a(f[h.fg])}}(g))},
b,c)}return x.setInterval(bx(a,b,c),1E3)},bx=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:zb()};Kc(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=Yw(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),
f[h]++;else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},cx=function(a,b,c){for(var d=new x.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<$w.length;f++)if(!$w[f])return $w[f]=d,f;return $w.push(d)-1},$w=[],Zw=!(!x.IntersectionObserver||!x.IntersectionObserverEntry);
var fx=function(a){return a.tagName+":"+a.isVisible+":"+a.la.length+":"+ex.test(a.la)},tx=function(a){a=a||{Ce:!0,De:!0,Jh:void 0};a.Zb=a.Zb||{email:!0,phone:!1,address:!1};var b=gx(a),c=hx[b];if(c&&zb()-c.timestamp<200)return c.result;var d=ix(),e=d.status,f=[],g,h,m=[];if(!E(33)){if(a.Zb&&a.Zb.email){var n=jx(d.elements);f=kx(n,a&&a.Vf);g=lx(f);n.length>10&&(e="3")}!a.Jh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(mx(f[p],!!a.Ce,!!a.De));m=m.slice(0,10)}else if(a.Zb){}g&&(h=mx(g,!!a.Ce,!!a.De));var F={elements:m,
Gj:h,status:e};hx[b]={timestamp:zb(),result:F};return F},ux=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},wx=function(a){var b=vx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},vx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},sx=function(a,b,c){var d=a.element,e={la:a.la,type:a.xa,tagName:d.tagName};b&&(e.querySelector=xx(d));c&&(e.isVisible=!Ww(d));return e},mx=function(a,b,c){return sx({element:a.element,la:a.la,xa:rx.kc},b,c)},gx=function(a){var b=!(a==null||!a.Ce)+"."+!(a==null||!a.De);a&&a.Vf&&a.Vf.length&&(b+="."+a.Vf.join("."));a&&a.Zb&&(b+="."+a.Zb.email+"."+a.Zb.phone+"."+a.Zb.address);return b},lx=function(a){if(a.length!==0){var b;b=yx(a,function(c){return!zx.test(c.la)});b=yx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=yx(b,function(c){return!Ww(c.element)});return b[0]}},kx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&si(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},yx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},xx=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=xx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},jx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(Ax);if(f){var g=f[0],h;if(x.location){var m=Mk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,la:g})}}}return b},ix=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Bx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Cx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||E(33)&&Dx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Ax=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,ex=/@(gmail|googlemail)\./i,zx=/support|noreply/i,Bx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Cx=
["BR"],Ex=tg('',2),rx={kc:"1",zd:"2",sd:"3",yd:"4",Ke:"5",Nf:"6",kh:"7",Ri:"8",Mh:"9",Mi:"10"},hx={},Dx=["INPUT","SELECT"],Fx=vx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var dy=function(a,b,c){var d={};a.mergeHitDataForKey(J.m.Oi,(d[b]=c,d))},ey=function(a,b){var c=fw(a,J.m.Og,a.D.H[J.m.Og]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},fy=function(a){var b=P(a,O.A.jb);if(jd(b))return b},gy=function(a){if(P(a,O.A.xd)||!Yk(a.D))return!1;if(!N(a.D,J.m.od)){var b=N(a.D,J.m.Zd);return b===!0||b==="true"}return!0},hy=function(a){return fw(a,J.m.ce,N(a.D,J.m.ce))||!!fw(a,"google_ng",!1)};var iy=Number('')||5,jy=Number('')||50,ky=pb();
var my=function(a,b){a&&(ly("sid",a.targetId,b),ly("cc",a.clientCount,b),ly("tl",a.totalLifeMs,b),ly("hc",a.heartbeatCount,b),ly("cl",a.clientLifeMs,b))},ly=function(a,b,c){b!=null&&c.push(a+"="+b)},ny=function(){var a=A.referrer;if(a){var b;return Kk(Qk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},oy="https://"+Oi(21,"www.googletagmanager.com")+"/a?",qy=function(){this.R=py;this.N=0};qy.prototype.H=function(a,b,c,d){var e=ny(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&ly("si",a.hg,g);ly("m",0,g);ly("iss",f,g);ly("if",c,g);my(b,g);d&&ly("fm",encodeURIComponent(d.substring(0,jy)),g);this.P(g);};qy.prototype.C=function(a,b,c,d,e){var f=[];ly("m",1,f);ly("s",a,f);ly("po",ny(),f);b&&(ly("st",b.state,f),ly("si",b.hg,f),ly("sm",b.qg,f));my(c,f);ly("c",d,f);e&&ly("fm",encodeURIComponent(e.substring(0,
jy)),f);this.P(f);};qy.prototype.P=function(a){a=a===void 0?[]:a;!el||this.N>=iy||(ly("pid",ky,a),ly("bc",++this.N,a),a.unshift("ctid="+ig.ctid+"&t=s"),this.R(""+oy+a.join("&")))};var ry=Number('')||500,sy=Number('')||5E3,ty=Number('20')||10,uy=Number('')||5E3;function vy(a){return a.performance&&a.performance.now()||Date.now()}
var wy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{xm:function(){},ym:function(){},wm:function(){},onFailure:function(){}}:h;this.Go=f;this.C=g;this.N=h;this.ba=this.ka=this.heartbeatCount=this.Eo=0;this.mh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.hg=vy(this.C);this.qg=vy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Ba()};e.prototype.getState=function(){return{state:this.state,
hg:Math.round(vy(this.C)-this.hg),qg:Math.round(vy(this.C)-this.qg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.qg=vy(this.C))};e.prototype.Sl=function(){return String(this.Eo++)};e.prototype.Ba=function(){var f=this;this.heartbeatCount++;this.Sa({type:0,clientId:this.id,requestId:this.Sl(),maxDelay:this.nh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ba++,g.isDead||f.ba>ty){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.Do();var n,p;(p=(n=f.N).wm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Wl();else{if(f.heartbeatCount>g.stats.heartbeatCount+ty){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.mh){var v,u;(u=(v=f.N).ym)==null||u.call(v)}else{f.mh=!0;var w,y;(y=(w=f.N).xm)==null||y.call(w)}f.ba=0;f.Ho();f.Wl()}}})};e.prototype.nh=function(){return this.state===2?
sy:ry};e.prototype.Wl=function(){var f=this;this.C.setTimeout(function(){f.Ba()},Math.max(0,this.nh()-(vy(this.C)-this.ka)))};e.prototype.Ko=function(f,g,h){var m=this;this.Sa({type:1,clientId:this.id,requestId:this.Sl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},v,u;(u=(v=m.N).onFailure)==null||u.call(v,t);h(t)}})};e.prototype.Sa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Lf(t,7)},(p=f.maxDelay)!=null?p:uy),r={request:f,Km:g,Em:m,aq:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ka=vy(this.C);f.Em=!1;this.Go(f.request)};e.prototype.Ho=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.Em&&this.sendRequest(h)}};e.prototype.Do=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Lf(this.H[g.value],this.R)};e.prototype.Lf=function(f,g){this.rb(f);var h=f.request;h.failure={failureType:g};f.Km(h)};e.prototype.rb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.aq)};e.prototype.Hp=function(f){this.ka=vy(this.C);var g=this.H[f.requestId];if(g)this.rb(g),g.Km(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var xy;
var yy=function(){xy||(xy=new qy);return xy},py=function(a){kn(mn(Mm.X.Qc),function(){Hc(a)})},zy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Ay=function(a){var b=a,c=Lj.Ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},By=function(a){var b=rn(nn.Z.Ll);return b&&b[a]},Cy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.Yo(a);x.setTimeout(function(){f.initialize()},1E3);Kc(function(){f.Rp(a,b,e)})};k=Cy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),hg:this.initTime,qg:Math.round(zb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Ko(a,b,c)};k.getState=function(){return this.N.getState().state};k.Rp=function(a,b,c){var d=x.location.origin,e=this,
f=Fc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?zy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Fc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Hp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Yo=function(a){var b=this,c=wy(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{xm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},ym:function(){},wm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Dy(){var a=hg(eg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Ey(a,b){var c=Math.round(zb());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Dy()||E(168))return;kk()&&(a=""+d+jk()+"/_/service_worker");var e=Ay(a);if(e===null||By(e.origin))return;if(!sc()){yy().H(void 0,void 0,6);return}var f=new Cy(e,!!a,c||Math.round(zb()),yy(),b);sn(nn.Z.Ll)[e.origin]=f;}
var Fy=function(a,b,c,d){var e;if((e=By(a))==null||!e.delegate){var f=sc()?16:6;yy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}By(a).delegate(b,c,d);};
function Gy(a,b,c,d,e){var f=Ay();if(f===null){d(sc()?16:6);return}var g,h=(g=By(f.origin))==null?void 0:g.initTime,m=Math.round(zb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Fy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Hy(a,b,c,d){var e=Ay(a);if(e===null){d("_is_sw=f"+(sc()?16:6)+"te");return}var f=b?1:0,g=Math.round(zb()),h,m=(h=By(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);Fy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,v=(t=By(e.origin))==
null?void 0:t.getState();v!==void 0&&(r+="s"+v);d(n?r+("t"+n):r+"te")});};function Iy(a){if(E(10)||kk()||Lj.N||Yk(a.D)||E(168))return;Ey(void 0,E(131));};var Jy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Ky(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Ly(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=Object.assign({},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function My(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function Ny(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Oy(a){if(!Ny(a))return null;var b=Ky(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Jy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Qy=function(a,b){if(a)for(var c=Py(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;R(b,f,c[f])}},Py=function(a){var b={};b[J.m.vf]=a.architecture;b[J.m.wf]=a.bitness;a.fullVersionList&&(b[J.m.xf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[J.m.yf]=a.mobile?"1":"0";b[J.m.zf]=a.model;b[J.m.Af]=a.platform;b[J.m.Bf]=a.platformVersion;b[J.m.Cf]=a.wow64?"1":"0";return b},Ry=function(a){var b=0,c=function(h,
m){try{a(h,m)}catch(n){}},d=x,e=Ly(d);if(e)c(e);else{var f=My(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var g=d.setTimeout(function(){c.ig||(c.ig=!0,L(106),c(null,Error("Timeout")))},b);f.then(function(h){c.ig||(c.ig=!0,L(104),d.clearTimeout(g),c(h))}).catch(function(h){c.ig||(c.ig=!0,L(105),d.clearTimeout(g),c(null,h))})}else c(null)}},Ty=function(){var a=x;if(Ny(a)&&(Sy=zb(),!My(a))){var b=Oy(a);b&&(b.then(function(){L(95)}),b.catch(function(){L(96)}))}},Sy;function Uy(a){var b=a.location.href;if(a===a.top)return{url:b,Wp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Wp:c}};var Iz=function(){return E(90)?ko():""},Jz=function(){var a;E(90)&&ko()!==""&&(a=ko());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},Kz=function(){var a="www";E(90)&&ko()&&(a=ko());return"https://"+a+".google-analytics.com/g/collect"};function Lz(a,b){var c=!!kk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?jk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(187)?Iz()?Jz():""+jk()+"/ag/g/c":Iz().toLowerCase()==="region1"?""+jk()+"/r1ag/g/c":""+jk()+"/ag/g/c":Jz();case 16:if(c){if(E(187))return Iz()?Kz():
""+jk()+"/ga/g/c";var d=Iz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+jk()+d}return Kz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?jk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?jk()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Lo+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?jk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return(E(207)?c:c&&b.Eh)?jk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?jk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return(E(207)?c:c&&b.Eh)?jk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";
case 55:return c?jk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return E(205)?"https://www.google.com/measurement/conversion/":c?jk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return(E(207)?c:c&&b.Eh)?jk()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:jc(a,"Unknown endpoint")}};function Mz(a){a=a===void 0?[]:a;return Mj(a).join("~")}function Nz(){if(!E(118))return"";var a,b;return(((a=Am(pm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Oz(a,b){b&&sb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var Qz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Mv(a,g),m=Pz[g];m&&h!==void 0&&h!==""&&(!P(a,O.A.te)||g!==J.m.dd&&g!==J.m.hd&&g!==J.m.Xd&&g!==J.m.Qe||(h="0"),d(m,h))}d("gtm",Kr({Ma:P(a,O.A.ib)}));ur()&&d("gcs",vr());d("gcd",zr(a.D));Cr()&&d("dma_cps",Ar());d("dma",Br());Yq(fr())&&d("tcfd",Dr());Mz()&&d("tag_exp",Mz());Nz()&&d("ptag_exp",Nz());if(P(a,O.A.ug)){d("tft",
zb());var n=Xc();n!==void 0&&d("tfd",Math.round(n))}E(24)&&d("apve","1");(E(25)||E(26))&&d("apvf",Uc()?E(26)?"f":"sb":"nf");dn[Mm.X.Da]!==Lm.Ia.pe||gn[Mm.X.Da].isConsentGranted()||(c.limited_ads="1");b(c)},Rz=function(a,b,c){var d=b.D;Vo({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Ya:{eventId:d.eventId,priorityId:d.priorityId},wh:{eventId:P(b,O.A.Ie),priorityId:P(b,O.A.Je)}})},Sz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,
priorityId:b.D.priorityId};Rz(a,b,c);fm(d,a,void 0,{Hh:!0,method:"GET"},function(){},function(){em(d,a+"&img=1")})},Tz=function(a){var b=zc()||xc()?"www.google.com":"www.googleadservices.com",c=[];sb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Uz=function(a){Qz(a,function(b){if(P(a,O.A.fa)===K.J.Ga){var c=[];E(28)&&a.target.destinationId&&c.push("tid="+a.target.destinationId);
sb(b,function(r,t){c.push(r+"="+t)});var d=ip([J.m.U,J.m.V])?45:46,e=Lz(d)+"?"+c.join("&");Rz(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(E(26)&&Uc()){fm(g,e,void 0,{Hh:!0},function(){},function(){em(g,e+"&img=1")});var h=ip([J.m.U,J.m.V]),m=Mv(a,J.m.md)==="1",n=Mv(a,J.m.Vh)==="1";if(h&&m&&!n){var p=Tz(b),q=zc()||xc()?58:57;Sz(p,a,q)}}else dm(g,e)||em(g,e+"&img=1");if(jb(a.D.onSuccess))a.D.onSuccess()}})},Vz={},Pz=(Vz[J.m.ja]="gcu",
Vz[J.m.oc]="gclgb",Vz[J.m.mb]="gclaw",Vz[J.m.Oe]="gad_source",Vz[J.m.Pe]="gad_source_src",Vz[J.m.dd]="gclid",Vz[J.m.sk]="gclsrc",Vz[J.m.Qe]="gbraid",Vz[J.m.Xd]="wbraid",Vz[J.m.Qb]="auid",Vz[J.m.uk]="rnd",Vz[J.m.Vh]="ncl",Vz[J.m.Zh]="gcldc",Vz[J.m.hd]="dclid",Vz[J.m.Ub]="edid",Vz[J.m.kd]="en",Vz[J.m.ld]="gdpr",Vz[J.m.Vb]="gdid",Vz[J.m.be]="_ng",Vz[J.m.hf]="gpp_sid",Vz[J.m.jf]="gpp",Vz[J.m.kf]="_tu",Vz[J.m.Pk]="gtm_up",Vz[J.m.Lc]="frm",Vz[J.m.md]="lps",Vz[J.m.Ug]="did",Vz[J.m.Sk]="navt",Vz[J.m.Aa]=
"dl",Vz[J.m.Wa]="dr",Vz[J.m.Eb]="dt",Vz[J.m.Zk]="scrsrc",Vz[J.m.tf]="ga_uid",Vz[J.m.pd]="gdpr_consent",Vz[J.m.ni]="u_tz",Vz[J.m.Qa]="uid",Vz[J.m.Df]="us_privacy",Vz[J.m.zc]="npa",Vz);var Wz={};Wz.O=as.O;var Xz={ir:"L",Bo:"S",zr:"Y",Nq:"B",Xq:"E",er:"I",wr:"TC",ar:"HTC"},Yz={Bo:"S",Wq:"V",Qq:"E",vr:"tag"},Zz={},$z=(Zz[Wz.O.Ti]="6",Zz[Wz.O.Ui]="5",Zz[Wz.O.Si]="7",Zz);function aA(){function a(c,d){var e=gb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var bA=!1;function sA(a){}
function tA(a){}function uA(){}
function vA(a){}function wA(a){}
function xA(a){}
function yA(){}function zA(a,b){}
function AA(a,b,c){}
function BA(){};var CA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function DA(a,b,c,d,e,f,g){var h=Object.assign({},CA);c&&(h.body=c,h.method="POST");Object.assign(h,e);x.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var v;v=t.done;var u=p.decode(t.value,{stream:!v});EA(d,u);v?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():E(128)&&(b+="&_z=retryFetch",c?dm(a,b,c):cm(a,b))})};var FA=function(a){this.P=a;this.C=""},GA=function(a,b){a.H=b;return a},HA=function(a,b){a.N=b;return a},EA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}IA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},JA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};IA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},IA=function(a,b){b&&(KA(b.send_pixel,b.options,a.P),KA(b.create_iframe,b.options,a.H),KA(b.fetch,b.options,a.N))};function LA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function KA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=jd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var AB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),BB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},CB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},DB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function EB(){var a=qk("gtm.allowlist")||qk("gtm.whitelist");a&&L(9);Yj&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);AB.test(x.location&&x.location.hostname)&&(Yj?L(116):(L(117),FB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Db(wb(a),BB),c=qk("gtm.blocklist")||qk("gtm.blacklist");c||(c=qk("tagTypeBlacklist"))&&L(3);c?L(8):c=[];AB.test(x.location&&x.location.hostname)&&(c=wb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
wb(c).indexOf("google")>=0&&L(2);var d=c&&Db(wb(c),CB),e={};return function(f){var g=f&&f[ef.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=gk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Yj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){L(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=qb(d,h||[]);t&&L(10);q=t}}var v=!m||
q;!v&&(h.indexOf("sandboxedScripts")===-1?0:Yj&&h.indexOf("cmpPartners")>=0?!GB():b&&b.indexOf("sandboxedScripts")!==-1?0:qb(d,DB))&&(v=!0);return e[g]=v}}function GB(){var a=hg(eg.C,ig.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var FB=!1;FB=!0;function HB(a,b,c,d,e){if(!IB()&&!Fm(a)){d.loadExperiments=Nj();om(a,d,e);var f=JB(a),g=function(){qm().container[a]&&(qm().container[a].state=3);KB()},h={destinationId:a,endpoint:0};if(kk())gm(h,jk()+"/"+f,void 0,g);else{var m=Eb(a,"GTM-"),n=Xk(),p=c?"/gtag/js":"/gtm.js",q=Wk(b,p+f);if(!q){var r=Pj.Ag+p;n&&uc&&m&&(r=uc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=qw("https://","http://",r+f)}gm(h,q,void 0,g)}}}
function KB(){Hm()||sb(Im(),function(a,b){LB(a,b.transportUrl,b.context);L(92)})}
function LB(a,b,c,d){if(!IB()&&!Gm(a))if(c.loadExperiments||(c.loadExperiments=Nj()),Hm()){var e;(e=qm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:pm()});qm().destination[a].state=0;rm({ctid:a,isDestination:!0},d);L(91)}else{var f;(f=qm().destination)[a]!=null||(f[a]={context:c,state:1,parent:pm()});qm().destination[a].state=1;rm({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(kk())gm(g,jk()+("/gtd"+JB(a,!0)));else{var h="/gtag/destination"+JB(a,!0),
m=Wk(b,h);m||(m=qw("https://","http://",Pj.Ag+h));gm(g,m)}}}function JB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Sj!=="dataLayer"&&(c+="&l="+Sj);if(!Eb(a,"GTM-")||b)c=E(130)?c+(kk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Lr();Xk()&&(c+="&sign="+Pj.Pi);var d=Lj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Nj().join("~")&&(c+="&tag_exp="+Nj().join("~"));return c}
function IB(){if(Fr()){return!0}return!1};var MB=function(){this.H=0;this.C={}};MB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,hc:c};return d};MB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var OB=function(a,b){var c=[];sb(NB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.hc===void 0||b.indexOf(e.hc)>=0)&&c.push(e.listener)});return c};function PB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:ig.ctid}};function QB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var SB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;RB(this,a,b)},TB=function(a,b,c,d){if(Uj.hasOwnProperty(b)||b==="__zone")return-1;var e={};jd(d)&&(e=kd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},UB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},VB=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},RB=function(a,b,c){b!==void 0&&a.Sf(b);c&&x.setTimeout(function(){VB(a)},
Number(c))};SB.prototype.Sf=function(a){var b=this,c=Bb(function(){Kc(function(){a(ig.ctid,b.eventData)})});this.C?c():this.P.push(c)};var WB=function(a){a.N++;return Bb(function(){a.H++;a.R&&a.H>=a.N&&VB(a)})},XB=function(a){a.R=!0;a.H>=a.N&&VB(a)};var YB={};function ZB(){return x[$B()]}
function $B(){return x.GoogleAnalyticsObject||"ga"}function cC(){var a=ig.ctid;}
function dC(a,b){return function(){var c=ZB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var jC=["es","1"],kC={},lC={};function mC(a,b){if(el){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";kC[a]=[["e",c],["eid",a]];vq(a)}}function nC(a){var b=a.eventId,c=a.Nd;if(!kC[b])return[];var d=[];lC[b]||d.push(jC);d.push.apply(d,ta(kC[b]));c&&(lC[b]=!0);return d};var oC={},pC={},qC={};function rC(a,b,c,d){el&&E(120)&&((d===void 0?0:d)?(qC[b]=qC[b]||0,++qC[b]):c!==void 0?(pC[a]=pC[a]||{},pC[a][b]=Math.round(c)):(oC[a]=oC[a]||{},oC[a][b]=(oC[a][b]||0)+1))}function sC(a){var b=a.eventId,c=a.Nd,d=oC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete oC[b];return e.length?[["md",e.join(".")]]:[]}
function tC(a){var b=a.eventId,c=a.Nd,d=pC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete pC[b];return e.length?[["mtd",e.join(".")]]:[]}function uC(){for(var a=[],b=l(Object.keys(qC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+qC[d])}return a.length?[["mec",a.join(".")]]:[]};var vC={},wC={};function xC(a,b,c){if(el&&b){var d=al(b);vC[a]=vC[a]||[];vC[a].push(c+d);var e=b[ef.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Hf[e]?"1":"2")+d;wC[a]=wC[a]||[];wC[a].push(f);vq(a)}}function yC(a){var b=a.eventId,c=a.Nd,d=[],e=vC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=wC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete vC[b],delete wC[b]);return d};function zC(a,b,c){c=c===void 0?!1:c;AC().addRestriction(0,a,b,c)}function BC(a,b,c){c=c===void 0?!1:c;AC().addRestriction(1,a,b,c)}function CC(){var a=xm();return AC().getRestrictions(1,a)}var DC=function(){this.container={};this.C={}},EC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
DC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=EC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
DC.prototype.getRestrictions=function(a,b){var c=EC(this,b);if(a===0){var d,e;return[].concat(ta((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ta((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ta((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ta((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
DC.prototype.getExternalRestrictions=function(a,b){var c=EC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};DC.prototype.removeExternalRestrictions=function(a){var b=EC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function AC(){return tp("r",function(){return new DC})};function FC(a,b,c,d){var e=Ff[a],f=GC(a,b,c,d);if(!f)return null;var g=Uf(e[ef.Ml],c,[]);if(g&&g.length){var h=g[0];f=FC(h.index,{onSuccess:f,onFailure:h.jm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function GC(a,b,c,d){function e(){function w(){Yn(3);var M=zb()-H;xC(c.id,f,"7");UB(c.Rc,D,"exception",M);E(109)&&AA(c,f,Wz.O.Si);F||(F=!0,h())}if(f[ef.so])h();else{var y=Tf(f,c,[]),z=y[ef.Ym];if(z!=null)for(var C=0;C<z.length;C++)if(!ip(z[C])){h();return}var D=TB(c.Rc,String(f[ef.Ra]),Number(f[ef.qh]),y[ef.METADATA]),F=!1;y.vtp_gtmOnSuccess=function(){if(!F){F=!0;var M=zb()-H;xC(c.id,Ff[a],"5");UB(c.Rc,D,"success",M);E(109)&&AA(c,f,Wz.O.Ui);g()}};y.vtp_gtmOnFailure=function(){if(!F){F=!0;var M=zb()-
H;xC(c.id,Ff[a],"6");UB(c.Rc,D,"failure",M);E(109)&&AA(c,f,Wz.O.Ti);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);xC(c.id,f,"1");E(109)&&zA(c,f);var H=zb();try{Vf(y,{event:c,index:a,type:1})}catch(M){w(M)}E(109)&&AA(c,f,Wz.O.Tl)}}var f=Ff[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Uf(f[ef.Ul],c,[]);if(n&&n.length){var p=n[0],q=FC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.jm===
2?m:q}if(f[ef.Dl]||f[ef.vo]){var r=f[ef.Dl]?Gf:c.Fq,t=g,v=h;if(!r[a]){var u=HC(a,r,Bb(e));g=u.onSuccess;h=u.onFailure}return function(){r[a](t,v)}}return e}function HC(a,b,c){var d=[],e=[];b[a]=IC(d,e,c);return{onSuccess:function(){b[a]=JC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=KC;for(var f=0;f<e.length;f++)e[f]()}}}function IC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function JC(a){a()}function KC(a,b){b()};var NC=function(a,b){for(var c=[],d=0;d<Ff.length;d++)if(a[d]){var e=Ff[d];var f=WB(b.Rc);try{var g=FC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[ef.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Hf[h];c.push({Qm:d,priorityOverride:(m?m.priorityOverride||0:0)||QB(e[ef.Ra],1)||0,execute:g})}else LC(d,b),f()}catch(p){f()}}c.sort(MC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function OC(a,b){if(!NB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=OB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=WB(b);try{d[e](a,f)}catch(g){f()}}return!0}function MC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Qm,h=b.Qm;f=g>h?1:g<h?-1:0}return f}
function LC(a,b){if(el){var c=function(d){var e=b.isBlocked(Ff[d])?"3":"4",f=Uf(Ff[d][ef.Ml],b,[]);f&&f.length&&c(f[0].index);xC(b.id,Ff[d],e);var g=Uf(Ff[d][ef.Ul],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var PC=!1,NB;function QC(){NB||(NB=new MB);return NB}
function RC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(PC)return!1;PC=!0}var e=!1,f=CC(),g=kd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}mC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:SC(g,e),Fq:[],logMacroError:function(){L(6);Yn(0)},cachedModelValues:TC(),Rc:new SB(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&el&&(n.reportMacroDiscrepancy=rC);E(109)&&wA(n.id);var p=$f(n);E(109)&&xA(n.id);e&&(p=UC(p));E(109)&&vA(b);var q=NC(p,n),r=OC(a,n.Rc);XB(n.Rc);d!=="gtm.js"&&d!=="gtm.sync"||cC();return VC(p,q)||r}function TC(){var a={};a.event=vk("event",1);a.ecommerce=vk("ecommerce",1);a.gtm=vk("gtm");a.eventModel=vk("eventModel");return a}
function SC(a,b){var c=EB();return function(d){if(c(d))return!0;var e=d&&d[ef.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=xm();f=AC().getRestrictions(0,g);var h=a;b&&(h=kd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=gk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function UC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Ff[c][ef.Ra]);if(Tj[d]||Ff[c][ef.wo]!==void 0||QB(d,2))b[c]=!0}return b}function VC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Ff[c]&&!Uj[String(Ff[c][ef.Ra])])return!0;return!1};function WC(){QC().addListener("gtm.init",function(a,b){Lj.ba=!0;In();b()})};var XC=!1,YC=0,ZC=[];function $C(a){if(!XC){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){XC=!0;for(var e=0;e<ZC.length;e++)Kc(ZC[e])}ZC.push=function(){for(var f=xa.apply(0,arguments),g=0;g<f.length;g++)Kc(f[g]);return 0}}}function aD(){if(!XC&&YC<140){YC++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");$C()}catch(c){x.setTimeout(aD,50)}}}
function bD(){var a=x;XC=!1;YC=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")$C();else{Ic(A,"DOMContentLoaded",$C);Ic(A,"readystatechange",$C);if(A.createEventObject&&A.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&aD()}Ic(a,"load",$C)}}function cD(a){XC?a():ZC.push(a)};var dD={},eD={};function fD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Fj:void 0,mj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Fj=Dp(g,b),e.Fj){var h=wm();ob(h,function(r){return function(t){return r.Fj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=dD[g]||[];e.mj={};m.forEach(function(r){return function(t){r.mj[t]=!0}}(e));for(var n=ym(),p=0;p<n.length;p++)if(e.mj[n[p]]){c=c.concat(wm());break}var q=eD[g]||[];q.length&&(c=c.concat(q))}}return{zj:c,cq:d}}
function gD(a){sb(dD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function hD(a){sb(eD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var iD=!1,jD=!1;function kD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=kd(b,null),b[J.m.ef]&&(d.eventCallback=b[J.m.ef]),b[J.m.Pg]&&(d.eventTimeout=b[J.m.Pg]));return d}function lD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:wp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function mD(a,b){var c=a&&a[J.m.nd];c===void 0&&(c=qk(J.m.nd,2),c===void 0&&(c="default"));if(lb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?lb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=fD(d,b.isGtmEvent),f=e.zj,g=e.cq;if(g.length)for(var h=nD(a),m=0;m<g.length;m++){var n=Dp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=qm().destination[q];r&&r.state===0||LB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{zj:Ep(f,b.isGtmEvent),
Mo:Ep(t,b.isGtmEvent)}}}var oD=void 0,pD=void 0;function qD(a,b,c){var d=kd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&L(136);var e=kd(b,null);kd(c,e);Nw(Jw(ym()[0],e),a.eventId,d)}function nD(a){for(var b=l([J.m.od,J.m.xc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Dq.C[d];if(e)return e}}
var rD={config:function(a,b){var c=lD(a,b);if(!(a.length<2)&&lb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!jd(a[2])||a.length>3)return;d=a[2]}var e=Dp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!um.qe){var m=Am(pm());if(Jm(m)){var n=m.parent,p=n.isDestination;h={gq:Am(n),Yp:p};break a}}h=void 0}var q=h;q&&(f=q.gq,g=q.Yp);mC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?wm().indexOf(r)===-1:ym().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Nc]){var v=nD(d);if(t)LB(r,v,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var u=d;oD?qD(b,u,oD):pD||(pD=kd(u,null))}else HB(r,v,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(L(128),g&&L(130),b.inheritParentConfig)){var w;var y=d;pD?(qD(b,pD,y),w=!1):(!y[J.m.rd]&&Wj&&oD||(oD=kd(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}fl&&(yp===1&&(An.mcc=!1),yp=2);if(Wj&&!t&&!d[J.m.rd]){var z=jD;jD=!0;if(z)return}iD||L(43);if(!b.noTargetGroup)if(t){hD(e.id);
var C=e.id,D=d[J.m.Sg]||"default";D=String(D).split(",");for(var F=0;F<D.length;F++){var H=eD[D[F]]||[];eD[D[F]]=H;H.indexOf(C)<0&&H.push(C)}}else{gD(e.id);var M=e.id,S=d[J.m.Sg]||"default";S=S.toString().split(",");for(var ca=0;ca<S.length;ca++){var U=dD[S[ca]]||[];dD[S[ca]]=U;U.indexOf(M)<0&&U.push(M)}}delete d[J.m.Sg];var oa=b.eventMetadata||{};oa.hasOwnProperty(O.A.wd)||(oa[O.A.wd]=!b.fromContainerExecution);b.eventMetadata=oa;delete d[J.m.ef];for(var T=t?[e.id]:wm(),Z=0;Z<T.length;Z++){var Y=
d,V=T[Z],ka=kd(b,null),ia=Dp(V,ka.isGtmEvent);ia&&Dq.push("config",[Y],ia,ka)}}}}},consent:function(a,b){if(a.length===3){L(39);var c=lD(a,b),d=a[1],e={},f=Bo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.vg?Array.isArray(h)?NaN:Number(h):g===J.m.jc?(Array.isArray(h)?h:[h]).map(Co):Do(h)}b.fromContainerExecution||(e[J.m.V]&&L(139),e[J.m.La]&&L(140));d==="default"?ep(e):d==="update"?gp(e,c):d==="declare"&&b.fromContainerExecution&&dp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&lb(c)){var d=void 0;if(a.length>2){if(!jd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=kD(c,d),f=lD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=mD(d,b);if(m){var n=m.zj,p=m.Mo,q,r,t;if(E(108)){q=p.map(function(M){return M.id});r=p.map(function(M){return M.destinationId});t=n.map(function(M){return M.id});for(var v=l(wm()),u=v.next();!u.done;u=v.next()){var w=u.value;r.indexOf(w)<
0&&t.push(w)}}else q=n.map(function(M){return M.id}),r=n.map(function(M){return M.destinationId}),t=q;mC(g,c);for(var y=l(t),z=y.next();!z.done;z=y.next()){var C=z.value,D=kd(b,null),F=kd(d,null);delete F[J.m.ef];var H=D.eventMetadata||{};H.hasOwnProperty(O.A.wd)||(H[O.A.wd]=!D.fromContainerExecution);H[O.A.Ni]=q.slice();H[O.A.Pf]=r.slice();D.eventMetadata=H;Eq(c,F,C,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.nd]=q.join(","):delete e.eventModel[J.m.nd];iD||L(43);b.noGtmEvent===void 0&&
b.eventMetadata&&b.eventMetadata[O.A.Rl]&&(b.noGtmEvent=!0);e.eventModel[J.m.Mc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){L(53);if(a.length===4&&lb(a[1])&&lb(a[2])&&jb(a[3])){var c=Dp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){iD||L(43);var f=nD();if(ob(wm(),function(h){return c.destinationId===h})){lD(a,b);var g={};kd((g[J.m.sc]=d,g[J.m.Kc]=e,g),null);Fq(d,function(h){Kc(function(){e(h)})},c.id,b)}else LB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},
js:function(a,b){if(a.length===2&&a[1].getTime){iD=!0;var c=lD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&lb(a[1])&&jb(a[2])){if(fg(a[1],a[2]),L(74),a[1]==="all"){L(75);var b=!1;try{b=a[2](ig.ctid,"unknown",{})}catch(c){}b||L(76)}}else L(73)},set:function(a,b){var c=void 0;a.length===2&&jd(a[1])?c=kd(a[1],null):a.length===3&&lb(a[1])&&(c={},jd(a[2])||Array.isArray(a[2])?
c[a[1]]=kd(a[2],null):c[a[1]]=a[2]);if(c){var d=lD(a,b),e=d.eventId,f=d.priorityId;kd(c,null);var g=kd(c,null);Dq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},sD={policy:!0};var uD=function(a){if(tD(a))return a;this.value=a};uD.prototype.getUntrustedMessageValue=function(){return this.value};var tD=function(a){return!a||hd(a)!=="object"||jd(a)?!1:"getUntrustedMessageValue"in a};uD.prototype.getUntrustedMessageValue=uD.prototype.getUntrustedMessageValue;var vD=!1,wD=[];function xD(){if(!vD){vD=!0;for(var a=0;a<wD.length;a++)Kc(wD[a])}}function yD(a){vD?Kc(a):wD.push(a)};var zD=0,AD={},BD=[],CD=[],DD=!1,ED=!1;function FD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function GD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return HD(a)}function ID(a,b){if(!mb(b)||b<0)b=0;var c=sp[Sj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function JD(a,b){var c=a._clear||b.overwriteModelFields;sb(a,function(e,f){e!=="_clear"&&(c&&tk(e),tk(e,f))});dk||(dk=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=wp(),a["gtm.uniqueEventId"]=d,tk("gtm.uniqueEventId",d));return RC(a)}function KD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(tb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function LD(){var a;if(CD.length)a=CD.shift();else if(BD.length)a=BD.shift();else return;var b;var c=a;if(DD||!KD(c.message))b=c;else{DD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=wp(),f=wp(),c.message["gtm.uniqueEventId"]=wp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};BD.unshift(n,c);b=h}return b}
function MD(){for(var a=!1,b;!ED&&(b=LD());){ED=!0;delete nk.eventModel;pk();var c=b,d=c.message,e=c.messageContext;if(d==null)ED=!1;else{e.fromContainerExecution&&uk();try{if(jb(d))try{d.call(rk)}catch(v){}else if(Array.isArray(d)){if(lb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=qk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(v){}}}else{var n=void 0;if(tb(d))a:{if(d.length&&lb(d[0])){var p=rD[d[0]];if(p&&(!e.fromContainerExecution||!sD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=JD(n,e)||a)}}finally{e.fromContainerExecution&&pk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=AD[String(q)]||[],t=0;t<r.length;t++)CD.push(ND(r[t]));r.length&&CD.sort(FD);delete AD[String(q)];q>zD&&(zD=q)}ED=!1}}}return!a}
function OD(){if(E(109)){var a=!Lj.ka;}var c=MD();if(E(109)){}try{var e=ig.ctid,f=x[Sj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Qw(a){if(zD<a.notBeforeEventId){var b=String(a.notBeforeEventId);AD[b]=AD[b]||[];AD[b].push(a)}else CD.push(ND(a)),CD.sort(FD),Kc(function(){ED||MD()})}function ND(a){return{message:a.message,messageContext:a.messageContext}}
function PD(){function a(f){var g={};if(tD(f)){var h=f;f=tD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=vc(Sj,[]),c=sp[Sj]=sp[Sj]||{};c.pruned===!0&&L(83);AD=Ow().get();Pw();cD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});yD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(sp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new uD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});BD.push.apply(BD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(L(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return MD()&&p};var e=b.slice(0).map(function(f){return a(f)});BD.push.apply(BD,e);if(!Lj.ka){if(E(109)){}Kc(OD)}}var HD=function(a){return x[Sj].push(a)};function QD(a){HD(a)};function RD(){var a,b=Qk(x.location.href);(a=b.hostname+b.pathname)&&En("dl",encodeURIComponent(a));var c;var d=ig.ctid;if(d){var e=um.qe?1:0,f,g=Am(pm());f=g&&g.context;c=d+";"+ig.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&En("tdp",h);var m=Fl(!0);m!==void 0&&En("frm",String(m))};function SD(){(Oo()||fl)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){L(179);var b=bm(a.effectiveDirective);if(b){var c;var d=$l(b,a.blockedURI);c=d?Yl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(u){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Im){p.Im=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Oo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Oo()){var v=Uo("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});v.tagDiagnostics=t;No(v)}}}Kn(p.endpoint)}}am(b,a.blockedURI)}}}}})};function TD(){var a;var b=zm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&En("pcid",e)};var UD=/^(https?:)?\/\//;
function VD(){var a=Bm();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=Zc())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(UD,"")===d.replace(UD,""))){b=g;break a}}L(146)}else L(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&En("rtg",String(a.canonicalContainerId)),En("slo",String(p)),En("hlo",a.htmlLoadOrder||"-1"),
En("lst",String(a.loadScriptType||"0")))}else L(144)};function WD(){var a=[],b=Number('1')||0,c=function(){var f=!1;return f}();a.push({Pm:195,Om:195,experimentId:104527906,controlId:104527907,percent:b,active:c,dj:1});var d=Number('1')||0,e=function(){var f=!1;
return f}();a.push({Pm:196,Om:196,experimentId:104528500,controlId:104528501,percent:d,active:e,dj:0});return a};var XD={};function YD(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Lj.R.H.add(Number(c.value))}function ZD(){for(var a=l(WD()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Pm;ji[d]=c;if(c.dj===1){var e=d,f=sn(nn.Z.yo);mi(f,e);YD(f)}else if(c.dj===0){var g=XD;mi(g,d);YD(g)}}};

function tE(){};var uE=function(){};uE.prototype.toString=function(){return"undefined"};var vE=new uE;function CE(a,b){function c(g){var h=Qk(g),m=Kk(h,"protocol"),n=Kk(h,"host",!0),p=Kk(h,"port"),q=Kk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function DE(a){return EE(a)?1:0}
function EE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=kd(a,{});kd({arg1:c[d],any_of:void 0},e);if(DE(e))return!0}return!1}switch(a["function"]){case "_cn":return Og(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Jg.length;g++){var h=Jg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Kg(b,c);case "_eq":return Pg(b,c);case "_ge":return Qg(b,c);case "_gt":return Sg(b,c);case "_lc":return Lg(b,c);case "_le":return Rg(b,
c);case "_lt":return Tg(b,c);case "_re":return Ng(b,c,a.ignore_case);case "_sw":return Ug(b,c);case "_um":return CE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var FE=function(a,b,c,d){Uq.call(this);this.mh=b;this.Lf=c;this.rb=d;this.Sa=new Map;this.nh=0;this.ka=new Map;this.Ba=new Map;this.R=void 0;this.H=a};ra(FE,Uq);FE.prototype.N=function(){delete this.C;this.Sa.clear();this.ka.clear();this.Ba.clear();this.R&&(Qq(this.H,"message",this.R),delete this.R);delete this.H;delete this.rb;Uq.prototype.N.call(this)};
var GE=function(a){if(a.C)return a.C;a.Lf&&a.Lf(a.H)?a.C=a.H:a.C=El(a.H,a.mh);var b;return(b=a.C)!=null?b:null},IE=function(a,b,c){if(GE(a))if(a.C===a.H){var d=a.Sa.get(b);d&&d(a.C,c)}else{var e=a.ka.get(b);if(e&&e.yj){HE(a);var f=++a.nh;a.Ba.set(f,{Ih:e.Ih,bp:e.sm(c),persistent:b==="addEventListener"});a.C.postMessage(e.yj(c,f),"*")}}},HE=function(a){a.R||(a.R=function(b){try{var c;c=a.rb?a.rb(b):void 0;if(c){var d=c.jq,e=a.Ba.get(d);if(e){e.persistent||a.Ba.delete(d);var f;(f=e.Ih)==null||f.call(e,
e.bp,c.payload)}}}catch(g){}},Pq(a.H,"message",a.R))};var JE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},KE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},LE={sm:function(a){return a.listener},yj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Ih:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},ME={sm:function(a){return a.listener},yj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Ih:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function NE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,jq:b.__gppReturn.callId}}
var OE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Uq.call(this);this.caller=new FE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},NE);this.caller.Sa.set("addEventListener",JE);this.caller.ka.set("addEventListener",LE);this.caller.Sa.set("removeEventListener",KE);this.caller.ka.set("removeEventListener",ME);this.timeoutMs=c!=null?c:500};ra(OE,Uq);OE.prototype.N=function(){this.caller.dispose();Uq.prototype.N.call(this)};
OE.prototype.addEventListener=function(a){var b=this,c=hl(function(){a(PE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);IE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(QE,!0);return}a(RE,!0)}}})};
OE.prototype.removeEventListener=function(a){IE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var RE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},PE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},QE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function SE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){wv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");wv.C=d}}function TE(){try{var a=new OE(x,{timeoutMs:-1});GE(a.caller)&&a.addEventListener(SE)}catch(b){}};function UE(){var a=[["cv",Pi(1)],["rv",Qj],["tc",Ff.filter(function(b){return b}).length]];Rj&&a.push(["x",Rj]);ik()&&a.push(["tag_exp",ik()]);return a};var VE={};function Si(a){VE[a]=(VE[a]||0)+1}function WE(){for(var a=[],b=l(Object.keys(VE)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+VE[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var XE={},YE={};function ZE(a){var b=a.eventId,c=a.Nd,d=[],e=XE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=YE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete XE[b],delete YE[b]);return d};function $E(){return!1}function aF(){var a={};return function(b,c,d){}};function bF(){var a=cF;return function(b,c,d){var e=d&&d.event;dF(c);var f=zh(b)?void 0:1,g=new Ua;sb(c,function(r,t){var v=Ad(t,void 0,f);v===void 0&&t!==void 0&&L(44);g.set(r,v)});a.Ob(Yf());var h={am:mg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Sf:e!==void 0?function(r){e.Rc.Sf(r)}:void 0,Kb:function(){return b},log:function(){},op:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},rq:!!QB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if($E()){var m=aF(),n,p;h.xb={Pj:[],Tf:{},bc:function(r,t,v){t===1&&(n=r);t===7&&(p=v);m(r,t,v)},Gh:Rh()};h.log=function(r){var t=xa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=We(a,h,[b,g]);a.Ob();q instanceof Aa&&(q.type==="return"?q=q.data:q=void 0);return zd(q,void 0,f)}}function dF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;jb(b)&&(a.gtmOnSuccess=function(){Kc(b)});jb(c)&&(a.gtmOnFailure=function(){Kc(c)})};function eF(a){}eF.M="internal.addAdsClickIds";function fF(a,b){var c=this;}fF.publicName="addConsentListener";var gF=!1;function hF(a){for(var b=0;b<a.length;++b)if(gF)try{a[b]()}catch(c){L(77)}else a[b]()}function iF(a,b,c){var d=this,e;if(!kh(a)||!gh(b)||!lh(c))throw G(this.getName(),["string","function","string|undefined"],arguments);hF([function(){I(d,"listen_data_layer",a)}]);e=QC().addListener(a,zd(b),c===null?void 0:c);return e}iF.M="internal.addDataLayerEventListener";function jF(a,b,c){}jF.publicName="addDocumentEventListener";function kF(a,b,c,d){}kF.publicName="addElementEventListener";function lF(a){return a.K.tb()};function mF(a){}mF.publicName="addEventCallback";
var nF=function(a){return typeof a==="string"?a:String(wp())},qF=function(a,b){oF(a,"init",!1)||(pF(a,"init",!0),b())},oF=function(a,b,c){var d=rF(a);return Ab(d,b,c)},sF=function(a,b,c,d){var e=rF(a),f=Ab(e,b,d);e[b]=c(f)},pF=function(a,b,c){rF(a)[b]=c},rF=function(a){var b=tp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},tF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Wc(a,"className"),"gtm.elementId":a.for||Lc(a,"id")||"","gtm.elementTarget":a.formTarget||
Wc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Wc(a,"href")||a.src||a.code||a.codebase||"";return d};
var wF=function(a,b,c){if(!a.elements)return 0;for(var d=b.dataset[c],e=0,f=1;e<a.elements.length;e++){var g=a.elements[e],h=g.tagName.toLowerCase();if(!(uF.indexOf(h)<0||h==="input"&&vF.indexOf(g.type.toLowerCase())>=0)){if(g.dataset[c]===d)return f;f++}}return 0},xF=function(a){if(a.form){var b;return((b=a.form)==null?0:b.tagName)?a.form:A.getElementById(a.form)}return Oc(a,["form"],100)},uF=["input","select","textarea"],vF=["button","hidden","image","reset","submit"];
function BF(a){}BF.M="internal.addFormAbandonmentListener";function CF(a,b,c,d){}
CF.M="internal.addFormData";var DF={},EF=[],FF={},GF=0,HF=0;
var JF=function(){Ic(A,"change",function(a){for(var b=0;b<EF.length;b++)EF[b](a)});Ic(x,"pagehide",function(){IF()})},IF=function(){sb(FF,function(a,b){var c=DF[a];c&&sb(b,function(d,e){KF(e,c)})})},NF=function(a,b){var c=""+a;if(DF[c])DF[c].push(b);else{var d=[b];DF[c]=d;var e=FF[c];e||(e={},FF[c]=e);EF.push(function(f){var g=f.target;if(g){var h=xF(g);if(h){var m=LF(h,"gtmFormInteractId",function(){return GF++}),n=LF(g,"gtmFormInteractFieldId",function(){return HF++}),p=e[m];p?(p.Gc&&(x.clearTimeout(p.Gc),
p.fc.dataset.gtmFormInteractFieldId!==n&&KF(p,d)),p.fc=g,MF(p,d,a)):(e[m]={form:h,fc:g,sequenceNumber:0,Gc:null},MF(e[m],d,a))}}})}},KF=function(a,b){var c=a.form,d=a.fc,e=tF(c,"gtm.formInteract"),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name");e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldPosition"]=wF(c,d,"gtmFormInteractFieldId");e["gtm.interactSequenceNumber"]=a.sequenceNumber;
e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name");e["gtm.interactedFormFieldType"]=d.getAttribute("type");for(var g=0;g<b.length;g++)b[g](e);a.sequenceNumber++;a.Gc=null},MF=function(a,b,c){c?a.Gc=x.setTimeout(function(){KF(a,b)},c):KF(a,b)},LF=function(a,b,c){var d=a.dataset[b];if(d)return d;d=String(c());return a.dataset[b]=d};
function OF(a,b){if(!gh(a)||!eh(b))throw G(this.getName(),["function","Object|undefined"],arguments);var c=zd(b)||{},d=Number(c.interval);if(!d||d<0)d=0;var e=zd(a),f;oF("pix.fil","init")?f=oF("pix.fil","reg"):(JF(),f=NF,pF("pix.fil","reg",NF),pF("pix.fil","init",!0));f(d,e);}OF.M="internal.addFormInteractionListener";
var QF=function(a,b,c){var d=tF(a,"gtm.formSubmit");d["gtm.interactedFormName"]=a.getAttribute("name");d["gtm.interactedFormLength"]=a.length;d["gtm.willOpenInCurrentWindow"]=!b&&PF(a);c&&c.value&&(d["gtm.formSubmitButtonText"]=c.value);var e=a.action;e&&e.tagName&&(e=a.cloneNode(!1).action);d["gtm.elementUrl"]=e;d["gtm.formCanceled"]=b;return d},RF=function(a,b){var c=oF("pix.fsl",a?"nv.mwt":"mwt",0);x.setTimeout(b,c)},SF=function(a,b,c,d,e){var f=oF("pix.fsl",c?"nv.mwt":"mwt",0),g=oF("pix.fsl",
c?"runIfCanceled":"runIfUncanceled",[]);if(!g.length)return!0;var h=QF(a,c,e);L(121);if(h["gtm.elementUrl"]==="https://www.facebook.com/tr/")return L(122),!0;if(d&&f){for(var m=Jb(b,g.length),n=0;n<g.length;++n)g[n](h,m);return m.done}for(var p=0;p<g.length;++p)g[p](h,function(){});return!0},TF=function(){var a=[],b=function(c){return ob(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);return d?d.button:null}}},
PF=function(a){var b=Wc(a,"target");return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},UF=function(){var a=TF(),b=HTMLFormElement.prototype.submit;Ic(A,"click",function(c){var d=c.target;if(d){var e=Oc(d,["button","input"],100);if(e&&(e.type==="submit"||e.type==="image")&&e.name&&Lc(e,"value")){var f=xF(e);f&&a.store(f,e)}}},!1);Ic(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=PF(d)&&!e,g=a.get(d),h=!0,m=function(){if(h){var n,
p={};g&&(n=A.createElement("input"),n.type="hidden",n.name=g.name,n.value=g.value,d.appendChild(n),g.getAttribute("formaction")&&(p.action=d.getAttribute("action"),ic(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(p.enctype=d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(p.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(p.validate=d.getAttribute("validate"),
d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(p.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);n&&(d.removeChild(n),p.hasOwnProperty("action")&&ic(d,p.action),p.hasOwnProperty("enctype")&&d.setAttribute("enctype",p.enctype),p.hasOwnProperty("method")&&d.setAttribute("method",p.method),p.hasOwnProperty("validate")&&d.setAttribute("validate",p.validate),p.hasOwnProperty("target")&&d.setAttribute("target",
p.target))}};if(SF(d,m,e,f,g))return h=!1,c.returnValue;RF(e,m);e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1);return!1},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0,e=function(){d&&b.call(c)};SF(c,e,!1,PF(c))?(b.call(c),d=!1):RF(!1,e)}};
function VF(a,b){if(!gh(a)||!eh(b))throw G(this.getName(),["function","Object|undefined"],arguments);var c=zd(b,this.K,1)||{},d=c.waitForCallbacks,e=c.waitForCallbacksTimeout,f=c.checkValidation;e=e&&e>0?e:2E3;var g=zd(a,this.K,1);if(d){var h=function(n){return Math.max(e,n)};sF("pix.fsl","mwt",h,0);f||sF("pix.fsl","nv.mwt",h,0)}var m=function(n){n.push(g);return n};sF("pix.fsl","runIfUncanceled",m,[]);f||sF("pix.fsl","runIfCanceled",
m,[]);oF("pix.fsl","init")||(UF(),pF("pix.fsl","init",!0));}VF.M="internal.addFormSubmitListener";
function $F(a){}$F.M="internal.addGaSendListener";function aG(a){if(!a)return{};var b=a.op;return PB(b.type,b.index,b.name)}function bG(a){return a?{originatingEntity:aG(a)}:{}};function jG(a){var b=sp.zones;return b?b.getIsAllowedFn(ym(),a):function(){return!0}}function kG(){var a=sp.zones;a&&a.unregisterChild(ym())}
function lG(){BC(xm(),function(a){var b=sp.zones;return b?b.isActive(ym(),a.originalEventData["gtm.uniqueEventId"]):!0});zC(xm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return jG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var mG=function(a,b){this.tagId=a;this.xe=b};
function nG(a,b){var c=this;return a}nG.M="internal.loadGoogleTag";function oG(a){return new rd("",function(b){var c=this.evaluate(b);if(c instanceof rd)return new rd("",function(){var d=xa.apply(0,arguments),e=this,f=kd(lF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.sb();h.Ld(f);return c.Mb.apply(c,[h].concat(ta(g)))})})};function pG(a,b,c){var d=this;}pG.M="internal.addGoogleTagRestriction";var qG={},rG=[];
function yG(a,b){}
yG.M="internal.addHistoryChangeListener";function zG(a,b,c){}zG.publicName="addWindowEventListener";function AG(a,b){return!0}AG.publicName="aliasInWindow";function BG(a,b,c){if(!kh(a)||!kh(b))throw G(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Gq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!jd(e[d[f]]))throw Error("apendRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}if(e[d[f]]===void 0)e[d[f]]=[];else if(!Array.isArray(e[d[f]]))throw Error("appendRemoteConfigParameter failed, destination is not an array: "+
d[f]);e[d[f]].push(zd(c,this.K));}BG.M="internal.appendRemoteConfigParameter";function CG(a){var b;return b}
CG.publicName="callInWindow";function DG(a){}DG.publicName="callLater";function EG(a){}EG.M="callOnDomReady";function FG(a){}FG.M="callOnWindowLoad";function GG(a,b){var c;return c}GG.M="internal.computeGtmParameter";function HG(a,b){var c=this;}HG.M="internal.consentScheduleFirstTry";function IG(a,b){var c=this;}IG.M="internal.consentScheduleRetry";function JG(a){var b;return b}JG.M="internal.copyFromCrossContainerData";function KG(a,b){var c;var d=Ad(c,this.K,zh(lF(this).Kb())?2:1);d===void 0&&c!==void 0&&L(45);return d}KG.publicName="copyFromDataLayer";
function LG(a){var b=void 0;return b}LG.M="internal.copyFromDataLayerCache";function MG(a){var b;return b}MG.publicName="copyFromWindow";function NG(a){var b=void 0;return Ad(b,this.K,1)}NG.M="internal.copyKeyFromWindow";var OG=function(a){return a===Mm.X.Da&&dn[a]===Lm.Ia.pe&&!ip(J.m.U)};var PG=function(){return"0"},QG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Rk(a,b,"0")};var RG={},SG={},TG={},UG={},VG={},WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH=(pH[J.m.Qa]=(RG[2]=[OG],RG),pH[J.m.tf]=(SG[2]=[OG],SG),pH[J.m.ff]=(TG[2]=[OG],TG),pH[J.m.si]=(UG[2]=[OG],UG),pH[J.m.ui]=(VG[2]=[OG],VG),pH[J.m.wi]=(WG[2]=[OG],WG),pH[J.m.xi]=(XG[2]=[OG],XG),pH[J.m.yi]=(YG[2]=[OG],YG),pH[J.m.yc]=(ZG[2]=[OG],ZG),pH[J.m.vf]=($G[2]=[OG],$G),pH[J.m.wf]=(aH[2]=[OG],aH),pH[J.m.xf]=(bH[2]=[OG],bH),pH[J.m.yf]=(cH[2]=
[OG],cH),pH[J.m.zf]=(dH[2]=[OG],dH),pH[J.m.Af]=(eH[2]=[OG],eH),pH[J.m.Bf]=(fH[2]=[OG],fH),pH[J.m.Cf]=(gH[2]=[OG],gH),pH[J.m.mb]=(hH[1]=[OG],hH),pH[J.m.dd]=(iH[1]=[OG],iH),pH[J.m.hd]=(jH[1]=[OG],jH),pH[J.m.Xd]=(kH[1]=[OG],kH),pH[J.m.Qe]=(lH[1]=[function(a){return E(102)&&OG(a)}],lH),pH[J.m.jd]=(mH[1]=[OG],mH),pH[J.m.Aa]=(nH[1]=[OG],nH),pH[J.m.Wa]=(oH[1]=[OG],oH),pH),rH={},sH=(rH[J.m.mb]=PG,rH[J.m.dd]=PG,rH[J.m.hd]=PG,rH[J.m.Xd]=PG,rH[J.m.Qe]=PG,rH[J.m.jd]=function(a){if(!jd(a))return{};var b=kd(a,
null);delete b.match_id;return b},rH[J.m.Aa]=QG,rH[J.m.Wa]=QG,rH),tH={},uH={},vH=(uH[O.A.jb]=(tH[2]=[OG],tH),uH),wH={};var xH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};xH.prototype.getValue=function(a){a=a===void 0?Mm.X.Gb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};xH.prototype.H=function(){return hd(this.C)==="array"||jd(this.C)?kd(this.C,null):this.C};
var yH=function(){},zH=function(a,b){this.conditions=a;this.C=b},AH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new xH(c,e,g,a.C[b]||yH)},BH,CH;var DH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;Q(this,g,d[g])}},Mv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,P(a,O.A.Qf))},R=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(BH!=null||(BH=new zH(qH,sH)),e=AH(BH,b,c));d[b]=e};
DH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return R(this,a,b),!0;if(!jd(c))return!1;R(this,a,Object.assign(c,b));return!0};var EH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
DH.prototype.copyToHitData=function(a,b,c){var d=N(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&lb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&R(this,a,d)};
var P=function(a,b){var c=a.metadata[b];if(b===O.A.Qf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,P(a,O.A.Qf))},Q=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(CH!=null||(CH=new zH(vH,wH)),e=AH(CH,b,c));d[b]=e},FH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},fw=function(a,b,c){var d=Uw(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function GH(a,b){var c;if(!dh(a)||!eh(b))throw G(this.getName(),["Object","Object|undefined"],arguments);var d=zd(b)||{},e=zd(a,this.K,1).Bb(),f=e.D;d.omitEventContext&&(f=gq(new Wp(e.D.eventId,e.D.priorityId)));var g=new DH(e.target,e.eventName,f);if(!d.omitHitData)for(var h=EH(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;R(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=FH(e),r=l(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var v=t.value;Q(g,v,q[v])}g.isAborted=e.isAborted;c=Ad(Cw(g),this.K,1);return c}GH.M="internal.copyPreHit";function HH(a,b){var c=null;return Ad(c,this.K,2)}HH.publicName="createArgumentsQueue";function IH(a){return Ad(function(c){var d=ZB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
ZB(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}IH.M="internal.createGaCommandQueue";function JH(a){return Ad(function(){if(!jb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
zh(lF(this).Kb())?2:1)}JH.publicName="createQueue";function KH(a,b){var c=null;if(!kh(a)||!lh(b))throw G(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new wd(new RegExp(a,d))}catch(e){}return c}KH.M="internal.createRegex";function LH(a){}LH.M="internal.declareConsentState";function MH(a){var b="";return b}MH.M="internal.decodeUrlHtmlEntities";function NH(a,b,c){var d;return d}NH.M="internal.decorateUrlWithGaCookies";function OH(){}OH.M="internal.deferCustomEvents";function PH(a){var b;I(this,"detect_user_provided_data","auto");var c=zd(a)||{},d=tx({Ce:!!c.includeSelector,De:!!c.includeVisibility,Vf:c.excludeElementSelectors,Zb:c.fieldFilters,Jh:!!c.selectMultipleElements});b=new Ua;var e=new nd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(QH(f[g]));d.Gj!==void 0&&b.set("preferredEmailElement",QH(d.Gj));b.set("status",d.status);if(E(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(rc&&
rc.userAgent||"")){}return b}
var RH=function(a){switch(a){case rx.kc:return"email";case rx.zd:return"phone_number";case rx.sd:return"first_name";case rx.yd:return"last_name";case rx.Ri:return"street";case rx.Mh:return"city";case rx.Mi:return"region";case rx.Nf:return"postal_code";case rx.Ke:return"country"}},QH=function(a){var b=new Ua;b.set("userData",a.la);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(E(33)){}else switch(a.type){case rx.kc:b.set("type","email")}return b};PH.M="internal.detectUserProvidedData";
function UH(a,b){return f}UH.M="internal.enableAutoEventOnClick";var XH=function(a){if(!VH){var b=function(){var c=A.body;if(c)if(WH)(new MutationObserver(function(){for(var e=0;e<VH.length;e++)Kc(VH[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Ic(c,"DOMNodeInserted",function(){d||(d=!0,Kc(function(){d=!1;for(var e=0;e<VH.length;e++)Kc(VH[e])}))})}};VH=[];A.body?b():Kc(b)}VH.push(a)},WH=!!x.MutationObserver,VH;
function bI(a,b){return p}bI.M="internal.enableAutoEventOnElementVisibility";function cI(){}cI.M="internal.enableAutoEventOnError";var dI={},eI=[],fI={},gI=0,hI=0;
var jI=function(){sb(fI,function(a,b){var c=dI[a];c&&sb(b,function(d,e){iI(e,c)})})},mI=function(a,b){var c=""+b;if(dI[c])dI[c].push(a);else{var d=[a];dI[c]=d;var e=fI[c];e||(e={},fI[c]=e);eI.push(function(f){var g=f.target;if(g){var h=xF(g);if(h){var m=kI(h,"gtmFormInteractId",function(){return gI++}),n=kI(g,"gtmFormInteractFieldId",function(){return hI++});if(m!==null&&n!==null){var p=e[m];p?(p.Gc&&(x.clearTimeout(p.Gc),p.fc.getAttribute("data-gtm-form-interact-field-id")!==n&&iI(p,d)),p.fc=g,lI(p,
d,b)):(e[m]={form:h,fc:g,sequenceNumber:0,Gc:null},lI(e[m],d,b))}}}})}},iI=function(a,b){var c=a.form,d=a.fc,e=tF(c,"gtm.formInteract",b),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name")!=null?c.getAttribute("name"):void 0;e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name")!=null?d.getAttribute("name"):void 0;e["gtm.interactedFormFieldPosition"]=
wF(c,d,"gtmFormInteractFieldId");e["gtm.interactedFormFieldType"]=d.getAttribute("type")!=null?d.getAttribute("type"):void 0;e["gtm.interactSequenceNumber"]=a.sequenceNumber;HD(e);a.sequenceNumber++;a.Gc=null},lI=function(a,b,c){c?a.Gc=x.setTimeout(function(){iI(a,b)},c):iI(a,b)},kI=function(a,b,c){var d;try{if(d=a.dataset[b])return d;d=String(c());a.dataset[b]=d}catch(e){d=null}return d};
function nI(a,b){var c=this;if(!eh(a))throw G(this.getName(),["Object|undefined","any"],arguments);hF([function(){I(c,"detect_form_interaction_events")}]);var d=nF(b),e=a&&Number(a.get("interval"));e>0&&isFinite(e)||(e=0);if(oF("fil","init",!1)){var f=oF("fil","reg");if(f)f(d,e);else throw Error("Failed to register trigger: "+d);}else Ic(A,"change",function(g){for(var h=0;h<eI.length;h++)eI[h](g)}),Ic(x,"pagehide",function(){jI()}),
mI(d,e),pF("fil","reg",mI),pF("fil","init",!0);return d}nI.M="internal.enableAutoEventOnFormInteraction";
var oI=function(a,b,c,d,e){var f=oF("fsl",c?"nv.mwt":"mwt",0),g;g=c?oF("fsl","nv.ids",[]):oF("fsl","ids",[]);if(!g.length)return!0;var h=tF(a,"gtm.formSubmit",g),m=a.action;m&&m.tagName&&(m=a.cloneNode(!1).action);L(121);if(m==="https://www.facebook.com/tr/")return L(122),!0;h["gtm.elementUrl"]=m;h["gtm.formCanceled"]=c;a.getAttribute("name")!=null&&(h["gtm.interactedFormName"]=a.getAttribute("name"));e&&(h["gtm.formSubmitElement"]=e,h["gtm.formSubmitElementText"]=e.value);if(d&&f){if(!GD(h,ID(b,
f),f))return!1}else GD(h,function(){},f||2E3);return!0},pI=function(){var a=[],b=function(c){return ob(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);if(d)return d.button}}},qI=function(a){var b=a.target;return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},rI=function(){var a=pI(),b=HTMLFormElement.prototype.submit;Ic(A,"click",function(c){var d=c.target;if(d){var e=Oc(d,["button","input"],100);if(e&&(e.type===
"submit"||e.type==="image")&&e.name&&Lc(e,"value")){var f=xF(e);f&&a.store(f,e)}}},!1);Ic(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=qI(d)&&!e,g=a.get(d),h=!0;if(oI(d,function(){if(h){var m=null,n={};g&&(m=A.createElement("input"),m.type="hidden",m.name=g.name,m.value=g.value,d.appendChild(m),g.hasAttribute("formaction")&&(n.action=d.getAttribute("action"),ic(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(n.enctype=
d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(n.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(n.validate=d.getAttribute("validate"),d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(n.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);m&&(d.removeChild(m),n.hasOwnProperty("action")&&
ic(d,n.action),n.hasOwnProperty("enctype")&&d.setAttribute("enctype",n.enctype),n.hasOwnProperty("method")&&d.setAttribute("method",n.method),n.hasOwnProperty("validate")&&d.setAttribute("validate",n.validate),n.hasOwnProperty("target")&&d.setAttribute("target",n.target))}},e,f,g))h=!1;else return e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1),!1;return c.returnValue},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0;oI(c,function(){d&&b.call(c)},!1,qI(c))&&(b.call(c),d=
!1)}};
function sI(a,b){var c=this;if(!eh(a))throw G(this.getName(),["Object|undefined","any"],arguments);var d=a&&a.get("waitForTags");hF([function(){I(c,"detect_form_submit_events",{waitForTags:!!d})}]);var e=a&&a.get("checkValidation"),f=nF(b);if(d){var g=Number(a.get("waitForTagsTimeout"));g>0&&isFinite(g)||(g=2E3);var h=function(n){return Math.max(g,n)};sF("fsl","mwt",h,0);e||sF("fsl","nv.mwt",h,0)}var m=function(n){n.push(f);
return n};sF("fsl","ids",m,[]);e||sF("fsl","nv.ids",m,[]);oF("fsl","init",!1)||(rI(),pF("fsl","init",!0));return f}sI.M="internal.enableAutoEventOnFormSubmit";
function xI(){var a=this;}xI.M="internal.enableAutoEventOnGaSend";var yI={},zI=[];
var BI=function(a,b){var c=""+b;if(yI[c])yI[c].push(a);else{var d=[a];yI[c]=d;var e=AI("gtm.historyChange-v2"),f=-1;zI.push(function(g){f>=0&&x.clearTimeout(f);b?f=x.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},AI=function(a){var b=x.location.href,c={source:null,state:x.history.state||null,url:Nk(Qk(b)),cb:Kk(Qk(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.cb!==d.cb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.cb,
"gtm.newUrlFragment":d.cb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;HD(h)}}},CI=function(a,b){var c=x.history,d=c[a];if(jb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=x.location.href;b({source:a,state:e,url:Nk(Qk(h)),cb:Kk(Qk(h),"fragment")})}}catch(e){}},EI=function(a){x.addEventListener("popstate",function(b){var c=DI(b);a({source:"popstate",state:b.state,url:Nk(Qk(c)),cb:Kk(Qk(c),
"fragment")})})},FI=function(a){x.addEventListener("hashchange",function(b){var c=DI(b);a({source:"hashchange",state:null,url:Nk(Qk(c)),cb:Kk(Qk(c),"fragment")})})},DI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||x.location.href};
function GI(a,b){var c=this;if(!eh(a))throw G(this.getName(),["Object|undefined","any"],arguments);hF([function(){I(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!oF(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<zI.length;n++)zI[n](m)},f=nF(b),BI(f,e),pF(d,"reg",BI)):g=AI("gtm.historyChange");FI(g);EI(g);CI("pushState",
g);CI("replaceState",g);pF(d,"init",!0)}else if(d==="ehl"){var h=oF(d,"reg");h&&(f=nF(b),h(f,e))}d==="hl"&&(f=void 0);return f}GI.M="internal.enableAutoEventOnHistoryChange";var HI=["http://","https://","javascript:","file://"];
var II=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Wc(b,"href");if(c.indexOf(":")!==-1&&!HI.some(function(h){return Eb(c,h)}))return!1;var d=c.indexOf("#"),e=Wc(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=Nk(Qk(c)),g=Nk(Qk(x.location.href));return f!==g}return!0},JI=function(a,b){for(var c=Kk(Qk((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Wc(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},KI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Oc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=oF("lcl",e?"nv.mwt":"mwt",0),g;g=e?oF("lcl","nv.ids",[]):oF("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=oF("lcl","aff.map",{})[n];p&&!JI(p,d)||h.push(n)}if(h.length){var q=II(c,d),r=tF(d,"gtm.linkClick",
h);r["gtm.elementText"]=Mc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!ob(String(Wc(d,"rel")||"").split(" "),function(y){return y.toLowerCase()==="noreferrer"}),v=x[(Wc(d,"target")||"_self").substring(1)],u=!0,w=ID(function(){var y;if(y=u&&v){var z;a:if(t){var C;try{C=new MouseEvent(c.type,{bubbles:!0})}catch(D){if(!A.createEvent){z=!1;break a}C=A.createEvent("MouseEvents");C.initEvent(c.type,!0,!0)}C.C=!0;c.target.dispatchEvent(C);z=!0}else z=!1;y=!z}y&&(v.location.href=Wc(d,
"href"))},f);if(GD(r,w,f))u=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else GD(r,function(){},f||2E3);return!0}}}var b=0;Ic(A,"click",a,!1);Ic(A,"auxclick",a,!1)};
function LI(a,b){var c=this;if(!eh(a))throw G(this.getName(),["Object|undefined","any"],arguments);var d=zd(a);hF([function(){I(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=nF(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};sF("lcl","mwt",n,0);f||sF("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};sF("lcl","ids",p,[]);f||sF("lcl","nv.ids",p,[]);g&&sF("lcl","aff.map",function(q){q[h]=g;return q},{});oF("lcl","init",!1)||(KI(),pF("lcl","init",!0));return h}LI.M="internal.enableAutoEventOnLinkClick";var MI,NI;
var OI=function(a){return oF("sdl",a,{})},PI=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];sF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},SI=function(){function a(){QI();RI(a,!0)}return a},TI=function(){function a(){f?e=x.setTimeout(a,c):(e=0,QI(),RI(b));f=!1}function b(){d&&MI();e?f=!0:(e=x.setTimeout(a,c),pF("sdl","pending",!0))}var c=250,d=!1;A.scrollingElement&&A.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
RI=function(a,b){oF("sdl","init",!1)&&!UI()&&(b?Jc(x,"scrollend",a):Jc(x,"scroll",a),Jc(x,"resize",a),pF("sdl","init",!1))},QI=function(){var a=MI(),b=a.depthX,c=a.depthY,d=b/NI.scrollWidth*100,e=c/NI.scrollHeight*100;VI(b,"horiz.pix","PIXELS","horizontal");VI(d,"horiz.pct","PERCENT","horizontal");VI(c,"vert.pix","PIXELS","vertical");VI(e,"vert.pct","PERCENT","vertical");pF("sdl","pending",!1)},VI=function(a,b,c,d){var e=OI(b),f={},g;for(g in e)if(f={Ge:f.Ge},f.Ge=g,e.hasOwnProperty(f.Ge)){var h=
Number(f.Ge);if(!(a<h)){var m={};QD((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.Ge].join(","),m));sF("sdl",b,function(n){return function(p){delete p[n.Ge];return p}}(f),{})}}},XI=function(){sF("sdl","scr",function(a){a||(a=A.scrollingElement||A.body&&A.body.parentNode);return NI=a},!1);sF("sdl","depth",function(a){a||(a=WI());return MI=a},!1)},WI=function(){var a=0,b=0;return function(){var c=Xw(),d=c.height;
a=Math.max(NI.scrollLeft+c.width,a);b=Math.max(NI.scrollTop+d,b);return{depthX:a,depthY:b}}},UI=function(){return!!(Object.keys(OI("horiz.pix")).length||Object.keys(OI("horiz.pct")).length||Object.keys(OI("vert.pix")).length||Object.keys(OI("vert.pct")).length)};
function YI(a,b){var c=this;if(!dh(a))throw G(this.getName(),["Object","any"],arguments);hF([function(){I(c,"detect_scroll_events")}]);XI();if(!NI)return;var d=nF(b),e=zd(a);switch(e.horizontalThresholdUnits){case "PIXELS":PI(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":PI(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":PI(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":PI(e.verticalThresholds,
d,"vert.pct")}oF("sdl","init",!1)?oF("sdl","pending",!1)||Kc(function(){QI()}):(pF("sdl","init",!0),pF("sdl","pending",!0),Kc(function(){QI();if(UI()){var f=TI();"onscrollend"in x?(f=SI(),Ic(x,"scrollend",f)):Ic(x,"scroll",f);Ic(x,"resize",f)}else pF("sdl","init",!1)}));return d}YI.M="internal.enableAutoEventOnScroll";function ZI(a){return function(){if(a.limit&&a.Bj>=a.limit)a.Dh&&x.clearInterval(a.Dh);else{a.Bj++;var b=zb();HD({event:a.eventName,"gtm.timerId":a.Dh,"gtm.timerEventNumber":a.Bj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Nm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Nm,"gtm.triggers":a.Kq})}}}
function $I(a,b){
return f}$I.M="internal.enableAutoEventOnTimer";
var aJ=function(a,b,c){function d(){var g=a();f+=e?(zb()-e)*g.playbackRate/1E3:0;e=zb()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.ej,q=m?Math.round(m):h?Math.round(n.ej*h):Math.round(n.gm),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),t=A.hidden?!1:Yw(c)>=.5;d();var v=void 0;b!==void 0&&(v=[b]);var u=tF(c,"gtm.video",v);u["gtm.videoProvider"]="youtube";u["gtm.videoStatus"]=g;u["gtm.videoUrl"]=n.url;u["gtm.videoTitle"]=n.title;u["gtm.videoDuration"]=Math.round(p);u["gtm.videoCurrentTime"]=
Math.round(q);u["gtm.videoElapsedTime"]=Math.round(f);u["gtm.videoPercent"]=r;u["gtm.videoVisible"]=t;return u},Jm:function(){e=zb()},we:function(){d()}}};var lc=va(["data-gtm-yt-inspected-"]),bJ=["www.youtube.com","www.youtube-nocookie.com"],cJ,dJ=!1;
var eJ=function(a,b,c){var d=a.map(function(g){return{ab:g,pg:g,ng:void 0}});if(!b.length)return d;var e=b.map(function(g){return{ab:g*c,pg:void 0,ng:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.ab-h.ab});return f},fJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},gJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},hJ=function(a,b){var c,d;function e(){t=aJ(function(){return{url:w,title:y,ej:u,gm:a.getCurrentTime(),playbackRate:z}},b.hc,a.getIframe());u=0;y=w="";z=1;return f}function f(H){switch(H){case 1:u=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var M=a.getVideoData();y=M?M.title:""}z=a.getPlaybackRate();if(b.Zi){var S=t.createEvent("start");HD(S)}else t.we();v=eJ(b.Ij,b.Hj,a.getDuration());return g(H);default:return f}}function g(){C=a.getCurrentTime();D=yb().getTime();
t.Jm();r();return h}function h(H){var M;switch(H){case 0:return n(H);case 2:M="pause";case 3:var S=a.getCurrentTime()-C;M=Math.abs((yb().getTime()-D)/1E3*z-S)>1?"seek":M||"buffering";if(a.getCurrentTime())if(b.Yi){var ca=t.createEvent(M);HD(ca)}else t.we();q();return m;case -1:return e(H);default:return h}}function m(H){switch(H){case 0:return n(H);case 1:return g(H);case -1:return e(H);default:return m}}function n(){for(;d;){var H=c;x.clearTimeout(d);H()}if(b.Xi){var M=t.createEvent("complete",1);
HD(M)}return e(-1)}function p(){}function q(){d&&(x.clearTimeout(d),d=0,c=p)}function r(){if(v.length&&z!==0){var H=-1,M;do{M=v[0];if(M.ab>a.getDuration())return;H=(M.ab-a.getCurrentTime())/z;if(H<0&&(v.shift(),v.length===0))return}while(H<0);c=function(){d=0;c=p;if(v.length>0&&v[0].ab===M.ab){v.shift();var S=t.createEvent("progress",M.ng,M.pg);HD(S)}r()};d=x.setTimeout(c,H*1E3)}}var t,v=[],u,w,y,z,C,D,F=e(-1);d=0;c=p;return{onStateChange:function(H){F=F(H)},onPlaybackRateChange:function(H){C=a.getCurrentTime();
D=yb().getTime();t.we();z=H;q();r()}}},jJ=function(a){Kc(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)iJ(d[f],a)}var c=A;b();XH(b)})},iJ=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.hc)&&(nc(a,"data-gtm-yt-inspected-"+b.hc),kJ(a,b.Yf))){a.id||(a.id=lJ());var c=x.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=hJ(d,b),f={},g;for(g in e)f={jg:f.jg},f.jg=g,e.hasOwnProperty(f.jg)&&d.addEventListener(f.jg,function(h){return function(m){return e[h.jg](m.data)}}(f))}},
kJ=function(a,b){var c=a.getAttribute("src");if(mJ(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(cJ||(cJ=A.location.protocol+"//"+A.location.hostname,A.location.port&&(cJ+=":"+A.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(cJ));var f;f=Ub(d);a.src=Vb(f).toString();return!0}}return!1},mJ=function(a,b){if(!a)return!1;for(var c=0;c<bJ.length;c++)if(a.indexOf("//"+bJ[c]+"/"+b)>=0)return!0;
return!1},lJ=function(){var a=""+Math.round(Math.random()*1E9);return A.getElementById(a)?lJ():a};
function nJ(a,b){var c=this;var d=function(){jJ(q)};if(!dh(a))throw G(this.getName(),["Object","any"],arguments);hF([function(){I(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=nF(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=gJ(zd(a.get("progressThresholdsPercent"))),n=fJ(zd(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={Zi:f,Xi:g,Yi:h,Hj:m,Ij:n,Yf:p,hc:e},r=x.YT;if(r)return r.ready&&r.ready(d),e;var t=x,v=t.onYouTubeIframeAPIReady;t.onYouTubeIframeAPIReady=function(){v&&v();d()};Kc(function(){for(var u=A.getElementsByTagName("script"),w=u.length,y=0;y<w;y++){var z=u[y].getAttribute("src");if(mJ(z,"iframe_api")||mJ(z,"player_api"))return e}for(var C=A.getElementsByTagName("iframe"),D=C.length,F=0;F<D;F++)if(!dJ&&kJ(C[F],q.Yf))return Dc("https://www.youtube.com/iframe_api"),
dJ=!0,e});return e}nJ.M="internal.enableAutoEventOnYouTubeActivity";dJ=!1;function oJ(a,b){if(!kh(a)||!eh(b))throw G(this.getName(),["string","Object|undefined"],arguments);var c=b?zd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Gh(f,c);return e}oJ.M="internal.evaluateBooleanExpression";var pJ;function qJ(a){var b=!1;return b}qJ.M="internal.evaluateMatchingRules";function $J(){return nr(7)&&nr(9)&&nr(10)};function VK(a,b,c,d){}VK.M="internal.executeEventProcessor";function WK(a){var b;return Ad(b,this.K,1)}WK.M="internal.executeJavascriptString";function XK(a){var b;return b};function YK(a){var b="";return b}YK.M="internal.generateClientId";function ZK(a){var b={};return Ad(b)}ZK.M="internal.getAdsCookieWritingOptions";function $K(a,b){var c=!1;return c}$K.M="internal.getAllowAdPersonalization";function aL(){var a;return a}aL.M="internal.getAndResetEventUsage";function bL(a,b){b=b===void 0?!0:b;var c;return c}bL.M="internal.getAuid";var cL=null;
function dL(){var a=new Ua;I(this,"read_container_data"),E(49)&&cL?a=cL:(a.set("containerId",'G-06RDJQW848'),a.set("version",'2'),a.set("environmentName",''),a.set("debugMode",ng),a.set("previewMode",og.Rm),a.set("environmentMode",og.lp),a.set("firstPartyServing",kk()||Lj.N),a.set("containerUrl",uc),a.Ua(),E(49)&&(cL=a));return a}
dL.publicName="getContainerVersion";function eL(a,b){b=b===void 0?!0:b;var c;return c}eL.publicName="getCookieValues";function fL(){var a="";return a}fL.M="internal.getCorePlatformServicesParam";function gL(){return go()}gL.M="internal.getCountryCode";function hL(){var a=[];a=wm();return Ad(a)}hL.M="internal.getDestinationIds";function iL(a){var b=new Ua;return b}iL.M="internal.getDeveloperIds";function jL(a){var b;return b}jL.M="internal.getEcsidCookieValue";function kL(a,b){var c=null;return c}kL.M="internal.getElementAttribute";function lL(a){var b=null;return b}lL.M="internal.getElementById";function mL(a){var b="";return b}mL.M="internal.getElementInnerText";function nL(a,b){var c=null;return Ad(c)}nL.M="internal.getElementProperty";function oL(a){var b;return b}oL.M="internal.getElementValue";function pL(a){var b=0;return b}pL.M="internal.getElementVisibilityRatio";function qL(a){var b=null;return b}qL.M="internal.getElementsByCssSelector";
function rL(a){var b;if(!kh(a))throw G(this.getName(),["string"],arguments);I(this,"read_event_data",a);var c;a:{var d=a,e=lF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var v=r[t].split("."),u=0;u<v.length;u++)n.push(v[u]),u!==v.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",z=l(n),C=z.next();!C.done;C=
z.next()){var D=C.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var F=l(w),H=F.next();!H.done;H=F.next()){if(f==null){c=void 0;break a}f=f[H.value]}c=f}else c=void 0}b=Ad(c,this.K,1);return b}rL.M="internal.getEventData";var sL={};sL.enableDecodeUri=E(92);sL.enableGaAdsConversions=E(122);sL.enableGaAdsConversionsClientId=E(121);sL.enableOverrideAdsCps=E(170);sL.enableUrlDecodeEventUsage=E(139);function tL(){return Ad(sL)}tL.M="internal.getFlags";function uL(){var a;return a}uL.M="internal.getGsaExperimentId";function vL(){return new wd(vE)}vL.M="internal.getHtmlId";function wL(a){var b;return b}wL.M="internal.getIframingState";function xL(a,b){var c={};return Ad(c)}xL.M="internal.getLinkerValueFromLocation";function yL(){var a=new Ua;return a}yL.M="internal.getPrivacyStrings";function zL(a,b){var c;if(!kh(a)||!kh(b))throw G(this.getName(),["string","string"],arguments);var d=Uw(a)||{};c=Ad(d[b],this.K);return c}zL.M="internal.getProductSettingsParameter";function AL(a,b){var c;if(!kh(a)||!oh(b))throw G(this.getName(),["string","boolean|undefined"],arguments);I(this,"get_url","query",a);var d=Kk(Qk(x.location.href),"query"),e=Hk(d,a,b);c=Ad(e,this.K);return c}AL.publicName="getQueryParameters";function BL(a,b){var c;return c}BL.publicName="getReferrerQueryParameters";function CL(a){var b="";return b}CL.publicName="getReferrerUrl";function DL(){return ho()}DL.M="internal.getRegionCode";function EL(a,b){var c;if(!kh(a)||!kh(b))throw G(this.getName(),["string","string"],arguments);var d=Gq(a);c=Ad(d[b],this.K);return c}EL.M="internal.getRemoteConfigParameter";function FL(){var a=new Ua;a.set("width",0);a.set("height",0);return a}FL.M="internal.getScreenDimensions";function GL(){var a="";return a}GL.M="internal.getTopSameDomainUrl";function HL(){var a="";return a}HL.M="internal.getTopWindowUrl";function IL(a){var b="";if(!lh(a))throw G(this.getName(),["string|undefined"],arguments);I(this,"get_url",a);b=Kk(Qk(x.location.href),a);return b}IL.publicName="getUrl";function JL(){I(this,"get_user_agent");return rc.userAgent}JL.M="internal.getUserAgent";function KL(){var a;return a?Ad(Py(a)):a}KL.M="internal.getUserAgentClientHints";var ML=function(a){var b=a.eventName===J.m.bd&&Ym()&&gy(a),c=P(a,O.A.Al),d=P(a,O.A.Vj),e=P(a,O.A.If),f=P(a,O.A.ne),g=P(a,O.A.xg),h=P(a,O.A.Od),m=P(a,O.A.yg),n=P(a,O.A.zg),p=!!fy(a)||!!P(a,O.A.Sh);return!(!Uc()&&rc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&LL)},LL=!1;
var NL=function(a){var b=0,c=0;return{start:function(){b=zb()},stop:function(){c=this.get()},get:function(){var d=0;a.sj()&&(d=zb()-b);return d+c}}},OL=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.N=!1;this.R=this.P=void 0};k=OL.prototype;k.po=function(a){var b=this;if(!this.C){this.N=A.hasFocus();this.isVisible=!A.hidden;this.isActive=!0;var c=function(e,f,g){Ic(e,f,function(h){b.C.stop();g(h);b.sj()&&b.C.start()})},d=x;c(d,"focus",function(){b.N=!0});c(d,"blur",function(){b.N=
!1});c(d,"pageshow",function(e){b.isActive=!0;e.persisted&&L(56);b.R&&b.R()});c(d,"pagehide",function(){b.isActive=!1;b.P&&b.P()});c(A,"visibilitychange",function(){b.isVisible=!A.hidden});gy(a)&&!xc()&&c(d,"beforeunload",function(){LL=!0});this.Lj(!0);this.H=0}};k.Lj=function(a){if((a===void 0?0:a)||this.C)this.H+=this.Bh(),this.C=NL(this),this.sj()&&this.C.start()};k.Jq=function(a){var b=this.Bh();b>0&&R(a,J.m.Lg,b)};k.Ip=function(a){R(a,J.m.Lg);this.Lj();this.H=0};k.sj=function(){return this.N&&
this.isVisible&&this.isActive};k.yp=function(){return this.H+this.Bh()};k.Bh=function(){return this.C&&this.C.get()||0};k.qq=function(a){this.P=a};k.Hm=function(a){this.R=a};var PL=function(a){db("GA4_EVENT",a)};var QL=function(a){var b=P(a,O.A.nl);if(Array.isArray(b))for(var c=0;c<b.length;c++)PL(b[c]);var d=gb("GA4_EVENT");d&&R(a,"_eu",d)},RL=function(){delete cb.GA4_EVENT};function SL(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function TL(){var a=SL();a.hid=a.hid||pb();return a.hid}function UL(a,b){var c=SL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var VL=["GA1"];
var WL=function(a,b,c){var d=P(a,O.A.Xj);if(d===void 0||c<=d)R(a,J.m.Rb,b),Q(a,O.A.Xj,c)},YL=function(a,b){var c=Mv(a,J.m.Rb);if(N(a.D,J.m.Nc)&&N(a.D,J.m.Mc)||b&&c===b)return c;if(c){c=""+c;if(!XL(c,a))return L(31),a.isAborted=!0,"";UL(c,ip(J.m.ia));return c}L(32);a.isAborted=!0;return""},ZL=function(a){var b=P(a,O.A.ya),c=b.prefix+"_ga",d=As(b.prefix+"_ga",b.domain,b.path,VL,J.m.ia);if(!d){var e=String(N(a.D,J.m.gd,""));e&&e!==c&&(d=As(e,b.domain,b.path,VL,J.m.ia))}return d},XL=function(a,b){var c;
var d=P(b,O.A.ya),e=d.prefix+"_ga",f=Mr(d,void 0,void 0,J.m.ia);if(N(b.D,J.m.Jc)===!1&&ZL(b)===a)c=!0;else{var g;g=[VL[0],xs(d.domain,d.path),a].join(".");c=ss(e,g,f)!==1}return c};
var $L=function(a){if(a){var b;a:{var c=(Eb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=Bt(c,2);break a}catch(d){}b=void 0}return b}},bM=function(a,b){var c;a:{var d=aM,e=At[2];if(e){var f,g=vs(b.domain),h=ws(b.path),m=Object.keys(e.Kh),n=Et.get(2),p;if(f=(p=ks(a,g,h,m,n))==null?void 0:p.Vo){var q=Bt(f,2,d);c=q?Gt(q):void 0;break a}}c=void 0}if(c){var r=Ft(a,2,aM);if(r&&r.length>1){PL(28);var t;if(r&&r.length!==0){for(var v,u=-Infinity,w=l(r),y=w.next();!y.done;y=w.next()){var z=y.value;
if(z.t!==void 0){var C=Number(z.t);!isNaN(C)&&C>u&&(u=C,v=z)}}t=v}else t=void 0;var D=t;D&&D.t!==c.t&&(PL(32),c=D)}return Dt(c,2)}},aM=function(a){a&&(a==="GS1"?PL(33):a==="GS2"&&PL(34))},cM=function(a){var b=$L(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||PL(29);d||PL(30);isNaN(e)&&PL(31);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",m.h=g,m}}};

var eM=function(a,b,c){if(!b)return a;if(!a)return b;var d=cM(a);if(!d)return b;var e,f=ub((e=N(c.D,J.m.rf))!=null?e:30),g=P(c,O.A.hb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=cM(b);if(!h)return a;h.o=d.o+1;var m;return(m=dM(h))!=null?m:b},gM=function(a,b){var c=P(b,O.A.ya),d=fM(b,c),e=$L(a);if(!e)return!1;var f=Mr(c||{},void 0,void 0,Et.get(2));ss(d,void 0,f);return Ht(d,e,2,c)!==1},hM=function(a){var b=P(a,O.A.ya);return bM(fM(a,b),b)},iM=function(a){var b=P(a,O.A.hb),c={};c.s=Mv(a,J.m.wc);
c.o=Mv(a,J.m.Yg);var d;d=Mv(a,J.m.Xg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=P(a,O.A.oe),c.j=P(a,O.A.Kf)||0,c.l=!!P(a,J.m.fi),c.h=Mv(a,J.m.Mg),c);return dM(e)},dM=function(a){if(a.s&&a.o){var b={},c=(b.s=a.s,b.o=String(a.o),b.g=ub(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Dt(c,2)}},fM=function(a,b){return b.prefix+"_ga_"+a.target.ids[Fp[6]]};
var jM=function(a){var b=N(a.D,J.m.Pa),c=a.D.H[J.m.Pa];if(c===b)return c;var d=kd(b,null);c&&c[J.m.ma]&&(d[J.m.ma]=(d[J.m.ma]||[]).concat(c[J.m.ma]));return d},kM=function(a,b){var c=Ts(!0);return c._up!=="1"?{}:{clientId:c[a],wb:c[b]}},lM=function(a,b,c){var d=Ts(!0),e=d[b];e&&(WL(a,e,2),XL(e,a));var f=d[c];f&&gM(f,a);return{clientId:e,wb:f}},mM=function(){var a=Mk(x.location,"host"),b=Mk(Qk(A.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},nM=function(a){if(!N(a.D,
J.m.Fb))return{};var b=P(a,O.A.ya),c=b.prefix+"_ga",d=fM(a,b);at(function(){var e;if(ip("analytics_storage"))e={};else{var f={_up:"1"},g;g=Mv(a,J.m.Rb);e=(f[c]=g,f[d]=iM(a),f)}return e},1);return!ip("analytics_storage")&&mM()?kM(c,d):{}},pM=function(a){var b=jM(a)||{},c=P(a,O.A.ya),d=c.prefix+"_ga",e=fM(a,c),f={};ct(b[J.m.de],!!b[J.m.ma])&&(f=lM(a,d,e),f.clientId&&f.wb&&(oM=!0));b[J.m.ma]&&$s(function(){var g={},h=ZL(a);h&&(g[d]=h);var m=hM(a);m&&(g[e]=m);var n=hs("FPLC",void 0,void 0,J.m.ia);n.length&&
(g._fplc=n[0]);return g},b[J.m.ma],b[J.m.Oc],!!b[J.m.vc]);return f},oM=!1;var qM=function(a){if(!P(a,O.A.xd)&&Yk(a.D)){var b=jM(a)||{},c=(ct(b[J.m.de],!!b[J.m.ma])?Ts(!0)._fplc:void 0)||(hs("FPLC",void 0,void 0,J.m.ia).length>0?void 0:"0");R(a,"_fplc",c)}};function rM(a){(gy(a)||kk())&&R(a,J.m.fl,ho()||go());!gy(a)&&kk()&&R(a,J.m.tl,"::")}function sM(a){if(kk()&&!gy(a)){var b=E(176);E(187)&&E(201)&&(b=b&&!ko());b&&R(a,J.m.Qk,!0);if(E(78)){$v(a);aw(a,Ap.Ef.gn,Eo(N(a.D,J.m.eb)));var c=Ap.Ef.hn;var d=N(a.D,J.m.Jc);aw(a,c,d===!0?1:d===!1?0:void 0);aw(a,Ap.Ef.fn,Eo(N(a.D,J.m.zb)));aw(a,Ap.Ef.bn,xs(Do(N(a.D,J.m.ob)),Do(N(a.D,J.m.Tb))))}}};var uM=function(a,b){tp("grl",function(){return tM()})(b)||(L(35),a.isAborted=!0)},tM=function(){var a=zb(),b=a+864E5,c=20,d=5E3;return function(e){var f=zb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.ap=d,e.Po=c);return g}};
var vM=function(a){var b=Mv(a,J.m.Wa);return Kk(Qk(b),"host",!0)},wM=function(a){if(N(a.D,J.m.lf)!==void 0)a.copyToHitData(J.m.lf);else{var b=N(a.D,J.m.ki),c,d;a:{if(oM){var e=jM(a)||{};if(e&&e[J.m.ma])for(var f=vM(a),g=e[J.m.ma],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=vM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(R(a,J.m.lf,"1"),
PL(4))}};
var xM=function(a,b){ur()&&(a.gcs=vr(),P(b,O.A.Hf)&&(a.gcu="1"));a.gcd=zr(b.D);E(97)?a.npa=P(b,O.A.Lh)?"0":"1":tr(b.D)?a.npa="0":a.npa="1";Er()&&(a._ng="1")},yM=function(a){return ip(J.m.U)&&ip(J.m.ia)?kk()&&P(a,O.A.Hi):!1},zM=function(a){if(P(a,O.A.xd))return{url:Zk("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=Vk(Yk(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=hy(a),d=N(a.D,J.m.Pb),e=c&&!io()&&d!==!1&&$J()&&ip(J.m.U)&&ip(J.m.ia)?17:16;return{url:Lz(e),
endpoint:e}},AM={};AM[J.m.Rb]="cid";AM[J.m.Uh]="gcut";AM[J.m.fd]="are";AM[J.m.Jg]="pscdl";AM[J.m.gi]="_fid";AM[J.m.Mk]="_geo";AM[J.m.Vb]="gdid";AM[J.m.be]="_ng";AM[J.m.Lc]="frm";AM[J.m.lf]="ir";AM[J.m.Qk]="fp";AM[J.m.Ab]="ul";AM[J.m.Vg]="ni";AM[J.m.Yn]="pae";AM[J.m.Wg]="_rdi";AM[J.m.Pc]="sr";AM[J.m.co]="tid";AM[J.m.ri]="tt";AM[J.m.yc]="ec_mode";AM[J.m.yl]="gtm_up";AM[J.m.vf]=
"uaa";AM[J.m.wf]="uab";AM[J.m.xf]="uafvl";AM[J.m.yf]="uamb";AM[J.m.zf]="uam";AM[J.m.Af]="uap";AM[J.m.Bf]="uapv";AM[J.m.Cf]="uaw";AM[J.m.fl]="ur";AM[J.m.tl]="_uip";AM[J.m.Xn]="_prs";AM[J.m.md]="lps";AM[J.m.Ud]="gclgs";AM[J.m.Wd]="gclst";AM[J.m.Vd]="gcllp";var BM={};BM[J.m.Se]="cc";
BM[J.m.Te]="ci";BM[J.m.Ue]="cm";BM[J.m.Ve]="cn";BM[J.m.Xe]="cs";BM[J.m.Ye]="ck";BM[J.m.Va]="cu";BM[J.m.kf]="_tu";BM[J.m.Aa]="dl";BM[J.m.Wa]="dr";BM[J.m.Eb]="dt";BM[J.m.Xg]="seg";BM[J.m.wc]="sid";BM[J.m.Yg]="sct";BM[J.m.Qa]="uid";E(145)&&(BM[J.m.pf]="dp");var CM={};CM[J.m.Lg]="_et";CM[J.m.Ub]="edid";E(94)&&(CM._eu="_eu");var DM={};DM[J.m.Se]="cc";DM[J.m.Te]="ci";
DM[J.m.Ue]="cm";DM[J.m.Ve]="cn";DM[J.m.Xe]="cs";DM[J.m.Ye]="ck";var EM={},FM=(EM[J.m.fb]=1,EM),GM=function(a,b,c){function d(T,Z){if(Z!==void 0&&!oo.hasOwnProperty(T)){Z===null&&(Z="");var Y;var V=Z;T!==J.m.Mg?Y=!1:P(a,O.A.ie)||gy(a)?(e.ecid=V,Y=!0):Y=void 0;if(!Y&&T!==J.m.fi){var ka=Z;Z===!0&&(ka="1");Z===!1&&(ka="0");ka=String(ka);var ia;if(AM[T])ia=AM[T],e[ia]=ka;else if(BM[T])ia=BM[T],g[ia]=ka;else if(CM[T])ia=CM[T],f[ia]=ka;else if(T.charAt(0)==="_")e[T]=ka;else{var la;DM[T]?la=!0:T!==J.m.We?
la=!1:(typeof Z!=="object"&&C(T,Z),la=!0);la||C(T,Z)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;e.gtm=Kr({Ma:P(a,O.A.ib)});e._p=E(159)?dk:TL();if(c&&(c.Za||c.nj)&&(E(125)||(e.em=c.Lb),c.Ib)){var h=c.Ib.ze;h&&!E(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}P(a,O.A.Od)&&(e._gaz=1);xM(e,a);Cr()&&(e.dma_cps=Ar());e.dma=Br();Yq(fr())&&(e.tcfd=Dr());Mz()&&(e.tag_exp=Mz());Nz()&&(e.ptag_exp=Nz());var m=Mv(a,J.m.Vb);m&&(e.gdid=m);f.en=String(a.eventName);if(P(a,O.A.Jf)){var n=P(a,O.A.wl);f._fv=
n?2:1}P(a,O.A.ih)&&(f._nsi=1);if(P(a,O.A.ne)){var p=P(a,O.A.zl);f._ss=p?2:1}P(a,O.A.If)&&(f._c=1);P(a,O.A.wd)&&(f._ee=1);if(P(a,O.A.vl)){var q=Mv(a,J.m.sa)||N(a.D,J.m.sa);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+(r+1)]=sg(q[r])}var t=Mv(a,J.m.Ub);t&&(f.edid=t);var v=Mv(a,J.m.uc);if(v&&typeof v==="object")for(var u=l(Object.keys(v)),w=u.next();!w.done;w=u.next()){var y=w.value,z=v[y];z!==void 0&&(z===null&&(z=""),f["gap."+y]=String(z))}for(var C=function(T,Z){if(typeof Z!=="object"||
!FM[T]){var Y="ep."+T,V="epn."+T;T=mb(Z)?V:Y;var ka=mb(Z)?Y:V;f.hasOwnProperty(ka)&&delete f[ka];f[T]=String(Z)}},D=l(Object.keys(a.C)),F=D.next();!F.done;F=D.next()){var H=F.value;d(H,Mv(a,H))}(function(T){gy(a)&&typeof T==="object"&&sb(T||{},function(Z,Y){typeof Y!=="object"&&(e["sst."+Z]=String(Y))})})(Mv(a,J.m.Oi));Oz(e,Mv(a,J.m.ud));var M=Mv(a,J.m.Xb)||{};N(a.D,J.m.Pb,void 0,4)===!1&&(e.ngs="1");sb(M,function(T,Z){Z!==void 0&&((Z===null&&(Z=""),T!==J.m.Qa||g.uid)?b[T]!==Z&&(f[(mb(Z)?"upn.":"up.")+
String(T)]=String(Z),b[T]=Z):g.uid=String(Z))});if(E(176)){var S=E(187)&&ko();if(kk()&&!S){var ca=P(a,O.A.oe);ca?e._gsid=ca:e.njid="1"}}else if(yM(a)){var U=P(a,O.A.oe);U?e._gsid=U:e.njid="1"}var oa=zM(a);Fg.call(this,{ra:e,Md:g,ij:f},oa.url,oa.endpoint,gy(a),void 0,a.target.destinationId,a.D.eventId,a.D.priorityId)};ra(GM,Fg);
var HM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},IM=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;if(E(186)){var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e}return b},JM=function(a,b,c,d,e){var f=0,g=new x.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;
EA(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},LM=function(a,b,c){var d;return d=HA(GA(new FA(function(e,f){var g=HM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");em(a,g,void 0,JA(d,f),h)}),function(e,f){var g=HM(e,b),h=f.dedupe_key;h&&jm(a,g,h)}),function(e,
f){var g=HM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?KM(a,g,void 0,d,h,JA(d,f)):fm(a,g,void 0,h,void 0,JA(d,f))})},MM=function(a,b,c,d,e){Zl(a,2,b);var f=LM(a,d,e);KM(a,b,c,f)},KM=function(a,b,c,d,e,f){Uc()?DA(a,b,c,d,e,void 0,f):JM(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},NM=function(a,b,c){var d=Qk(b),e=IM(d),f=LA(d);!E(132)||wc("; wv")||
wc("FBAN")||wc("FBAV")||yc()?MM(a,f,c,e):Hy(f,c,e,function(g){MM(a,f,c,e,g)})};var OM={AW:nn.Z.Wm,G:nn.Z.io,DC:nn.Z.fo};function PM(a){var b=$i(a);return""+$r(b.map(function(c){return c.value}).join("!"))}function QM(a){var b=Dp(a);return b&&OM[b.prefix]}function RM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};
var SM=function(a,b,c,d){var e=a+"?"+b;d?dm(c,e,d):cm(c,e)},UM=function(a,b,c,d,e){var f=b,g=Xc();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;TM&&(d=!Eb(h,Kz())&&!Eb(h,Jz()));if(d&&!LL)NM(e,h,c);else{var m=b;Uc()?fm(e,a+"?"+m,c,{Hh:!0})||SM(a,m,e,c):SM(a,m,e,c)}},VM=function(a,b){function c(w){q.push(w+"="+encodeURIComponent(""+a.ra[w]))}var d=b.xq,e=b.Aq,f=b.zq,g=b.yq,h=b.Ap,m=b.Tp,n=b.Sp,p=b.qp;if(d||e||f||g){var q=[];a.ra._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.Md.uid&&
!n&&q.push("uid="+encodeURIComponent(""+a.Md.uid));c("dma");a.ra.dma_cps!=null&&c("dma_cps");a.ra.gcs!=null&&c("gcs");c("gcd");a.ra.npa!=null&&c("npa");a.ra.frm!=null&&c("frm");d&&(Mz()&&q.push("tag_exp="+Mz()),Nz()&&q.push("ptag_exp="+Nz()),SM("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),Vo({targetId:String(a.ra.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+q.join("&"),
parameterEncoding:2,endpoint:19},Ya:b.Ya}));if(e&&(Mz()&&q.push("tag_exp="+Mz()),Nz()&&q.push("ptag_exp="+Nz()),q.push("z="+pb()),!m)){var r=h&&Eb(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(r){var t=r+q.join("&");em({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);Vo({targetId:String(a.ra.tid),request:{url:t,parameterEncoding:2,endpoint:47},Ya:b.Ya})}}if(f){var v="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");q=[];c("_gsid");c("gtm");E(176)&&a.ra._geo&&c("_geo");SM(v,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});Vo({targetId:String(a.ra.tid),request:{url:v+"?"+q.join("&"),parameterEncoding:2,endpoint:18},Ya:b.Ya})}if(g){var u="https://{ga4CollectionSubdomain.}google-analytics.com/g/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");q=[];q.push("v=2");q.push("t=g");c("_gsid");c("gtm");a.ra._geo&&c("_geo");SM(u,q.join("&"),{destinationId:a.destinationId||
"",endpoint:16,eventId:a.eventId,priorityId:a.priorityId});Vo({targetId:String(a.ra.tid),request:{url:u+"?"+q.join("&"),parameterEncoding:2,endpoint:16},Ya:b.Ya})}}},TM=!1;var WM=function(){this.N=1;this.P={};this.H=-1;this.C=new yg};k=WM.prototype;k.Nb=function(a,b){var c=this,d=new GM(a,this.P,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=ML(a),g,
h;f&&this.C.R(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=x,p=n.setTimeout,q;gy(a)?XM?(XM=!1,q=YM):q=ZM:q=5E3;this.H=p.call(n,function(){c.flush()},q)}}else{var r=Bg(d,this.N++),t=r.params,v=r.body;g=t;h=v;UM(d.baseUrl,t,v,d.N,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var u=P(a,O.A.xg),w=P(a,O.A.Od),y=P(a,O.A.zg),z=P(a,O.A.yg),C=N(a.D,J.m.nb)!==!1,D=tr(a.D),F={xq:u,Aq:w,zq:y,yq:z,Ap:mo(),Br:C,Ar:D,Tp:io(),Sp:P(a,O.A.ie),
Ya:e,D:a.D,qp:ko()};VM(d,F)}sA(a.D.eventId);Wo(function(){if(m){var H=Bg(d),M=H.body;g=H.params;h=M}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},Ya:e,isBatched:!1}})};k.add=function(a){if(E(100)){var b=P(a,O.A.Sh);if(b){R(a,J.m.yc,P(a,O.A.Xl));R(a,J.m.Vg,"1");this.Nb(a,b);return}}var c=fy(a);if(E(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=QM(e);if(h){var m=PM(g);f=(rn(h)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:
void 0;if(d&&d+6E4>zb())c=void 0,R(a,J.m.yc);else{var p=c,q=a.target.destinationId,r=QM(q);if(r){var t=PM(p),v=rn(r)||{},u=v[t];if(u)u.timestamp=zb(),u.sentTo=u.sentTo||{},u.sentTo[q]=zb(),u.pending=!0;else{var w={};v[t]={pending:!0,timestamp:zb(),sentTo:(w[q]=zb(),w)}}RM(v,t);qn(r,v)}}}!c||LL||E(125)&&!E(93)?this.Nb(a):this.Bq(a)};k.flush=function(){if(this.C.events.length){var a=Dg(this.C,this.N++);UM(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",endpoint:this.C.endpoint,
eventId:this.C.ba,priorityId:this.C.ka});this.C=new yg;this.H>=0&&(x.clearTimeout(this.H),this.H=-1)}};k.im=function(a,b){var c=Mv(a,J.m.yc);R(a,J.m.yc);b.then(function(d){var e={},f=(e[O.A.Sh]=d,e[O.A.Xl]=c,e),g=Kw(a.target.destinationId,J.m.Td,a.D.C);Nw(g,a.D.eventId,{eventMetadata:f})})};k.Bq=function(a){var b=this,c=fy(a);if(yj(c)){var d=nj(c,E(93));d?E(100)?(this.im(a,d),this.Nb(a)):d.then(function(g){b.Nb(a,g)},function(){b.Nb(a)}):this.Nb(a)}else{var e=xj(c);if(E(93)){var f=ij(e);f?E(100)?
(this.im(a,f),this.Nb(a)):f.then(function(g){b.Nb(a,g)},function(){b.Nb(a,e)}):this.Nb(a,e)}else this.Nb(a,e)}};var YM=tg('',500),ZM=tg('',5E3),XM=!0;
var $M=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;$M(a+"."+f,b[f],c)}else c[a]=b;return c},aN=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!ip(e)}return b},cN=function(a,b){var c=bN.filter(function(e){return!ip(e)});if(c.length){var d=aN(c);jp(c,function(){for(var e=aN(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){Q(b,O.A.Hf,!0);var n=f.map(function(p){return yo[p]}).join(".");n&&dy(b,"gcut",n);a(b)}})}},dN=function(a){gy(a)&&dy(a,"navt",Yc())},eN=function(a){gy(a)&&dy(a,"lpc",Ot())},fN=function(a){if(E(152)&&gy(a)){var b=N(a.D,J.m.Wb),c;b===!0&&(c="1");b===!1&&(c="0");c&&dy(a,"rdp",c)}},gN=function(a){E(147)&&gy(a)&&N(a.D,J.m.Re,!0)===!1&&R(a,J.m.Re,0)},hN=function(a,b){if(gy(b)){var c=P(b,O.A.If);(b.eventName==="page_view"||c)&&cN(a,b)}},iN=function(a){if(gy(a)&&a.eventName===J.m.Td&&
P(a,O.A.Hf)){var b=Mv(a,J.m.Uh);b&&(dy(a,"gcut",b),dy(a,"syn",1))}},jN=function(a){gy(a)&&Q(a,O.A.Ha,!1)},kN=function(a){gy(a)&&(P(a,O.A.Ha)&&dy(a,"sp",1),P(a,O.A.no)&&dy(a,"syn",1),P(a,O.A.Le)&&(dy(a,"em_event",1),dy(a,"sp",1)))},lN=function(a){if(gy(a)){var b=dk;b&&dy(a,"tft",Number(b))}},mN=function(a){function b(e){var f=$M(J.m.fb,e);sb(f,function(g,h){R(a,g,h)})}if(gy(a)){var c=fw(a,"ccd_add_1p_data",!1)?1:0;dy(a,"ude",c);var d=N(a.D,J.m.fb);d!==void 0?(b(d),R(a,J.m.yc,"c")):b(P(a,O.A.jb));Q(a,
O.A.jb)}},nN=function(a){if(gy(a)){var b=Jv();b&&dy(a,"us_privacy",b);var c=mr();c&&dy(a,"gdpr",c);var d=lr();d&&dy(a,"gdpr_consent",d);var e=wv.gppString;e&&dy(a,"gpp",e);var f=wv.C;f&&dy(a,"gpp_sid",f)}},oN=function(a){gy(a)&&Ym()&&N(a.D,J.m.za)&&dy(a,"adr",1)},pN=function(a){if(gy(a)){var b=Iz();b&&dy(a,"gcsub",b)}},qN=function(a){if(gy(a)){N(a.D,J.m.Pb,void 0,4)===!1&&dy(a,"ngs",1);io()&&dy(a,"ga_rd",1);$J()||dy(a,"ngst",1);var b=mo();b&&dy(a,"etld",b)}},rN=function(a){},sN=function(a){gy(a)&&Ym()&&dy(a,"rnd",jv())},bN=[J.m.U,J.m.V];
var tN=function(a,b){var c;a:{var d=iM(a);if(d){if(gM(d,a)){c=d;break a}L(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:YL(a,b),wb:e}},uN=function(a,b,c,d,e){var f=Do(N(a.D,J.m.Rb));if(N(a.D,J.m.Nc)&&N(a.D,J.m.Mc))f?WL(a,f,1):(L(127),a.isAborted=!0);else{var g=f?1:8;Q(a,O.A.ih,!1);f||(f=ZL(a),g=3);f||(f=b,g=5);if(!f){var h=ip(J.m.ia),m=SL();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=zs(),g=7,Q(a,O.A.Jf,!0),Q(a,O.A.ih,!0));WL(a,f,g)}var n=P(a,O.A.hb),p=Math.floor(n/1E3),q=void 0;P(a,O.A.ih)||
(q=hM(a)||c);var r=ub(N(a.D,J.m.rf,30));r=Math.min(475,r);r=Math.max(5,r);var t=ub(N(a.D,J.m.mi,1E4)),v=cM(q);Q(a,O.A.Jf,!1);Q(a,O.A.ne,!1);Q(a,O.A.Kf,0);v&&v.j&&Q(a,O.A.Kf,Math.max(0,v.j-Math.max(0,p-v.t)));var u=!1;if(!v){Q(a,O.A.Jf,!0);u=!0;var w={};v=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>v.t+r*60&&(u=!0,v.s=String(p),v.o++,v.g=!1,v.h=void 0);if(u)Q(a,O.A.ne,!0),d.Ip(a);else if(d.yp()>t||a.eventName===J.m.bd)v.g=!0;P(a,O.A.ie)?N(a.D,J.m.Qa)?v.l=!0:(v.l&&!E(9)&&(v.h=void 0),v.l=
!1):v.l=!1;var y=v.h;if(P(a,O.A.ie)||gy(a)){var z=N(a.D,J.m.Mg),C=z?1:8;z||(z=y,C=4);z||(z=ys(),C=7);var D=z.toString(),F=C,H=P(a,O.A.lk);if(H===void 0||F<=H)R(a,J.m.Mg,D),Q(a,O.A.lk,F)}e?(a.copyToHitData(J.m.wc,v.s),a.copyToHitData(J.m.Yg,v.o),a.copyToHitData(J.m.Xg,v.g?1:0)):(R(a,J.m.wc,v.s),R(a,J.m.Yg,v.o),R(a,J.m.Xg,v.g?1:0));Q(a,J.m.fi,v.l?1:0);kk()&&Q(a,O.A.oe,v.d||Nb())};var vN=window,wN=document,xN=function(a){var b=vN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||wN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&vN["ga-disable-"+a]===!0)return!0;try{var c=vN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(wN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return wN.getElementById("__gaOptOutExtension")?!0:!1};
var zN=function(a){return!a||yN.test(a)||qo.hasOwnProperty(a)},AN=function(a){var b=J.m.Pc,c;c||(c=function(){});Mv(a,b)!==void 0&&R(a,b,c(Mv(a,b)))},BN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=Jk(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},CN=function(a){N(a.D,J.m.Fb)&&(ip(J.m.ia)||N(a.D,J.m.Rb)||R(a,J.m.yl,!0));var b;var c;c=c===void 0?3:c;var d=x.location.href;if(d){var e=Qk(d).search.replace("?",""),f=Hk(e,"_gl",!1,!0)||"";b=f?Us(f,c)!==void 0:!1}else b=!1;b&&gy(a)&&
dy(a,"glv",1);if(a.eventName!==J.m.qa)return{};N(a.D,J.m.Fb)&&Nu(["aw","dc"]);Pu(["aw","dc"]);var g=pM(a),h=nM(a);return Object.keys(g).length?g:h},DN=function(a){var b=Ib(a.D.getMergedValues(J.m.oa,1,Bo(Dq.C[J.m.oa])),".");b&&R(a,J.m.Vb,b);var c=Ib(a.D.getMergedValues(J.m.oa,2),".");c&&R(a,J.m.Ub,c)},EN={np:""},FN={},GN=(FN[J.m.Se]=1,FN[J.m.Te]=1,FN[J.m.Ue]=1,FN[J.m.Ve]=1,FN[J.m.Xe]=1,FN[J.m.Ye]=1,FN),yN=/^(_|ga_|google_|gtag\.|firebase_).*$/,HN=[ew,
bw,Ov,gw,DN,Ew],IN=function(a){this.N=a;this.C=this.wb=this.clientId=void 0;this.Ba=this.R=!1;this.rb=0;this.P=!1;this.Sa=!0;this.ba={qj:!1};this.ka=new WM;this.H=new OL};k=IN.prototype;k.mq=function(a,b,c){var d=this,e=Dp(this.N);if(e)if(c.eventMetadata[O.A.wd]&&a.charAt(0)==="_")c.onFailure();else{a!==J.m.qa&&a!==J.m.Db&&zN(a)&&L(58);JN(c.C);var f=new DH(e,a,c);Q(f,O.A.hb,b);var g=[J.m.ia],h=gy(f);Q(f,O.A.jh,h);if(fw(f,J.m.ce,N(f.D,J.m.ce))||h)g.push(J.m.U),g.push(J.m.V);Ry(function(){lp(function(){d.nq(f)},
g)});E(88)&&a===J.m.qa&&fw(f,"ga4_ads_linked",!1)&&kn(mn(Mm.X.Da),function(){d.kq(a,c,f)})}else c.onFailure()};k.kq=function(a,b,c){function d(){for(var h=l(HN),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}P(f,O.A.Ha)||f.isAborted||Uz(f)}var e=Dp(this.N),f=new DH(e,a,b);Q(f,O.A.fa,K.J.Ga);Q(f,O.A.Ha,!0);Q(f,O.A.jh,P(c,O.A.jh));var g=[J.m.U,J.m.V];lp(function(){d();ip(g)||kp(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;Q(f,O.A.ja,!0);Q(f,O.A.Ie,m);Q(f,O.A.Je,
n);d()},g)},g)};k.nq=function(a){var b=this;try{ew(a);if(a.isAborted){RL();return}E(165)||(this.C=a);KN(a);LN(a);MN(a);NN(a);E(138)&&(a.isAborted=!0);Wv(a);var c={};uM(a,c);if(a.isAborted){a.D.onFailure();RL();return}E(165)&&(this.C=a);var d=c.Po;c.ap===0&&PL(25);d===0&&PL(26);gw(a);Q(a,O.A.Qf,Mm.X.Hc);ON(a);PN(a);this.qo(a);this.H.Jq(a);QN(a);RN(a);SN(a);TN(a);this.Gm(CN(a));var e=a.eventName===J.m.qa;e&&(this.P=!0);UN(a);e&&!a.isAborted&&this.rb++>0&&PL(17);VN(a);WN(a);uN(a,this.clientId,this.wb,
this.H,!this.Ba);XN(a);YN(a);ZN(a);$N(a,this.ba);this.Sa=aO(a,this.Sa);bO(a);cO(a);dO(a);eO(a);fO(a);qM(a);wM(a);sN(a);rN(a);qN(a);pN(a);oN(a);nN(a);lN(a);kN(a);iN(a);gN(a);fN(a);eN(a);dN(a);rM(a);sM(a);gO(a);hO(a);iO(a);Yv(a);Xv(a);dw(a);jO(a);kO(a);Ew(a);lO(a);mN(a);jN(a);mO(a);!this.P&&P(a,O.A.Le)&&PL(18);QL(a);if(P(a,O.A.Ha)||a.isAborted){a.D.onFailure();RL();return}this.Gm(tN(a,this.clientId));this.Ba=!0;this.Gq(a);nO(a);hN(function(f){b.Yl(f)},a);this.H.Lj();oO(a);cw(a);if(a.isAborted){a.D.onFailure();
RL();return}this.Yl(a);a.D.onSuccess()}catch(f){a.D.onFailure()}RL()};k.Yl=function(a){this.ka.add(a)};k.Gm=function(a){var b=a.clientId,c=a.wb;b&&c&&(this.clientId=b,this.wb=c)};k.flush=function(){this.ka.flush()};k.Gq=function(a){var b=this;if(!this.R){var c=ip(J.m.V),d=ip(J.m.ia),e=[J.m.V,J.m.ia];E(213)&&e.push(J.m.U);jp(e,function(){var f=ip(J.m.V),g=ip(J.m.ia),h=!1,m={},n={};if(d!==g&&b.C&&b.wb&&b.clientId){var p=b.clientId,q;var r=cM(b.wb);q=r?r.h:void 0;if(g){var t=ZL(b.C);if(t){b.clientId=
t;var v=hM(b.C);v&&(b.wb=eM(v,b.wb,b.C))}else XL(b.clientId,b.C),UL(b.clientId,!0);gM(b.wb,b.C);h=!0;m[J.m.ii]=p;E(69)&&q&&(m[J.m.Sn]=q)}else b.wb=void 0,b.clientId=void 0,x.gaGlobal={}}f&&!c&&(h=!0,n[O.A.Hf]=!0,m[J.m.Uh]=yo[J.m.V]);if(h){var u=Kw(b.N,J.m.Td,m);Nw(u,a.D.eventId,{eventMetadata:n})}d=g;c=f;b.ba.qj=!0});this.R=!0}};k.qo=function(a){a.eventName!==J.m.Db&&this.H.po(a)};var MN=function(a){var b=A.location.protocol;b!=="http:"&&b!=="https:"&&(L(29),a.isAborted=!0)},NN=function(a){rc&&rc.loadPurpose===
"preview"&&(L(30),a.isAborted=!0)},ON=function(a){var b={prefix:String(N(a.D,J.m.eb,"")),path:String(N(a.D,J.m.Tb,"/")),flags:String(N(a.D,J.m.zb,"")),domain:String(N(a.D,J.m.ob,"auto")),Dc:Number(N(a.D,J.m.pb,63072E3))};Q(a,O.A.ya,b)},QN=function(a){P(a,O.A.xd)?Q(a,O.A.ie,!1):fw(a,"ccd_add_ec_stitching",!1)&&Q(a,O.A.ie,!0)},RN=function(a){if(fw(a,"ccd_add_1p_data",!1)){var b=a.D.H[J.m.Zg];if(Ck(b)){var c=N(a.D,J.m.fb);if(c===null)Q(a,O.A.ve,null);else if(b.enable_code&&jd(c)&&Q(a,O.A.ve,c),jd(b.selectors)&&
!P(a,O.A.rh)){var d={};Q(a,O.A.rh,Ak(b.selectors,d));E(60)&&a.mergeHitDataForKey(J.m.uc,{ec_data_layer:xk(d)})}}}},SN=function(a){if(E(91)&&!E(88)&&fw(a,"ga4_ads_linked",!1)&&a.eventName===J.m.qa){var b=N(a.D,J.m.Oa)!==!1;if(b){var c=Kv(a);c.Dc&&(c.Dc=Math.min(c.Dc,7776E3));Lv({ye:b,Ee:Bo(N(a.D,J.m.Pa)),He:!!N(a.D,J.m.Fb),Tc:c})}}},TN=function(a){if(E(97)){var b=tr(a.D);N(a.D,J.m.Wb)===!0&&(b=!1);Q(a,O.A.Lh,b)}},gO=function(a){if(!Ny(x))L(87);else if(Sy!==void 0){L(85);var b=Ly(x);b?N(a.D,J.m.Wg)&&
!gy(a)||Qy(b,a):L(86)}},UN=function(a){a.eventName===J.m.qa&&(N(a.D,J.m.qb,!0)?(a.D.C[J.m.oa]&&(a.D.N[J.m.oa]=a.D.C[J.m.oa],a.D.C[J.m.oa]=void 0,R(a,J.m.oa)),a.eventName=J.m.bd):a.isAborted=!0)},PN=function(a){function b(c,d){oo[c]||d===void 0||R(a,c,d)}sb(a.D.N,b);sb(a.D.C,b)},XN=function(a){var b=Vp(a.D),c=function(d,e){GN[d]&&R(a,d,e)};jd(b[J.m.We])?sb(b[J.m.We],function(d,e){c((J.m.We+"_"+d).toLowerCase(),e)}):sb(b,c)},VN=DN,nO=function(a){if(E(132)&&gy(a)&&!(wc("; wv")||wc("FBAN")||wc("FBAV")||
yc())&&ip(J.m.ia)){Q(a,O.A.Al,!0);gy(a)&&dy(a,"sw_exp",1);a:{if(!E(132)||!gy(a))break a;var b=Vk(Yk(a.D),"/_/service_worker");Ey(b);}}},jO=function(a){if(a.eventName===J.m.Db){var b=N(a.D,J.m.sc),c=N(a.D,J.m.Kc),d;d=Mv(a,b);c(d||N(a.D,b));a.isAborted=!0}},YN=function(a){if(!N(a.D,J.m.Mc)||!N(a.D,J.m.Nc)){var b=a.copyToHitData,c=J.m.Aa,d="",e=A.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f=
"/"+f);var g=e.search||"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Kb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,BN);var p=a.copyToHitData,q=J.m.Wa,r;a:{var t=hs("_opt_expid",void 0,void 0,J.m.ia)[0];if(t){var v=Jk(t);if(v){var u=v.split("$");if(u.length===3){r=u[2];break a}}}var w=sp.ga4_referrer_override;if(w!==void 0)r=w;else{var y=qk("gtm.gtagReferrer."+
a.target.destinationId),z=A.referrer;r=y?""+y:z}}p.call(a,q,r||void 0,BN);a.copyToHitData(J.m.Eb,A.title);a.copyToHitData(J.m.Ab,(rc.language||"").toLowerCase());var C=Vw();a.copyToHitData(J.m.Pc,C.width+"x"+C.height);E(145)&&a.copyToHitData(J.m.pf,void 0,BN);E(87)&&mv()&&a.copyToHitData(J.m.md,"1")}},$N=function(a,b){E(213)&&kk()&&b.qj&&(Q(a,O.A.xl,!0),Q(a,O.A.oe,Nb()),b.qj=!1)},aO=function(a,b){var c=P(a,O.A.Kf);c=c||0;var d=ip(J.m.U),e=!b&&d,f;f=E(213)?!!P(a,O.A.xl):e||!!P(a,O.A.Hf)||!!Mv(a,J.m.ii);
var g=c===0||f;Q(a,O.A.Ji,g);g&&Q(a,O.A.Kf,60);return d},bO=function(a){Q(a,O.A.xg,!1);Q(a,O.A.Od,!1);if(!gy(a)&&!P(a,O.A.xd)&&N(a.D,J.m.Pb)!==!1&&$J()&&ip(J.m.U)&&(!E(143)||ip(J.m.ia))){var b=hy(a);(P(a,O.A.ne)||N(a.D,J.m.ii))&&Q(a,O.A.xg,!!b);b&&P(a,O.A.Ji)&&P(a,O.A.Hi)&&Q(a,O.A.Od,!0)}},cO=function(a){Q(a,O.A.yg,!1);Q(a,O.A.zg,!1);if(!(E(187)&&ko()||!kk()||gy(a)||P(a,O.A.xd))&&P(a,O.A.Ji)){var b=P(a,O.A.Od);P(a,O.A.oe)&&(b?Q(a,O.A.zg,!0):E(176)&&Q(a,O.A.yg,!0))}},fO=function(a){a.copyToHitData(J.m.ri);
for(var b=N(a.D,J.m.ji)||[],c=0;c<b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(J.m.ri,d.traffic_type);PL(3);break}}},oO=function(a){a.copyToHitData(J.m.Mk);N(a.D,J.m.Wg)&&(R(a,J.m.Wg,!0),gy(a)||AN(a))},kO=function(a){a.copyToHitData(J.m.Qa);a.copyToHitData(J.m.Xb)},ZN=function(a){fw(a,"google_ng")&&!io()?a.copyToHitData(J.m.be,1):Zv(a)},mO=function(a){var b=N(a.D,J.m.Nc);b&&PL(12);P(a,O.A.Le)&&PL(14);var c=Am(pm());(b||Jm(c)||c&&c.parent&&c.context&&c.context.source===5)&&PL(19)},KN=
function(a){if(xN(a.target.destinationId))L(28),a.isAborted=!0;else if(E(144)){var b=zm();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(xN(b.destinations[c])){L(125);a.isAborted=!0;break}}},hO=function(a){Il("attribution-reporting")&&R(a,J.m.fd,"1")},LN=function(a){if(EN.np.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=ey(a);b&&b.blacklisted&&(a.isAborted=!0)}},dO=function(a){var b=function(c){return!!c&&c.conversion};Q(a,O.A.If,b(ey(a)));
P(a,O.A.Jf)&&Q(a,O.A.wl,b(ey(a,"first_visit")));P(a,O.A.ne)&&Q(a,O.A.zl,b(ey(a,"session_start")))},eO=function(a){so.hasOwnProperty(a.eventName)&&(Q(a,O.A.vl,!0),a.copyToHitData(J.m.sa),a.copyToHitData(J.m.Va))},lO=function(a){if(!gy(a)&&P(a,O.A.If)&&ip(J.m.U)&&fw(a,"ga4_ads_linked",!1)){var b=Kv(a),c=du(b.prefix),d=Fv(c);R(a,J.m.Ud,d.yh);R(a,J.m.Wd,d.Ah);R(a,J.m.Vd,d.zh)}},iO=function(a){if(E(122)){var b=ko();b&&Q(a,O.A.ho,b)}},WN=function(a){Q(a,O.A.Hi,hy(a)&&N(a.D,J.m.Pb)!==!1&&$J()&&!io())};
function JN(a){sb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Xb]||{};sb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var qO=function(a){if(!pO(a)){var b=!1,c=function(){!b&&pO(a)&&(b=!0,Jc(A,"visibilitychange",c),E(5)&&Jc(A,"prerenderingchange",c),L(55))};Ic(A,"visibilitychange",c);E(5)&&Ic(A,"prerenderingchange",c);L(54)}},pO=function(a){if(E(5)&&"prerendering"in A?A.prerendering:A.visibilityState==="prerender")return!1;a();return!0};function rO(a,b){qO(function(){var c=Dp(a);if(c){var d=sO(c,b);Cq(a,d,Mm.X.Hc)}});}function sO(a,b){var c=function(){};var d=new IN(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[O.A.xd]=!0);d.mq(g,h,m)};tO(a,d,b);return c}
function tO(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e[O.A.Vj]=!0,e),deferrable:!0};d.qq(function(){LL=!0;Dq.flush();d.Bh()>=1E3&&rc.sendBeacon!==void 0&&Eq(J.m.Td,{},a.id,f);b.flush();d.Hm(function(){LL=!1;d.Hm()})});};var uO=sO;function wO(a,b,c){var d=this;}wO.M="internal.gtagConfig";
function yO(a,b){}
yO.publicName="gtagSet";function zO(){var a={};return a};function AO(a){}AO.M="internal.initializeServiceWorker";function BO(a,b){}BO.publicName="injectHiddenIframe";var CO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function DO(a,b,c,d,e){}DO.M="internal.injectHtml";var HO={};
function JO(a,b,c,d){}var KO={dl:1,id:1},LO={};
function MO(a,b,c,d){}E(160)?MO.publicName="injectScript":JO.publicName="injectScript";MO.M="internal.injectScript";function NO(){return lo()}NO.M="internal.isAutoPiiEligible";function OO(a){var b=!0;return b}OO.publicName="isConsentGranted";function PO(a){var b=!1;return b}PO.M="internal.isDebugMode";function QO(){return jo()}QO.M="internal.isDmaRegion";function RO(a){var b=!1;return b}RO.M="internal.isEntityInfrastructure";function SO(a){var b=!1;if(!ph(a))throw G(this.getName(),["number"],[a]);b=E(a);return b}SO.M="internal.isFeatureEnabled";function TO(){var a=!1;return a}TO.M="internal.isFpfe";function UO(){var a=!1;return a}UO.M="internal.isGcpConversion";function VO(){var a=!1;return a}VO.M="internal.isLandingPage";function WO(){var a=!1;return a}WO.M="internal.isOgt";function XO(){var a;return a}XO.M="internal.isSafariPcmEligibleBrowser";function YO(){var a=Mh(function(b){lF(this).log("error",b)});a.publicName="JSON";return a};function ZO(a){var b=void 0;return Ad(b)}ZO.M="internal.legacyParseUrl";function $O(){return!1}
var aP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function bP(){}bP.publicName="logToConsole";function cP(a,b){}cP.M="internal.mergeRemoteConfig";function dP(a,b,c){c=c===void 0?!0:c;var d=[];return Ad(d)}dP.M="internal.parseCookieValuesFromString";function eP(a){var b=void 0;if(typeof a!=="string")return;a&&Eb(a,"//")&&(a=A.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Ad({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Qk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),v=t[0],u=Jk(t.splice(1).join("="))||"";u=u.replace(/\+/g," ");p.hasOwnProperty(v)?typeof p[v]==="string"?p[v]=[p[v],u]:p[v].push(u):p[v]=u}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Ad(n);
return b}eP.publicName="parseUrl";function fP(a){}fP.M="internal.processAsNewEvent";function gP(a,b,c){var d;return d}gP.M="internal.pushToDataLayer";function hP(a){var b=xa.apply(1,arguments),c=!1;if(!kh(a))throw G(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(zd(f.value,this.K,1));try{I.apply(null,d),c=!0}catch(g){return!1}return c}hP.publicName="queryPermission";function iP(a){var b=this;}iP.M="internal.queueAdsTransmission";function jP(a,b){var c=void 0;return c}jP.publicName="readAnalyticsStorage";function kP(){var a="";return a}kP.publicName="readCharacterSet";function lP(){return Sj}lP.M="internal.readDataLayerName";function mP(){var a="";return a}mP.publicName="readTitle";function nP(a,b){var c=this;if(!kh(a)||!gh(b))throw G(this.getName(),["string","function"],arguments);Fw(a,function(d){b.invoke(c.K,Ad(d,c.K,1))});}nP.M="internal.registerCcdCallback";function oP(a,b){return!0}oP.M="internal.registerDestination";var pP=["config","event","get","set"];function qP(a,b,c){}qP.M="internal.registerGtagCommandListener";function rP(a,b){var c=!1;return c}rP.M="internal.removeDataLayerEventListener";function sP(a,b){}
sP.M="internal.removeFormData";function tP(){}tP.publicName="resetDataLayer";function uP(a,b,c){var d=void 0;return d}uP.M="internal.scrubUrlParams";function vP(a){}vP.M="internal.sendAdsHit";function wP(a,b,c,d){if(arguments.length<2||!eh(d)||!eh(c))throw G(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?zd(c):{},f=zd(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?zd(d):{},m=lF(this);h.originatingEntity=aG(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};kd(e,q);var r={};kd(h,r);var t=Kw(p,b,q);Nw(t,h.eventId||m.eventId,r)}}}wP.M="internal.sendGtagEvent";function xP(a,b,c){}xP.publicName="sendPixel";function yP(a,b){}yP.M="internal.setAnchorHref";function zP(a){}zP.M="internal.setContainerConsentDefaults";function AP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}AP.publicName="setCookie";function BP(a){}BP.M="internal.setCorePlatformServices";function CP(a,b){}CP.M="internal.setDataLayerValue";function DP(a){}DP.publicName="setDefaultConsentState";function EP(a,b){}EP.M="internal.setDelegatedConsentType";function FP(a,b){}FP.M="internal.setFormAction";function GP(a,b,c){c=c===void 0?!1:c;}GP.M="internal.setInCrossContainerData";function HP(a,b,c){return!1}HP.publicName="setInWindow";function IP(a,b,c){}IP.M="internal.setProductSettingsParameter";function JP(a,b,c){if(!kh(a)||!kh(b)||arguments.length!==3)throw G(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Gq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!jd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=zd(c,this.K,1);}JP.M="internal.setRemoteConfigParameter";function KP(a,b){}KP.M="internal.setTransmissionMode";function LP(a,b,c,d){var e=this;}LP.publicName="sha256";function MP(a,b,c){if(!kh(a)||!kh(b)||!dh(c))throw G(this.getName(),["string","string","Object"],arguments);for(var d=b.split("."),e=Gq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)throw Error("sortRemoteConfigParameters failed, path points to an undefined object: "+d[f]);if(!jd(e[d[f]]))throw Error("sortRemoteConfigParameters failed, path contains a non-object type: "+d[f]);e=e[d[f]]}if(e[d[f]]===void 0)throw Error("sortRemoteConfigParameters failed, destination is undefined "+
d[f]);if(!Array.isArray(e[d[f]]))throw Error("sortRemoteConfigParameters failed, destination is not an array: "+d[f]);var g=zd(c)||{},h=g.sortKey;if(!h)throw Error("sortRemoteConfigParameters failed, option.sortKey is required");var m=g.ascending!==!1;e[d[f]].sort(function(n,p){if(n[h]===void 0||p[h]===void 0)throw Error("sortRemoteConfigParameters failed, object does not have required property: "+h);return m?n[h]-p[h]:p[h]-n[h]});}
MP.M="internal.sortRemoteConfigParameters";function NP(a){}NP.M="internal.storeAdsBraidLabels";function OP(a,b){var c=void 0;return c}OP.M="internal.subscribeToCrossContainerData";var PP={},QP={};PP.getItem=function(a){var b=null;I(this,"access_template_storage");var c=lF(this).Kb();QP[c]&&(b=QP[c].hasOwnProperty("gtm."+a)?QP[c]["gtm."+a]:null);return b};PP.setItem=function(a,b){I(this,"access_template_storage");var c=lF(this).Kb();QP[c]=QP[c]||{};QP[c]["gtm."+a]=b;};
PP.removeItem=function(a){I(this,"access_template_storage");var b=lF(this).Kb();if(!QP[b]||!QP[b].hasOwnProperty("gtm."+a))return;delete QP[b]["gtm."+a];};PP.clear=function(){I(this,"access_template_storage"),delete QP[lF(this).Kb()];};PP.publicName="templateStorage";function RP(a,b){var c=!1;return c}RP.M="internal.testRegex";function SP(a){var b;return b};function TP(a,b){var c;return c}TP.M="internal.unsubscribeFromCrossContainerData";function UP(a){}UP.publicName="updateConsentState";function VP(a){var b=!1;return b}VP.M="internal.userDataNeedsEncryption";var WP;function XP(a,b,c){WP=WP||new Xh;WP.add(a,b,c)}function YP(a,b){var c=WP=WP||new Xh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=jb(b)?sh(a,b):th(a,b)}
function ZP(){return function(a){var b;var c=WP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.tb();if(e){var f=!1,g=e.Kb();if(g){zh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function $P(){var a=function(c){return void YP(c.M,c)},b=function(c){return void XP(c.publicName,c)};b(fF);b(mF);b(AG);b(CG);b(DG);b(KG);b(MG);b(HH);b(YO());b(JH);b(dL);b(eL);b(AL);b(BL);b(CL);b(IL);b(yO);b(BO);b(OO);b(bP);b(eP);b(hP);b(kP);b(mP);b(xP);b(AP);b(DP);b(HP);b(LP);b(PP);b(UP);XP("Math",xh());XP("Object",Vh);XP("TestHelper",Zh());XP("assertApi",uh);XP("assertThat",vh);XP("decodeUri",Ah);XP("decodeUriComponent",Bh);XP("encodeUri",Ch);XP("encodeUriComponent",Dh);XP("fail",Ih);XP("generateRandom",
Jh);XP("getTimestamp",Kh);XP("getTimestampMillis",Kh);XP("getType",Lh);XP("makeInteger",Nh);XP("makeNumber",Oh);XP("makeString",Ph);XP("makeTableMap",Qh);XP("mock",Th);XP("mockObject",Uh);XP("fromBase64",XK,!("atob"in x));XP("localStorage",aP,!$O());XP("toBase64",SP,!("btoa"in x));a(eF);a(iF);a(CF);a(OF);a(VF);a($F);a(pG);a(yG);a(BG);a(EG);a(FG);a(GG);a(HG);a(IG);a(JG);a(LG);a(NG);a(GH);a(IH);a(KH);a(LH);a(MH);a(NH);a(OH);a(PH);a(UH);a(bI);a(cI);a(nI);a(sI);a(xI);a(GI);a(LI);a(YI);a($I);a(nJ);a(oJ);
a(qJ);a(VK);a(WK);a(YK);a(ZK);a($K);a(aL);a(bL);a(gL);a(hL);a(iL);a(jL);a(kL);a(lL);a(mL);a(nL);a(oL);a(pL);a(qL);a(rL);a(tL);a(uL);a(vL);a(wL);a(xL);a(yL);a(zL);a(DL);a(EL);a(FL);a(GL);a(HL);a(KL);a(wO);a(AO);a(DO);a(MO);a(NO);a(PO);a(QO);a(RO);a(SO);a(TO);a(UO);a(VO);a(WO);a(XO);a(ZO);a(nG);a(cP);a(dP);a(fP);a(gP);a(iP);a(lP);a(nP);a(oP);a(qP);a(rP);a(sP);a(uP);a(vP);a(wP);a(yP);a(zP);a(BP);a(CP);a(EP);a(FP);a(GP);a(IP);a(JP);a(KP);a(MP);a(NP);a(OP);a(RP);a(TP);a(VP);YP("internal.IframingStateSchema",
zO());
E(104)&&a(fL);E(160)?b(MO):b(JO);E(177)&&b(jP);return ZP()};var cF;
function aQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;cF=new Ue;bQ();Bf=bF();var e=cF,f=$P(),g=new sd("require",f);g.Ua();e.C.C.set("require",g);Pa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Xf(n,d[m]);try{cF.execute(n),E(120)&&el&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Pf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");gk[q]=["sandboxedScripts"]}cQ(b)}function bQ(){cF.Xc(function(a,b,c){sp.SANDBOXED_JS_SEMAPHORE=sp.SANDBOXED_JS_SEMAPHORE||0;sp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{sp.SANDBOXED_JS_SEMAPHORE--}})}function cQ(a){a&&sb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");gk[e]=gk[e]||[];gk[e].push(b)}})};function dQ(a){Nw(Hw("developer_id."+a,!0),0,{})};var eQ=Array.isArray;function fQ(a,b){return kd(a,b||null)}function W(a){return window.encodeURIComponent(a)}function gQ(a,b,c){Hc(a,b,c)}
function hQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Kk(Qk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function iQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function jQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=iQ(b,"parameter","parameterValue");e&&(c=fQ(e,c))}return c}var kQ=x.clearTimeout,lQ=x.setTimeout;function mQ(a,b,c){if(Fr()){b&&Kc(b)}else return Dc(a,b,c,void 0)}function nQ(){return x.location.href}function oQ(a,b){return qk(a,b||2)}function pQ(a,b){x[a]=b}function qQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}function rQ(a,b){if(Fr()){b&&Kc(b)}else Fc(a,b)}
var sQ={};var X={securityGroups:{}};
X.securityGroups.access_template_storage=["google"],X.__access_template_storage=function(){return{assert:function(){},T:function(){return{}}}},X.__access_template_storage.F="access_template_storage",X.__access_template_storage.isVendorTemplate=!0,X.__access_template_storage.priorityOverride=0,X.__access_template_storage.isInfrastructure=!1,X.__access_template_storage["5"]=!1;
X.securityGroups.v=["google"],X.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=oQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},X.__v.F="v",X.__v.isVendorTemplate=!0,X.__v.priorityOverride=0,X.__v.isInfrastructure=!0,X.__v["5"]=!0;
X.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){X.__read_event_data=b;X.__read_event_data.F="read_event_data";X.__read_event_data.isVendorTemplate=!0;X.__read_event_data.priorityOverride=0;X.__read_event_data.isInfrastructure=!1;X.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!lb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Ig(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

X.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){X.__detect_youtube_activity_events=b;X.__detect_youtube_activity_events.F="detect_youtube_activity_events";X.__detect_youtube_activity_events.isVendorTemplate=!0;X.__detect_youtube_activity_events.priorityOverride=0;X.__detect_youtube_activity_events.isInfrastructure=!1;X.__detect_youtube_activity_events["5"]=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},T:a}})}();



X.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){X.__detect_history_change_events=b;X.__detect_history_change_events.F="detect_history_change_events";X.__detect_history_change_events.isVendorTemplate=!0;X.__detect_history_change_events.priorityOverride=0;X.__detect_history_change_events.isInfrastructure=!1;X.__detect_history_change_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();


X.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){X.__detect_link_click_events=b;X.__detect_link_click_events.F="detect_link_click_events";X.__detect_link_click_events.isVendorTemplate=!0;X.__detect_link_click_events.priorityOverride=0;X.__detect_link_click_events.isInfrastructure=!1;X.__detect_link_click_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.waitForTags)throw d(e,
{},"Prohibited option waitForTags.");},T:a}})}();
X.securityGroups.detect_form_submit_events=["google"],function(){function a(b,c){return{options:c}}(function(b){X.__detect_form_submit_events=b;X.__detect_form_submit_events.F="detect_form_submit_events";X.__detect_form_submit_events.isVendorTemplate=!0;X.__detect_form_submit_events.priorityOverride=0;X.__detect_form_submit_events.isInfrastructure=!1;X.__detect_form_submit_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&
f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},T:a}})}();
X.securityGroups.read_container_data=["google"],X.__read_container_data=function(){return{assert:function(){},T:function(){return{}}}},X.__read_container_data.F="read_container_data",X.__read_container_data.isVendorTemplate=!0,X.__read_container_data.priorityOverride=0,X.__read_container_data.isInfrastructure=!1,X.__read_container_data["5"]=!1;
X.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){X.__listen_data_layer=b;X.__listen_data_layer.F="listen_data_layer";X.__listen_data_layer.isVendorTemplate=!0;X.__listen_data_layer.priorityOverride=0;X.__listen_data_layer.isInfrastructure=!1;X.__listen_data_layer["5"]=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!lb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},T:a}})}();
X.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){X.__detect_user_provided_data=b;X.__detect_user_provided_data.F="detect_user_provided_data";X.__detect_user_provided_data.isVendorTemplate=!0;X.__detect_user_provided_data.priorityOverride=0;X.__detect_user_provided_data.isInfrastructure=!1;X.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},T:a}})}();



X.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){X.__get_url=b;X.__get_url.F="get_url";X.__get_url.isVendorTemplate=!0;X.__get_url.priorityOverride=0;X.__get_url.isInfrastructure=!1;X.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!lb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!lb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();




X.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){X.__gct=b;X.__gct.F="gct";X.__gct.isVendorTemplate=!0;X.__gct.priorityOverride=0;X.__gct.isInfrastructure=!1;X.__gct["5"]=!1})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[J.m.rf]=d);c[J.m.Og]=b.vtp_eventSettings;c[J.m.wk]=b.vtp_dynamicEventSettings;c[J.m.ce]=b.vtp_googleSignals===1;c[J.m.Nk]=b.vtp_foreignTld;c[J.m.Lk]=b.vtp_restrictDomain===
1;c[J.m.ji]=b.vtp_internalTrafficResults;var e=J.m.Pa,f=b.vtp_linker;f&&f[J.m.ma]&&(f[J.m.ma]=a(f[J.m.ma]));c[e]=f;var g=J.m.ki,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;Iq(b.vtp_trackingId,c);rO(b.vtp_trackingId,b.vtp_gtmEventId);Kc(b.vtp_gtmOnSuccess)})}();



X.securityGroups.get=["google"],X.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Kw(String(b.streamId),d,c);Nw(f,e.eventId,e);a.vtp_gtmOnSuccess()},X.__get.F="get",X.__get.isVendorTemplate=!0,X.__get.priorityOverride=0,X.__get.isInfrastructure=!1,X.__get["5"]=!1;
X.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){X.__detect_scroll_events=b;X.__detect_scroll_events.F="detect_scroll_events";X.__detect_scroll_events.isVendorTemplate=!0;X.__detect_scroll_events.priorityOverride=0;X.__detect_scroll_events.isInfrastructure=!1;X.__detect_scroll_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();



X.securityGroups.detect_form_interaction_events=["google"],function(){function a(){return{}}(function(b){X.__detect_form_interaction_events=b;X.__detect_form_interaction_events.F="detect_form_interaction_events";X.__detect_form_interaction_events.isVendorTemplate=!0;X.__detect_form_interaction_events.priorityOverride=0;X.__detect_form_interaction_events.isInfrastructure=!1;X.__detect_form_interaction_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();
var vp={dataLayer:rk,callback:function(a){fk.hasOwnProperty(a)&&jb(fk[a])&&fk[a]();delete fk[a]},bootstrap:0};
function tQ(){up();Dm();KB();Cb(gk,X.securityGroups);var a=Am(pm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;To(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||L(142);Of={Uo:cg}}var uQ=!1;
function co(){try{if(uQ||!Km()){Oj();Lj.P=Oi(18,"");
Lj.rb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Lj.Sa="ad_storage|analytics_storage|ad_user_data";Lj.Ba="56n0";Lj.Ba="56n0";Lj.ka=!0;if(E(109)){}Ha[8]=!0;var a=tp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});$o(a);rp();TE();gr();xp();if(Em()){kG();AC().removeExternalRestrictions(xm());}else{Ty();Mf();Hf=X;Jf=DE;eg=new lg;aQ();tQ();Gr();ao||($n=fo());
op();PD();bD();vD=!1;A.readyState==="complete"?xD():Ic(x,"load",xD);WC();el&&(kq(yq),x.setInterval(xq,864E5),kq(UE),kq(nC),kq(aA),kq(Bq),kq(ZE),kq(yC),E(120)&&(kq(sC),kq(tC),kq(uC)),VE={},kq(WE),Ri());fl&&(On(),Rp(),RD(),VD(),TD(),En("bt",String(Lj.C?2:Lj.N?1:0)),En("ct",String(Lj.C?0:Lj.N?1:Fr()?2:3)),SD());tE();Yn(1);lG();ZD();ek=zb();vp.bootstrap=ek;Lj.ka&&OD();E(109)&&uA();E(134)&&(typeof x.name==="string"&&Eb(x.name,"web-pixel-sandbox-CUSTOM")&&$c()?dQ("dMDg0Yz"):x.Shopify&&(dQ("dN2ZkMj"),$c()&&dQ("dNTU0Yz")))}}}catch(b){Yn(4),uq()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Go(n)&&(m=h.ol)}function c(){m&&uc?g(m):a()}if(!x[Oi(37,"__TAGGY_INSTALLED")]){var d=!1;if(A.referrer){var e=Qk(A.referrer);d=Mk(e,"host")===Oi(38,"cct.google")}if(!d){var f=hs(Oi(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Oi(37,"__TAGGY_INSTALLED")]=!0,Dc(Oi(40,"https://cct.google/taggy/agent.js")))}var g=function(v){var u="GTM",w="GTM";Yj&&(u="OGT",w="GTAG");
var y=Oi(23,"google.tagmanager.debugui2.queue"),z=x[y];z||(z=[],x[y]=z,Dc("https://"+Pj.Ag+"/debug/bootstrap?id="+ig.ctid+"&src="+w+"&cond="+String(v)+"&gtm="+Kr()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:uc,containerProduct:u,debug:!1,id:ig.ctid,targetRef:{ctid:ig.ctid,isDestination:vm()},aliases:ym(),destinations:wm()}};C.data.resume=function(){a()};Pj.Zm&&(C.data.initialPublish=!0);z.push(C)},h={lo:1,rl:2,Hl:3,jk:4,ol:5};h[h.lo]="GTM_DEBUG_LEGACY_PARAM";h[h.rl]="GTM_DEBUG_PARAM";h[h.Hl]="REFERRER";
h[h.jk]="COOKIE";h[h.ol]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Kk(x.location,"query",!1,void 0,"gtm_debug");Go(p)&&(m=h.rl);if(!m&&A.referrer){var q=Qk(A.referrer);Mk(q,"host")===Oi(24,"tagassistant.google.com")&&(m=h.Hl)}if(!m){var r=hs("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.jk)}m||b();if(!m&&Fo(n)){var t=!1;Ic(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&uQ&&!fo()["0"]?bo():co()});

})()

