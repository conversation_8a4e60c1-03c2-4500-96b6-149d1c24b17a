/**
 * jQuery m 1.0 -
 *
 * Copyright (c) 2009 <PERSON> (<EMAIL>)
 *
 * Licence:
 *
 * jQuery image is licensed under a Creative Commons
 * Attribution-Noncommercial-No Derivative Works 3.0 Unported
 *
 * License (http://creativecommons.org/licenses/by-nc-nd/3.0/).
 *
 * You are free:
 *
 * 	- to copy, distribute and transmit the work
 *
 * Under the following conditions:
 *
 * 	- Attribution. You must attribute the work in the manner specified by the
 *    author or licensor (but not in any way that suggests that they endorse
 *    you or your use of the work).
 *
 * 	- Noncommercial. You may not use this work for commercial purposes.
 *
 * 	- No Derivative Works. You may not alter, transform, or build upon this work.
 *
 *
 *  For any reuse or distribution, you must make clear to others the license terms
 *  of this work. The best way to do this is with a link to this web page.
 *
 *  Any of the above conditions can be waived if you get permission from the
 *  copyright holder.
 *
 *  Nothing in this license impairs or restricts the author's moral rights.
 *
 *
 * $Date: 2010-01-01 10:28:07 -0500 (Fri, 01 Jan 2010) $
 * $Rev: 1 $
 *
 **/

(function ($) {

	$.fn.image = function (settings) {
		var $this = $(this);
		if ($this.length == 0) return $this;

		var instance = $this.data('image');

		if (!instance) {
			instance = setup.apply(this, arguments);
			$this.data('image', instance);
		}
		return instance;
	}

	var defaults = {
		//elements: ".",
		//img: "img",
		mode: "rollover",
		styles: {
			box: "img_box",
			img: "img_tag"
		},
		actions: {
			download: {
				enable: false,
				url:function ($el) {return $el.attr('href');}
			},
			preview: {
				enable: false
			}
		}
    };

	var setup = function (settings) {

		this.settings = $.extend(true, defaults, settings || {});

		$.extend(this, image);

		var $this = this,
			settings = $this.settings,
			styles = settings.styles
		;

		$this.each(function (index, element) {

			var $element = $(element),
				$parent = $(settings.box, $element).addClass(styles.box),
				$img = $("img", $parent),
				actions = settings.actions,
				$overlay = $('<div class="img_overlay"/>').disableSelection()
			;

			if (actions.preview) {
				var actions_preview = actions.preview,
					$action = $('<a class="img_action action_preview" href="">Aperçu</a>')
				;
				
				$action.bind('click', function (event) {
					event.preventDefault();
					event.stopPropagation();

					this.popup = $(document).popup({
						show: false,
						modal: true,
						mode:"center",
						styles: {
							close: "img-close"
						}
					});
					
					var src = $img.attr('src').replace(/thumb/, "src").replace(/\-(MED|BIG|LRG|SML|THB)/, ""),
						//$preview = $('<p id="preview-img"><img class="img_tag" src="'+src+'"/></p>')
						$preview = $('<img class="img_popup"/>').disableSelection()
					;
					
					this.popup.wait();

					$preview.bind('load', {popup: this.popup, html:$preview}, function (event) {
						event.data.popup.show($preview);
					}).attr('src', src);

				});

				$overlay.append($action)
			}

			if (actions.download.enable) {
				var actions_download = actions.download,
					href = actions_download.url
				;
				if (href instanceof Function) href = href($img);
				$overlay.append('<a class="img_action action_download" href="'+href+'">Télécharger</a>')
			}
			
			if (settings.mode == "rollover") {
				var timeout = null;
				$overlay.hide();
				$element.bind('mouseover', {el:$overlay}, function (event) {
					event.data.el.show();
					/*if (event.data.el.is(':hidden')) {
						timeout = setTimeout(function () {
							event.data.el.show();
						}, 200);
					}*/
				}).bind('mouseout', {el:$overlay}, function (event) {
					//clearTimeout(timeout);
					event.data.el.hide();
				});
			}
			else {
				$overlay.bind('mouseover', {el:$overlay}, function (event) {
					event.data.el.addClass('img_overlay2');
				}).bind('mouseout', {el:$overlay}, function (event) {
					event.data.el.removeClass('img_overlay2');
				});
			}
			
			$parent.append($overlay);

		});

	}

	var image = {

		version : "beta"

	};

})(jQuery);
