/**
 * <AUTHOR>
 */

var $label = $label || null,
	lang_lower = 'en',
	lang_upper = 'EN',
	version = new Object(),
	previous_focus = $('#dummy')
;

	var list = window.location.search.substr(1).split('&');
	var getParameters = [];
	var needRedirect = false;
	for(el in list){
		var  param = list[el].split('=');
		if(param[0]!='logout'){
			getParameters.push(list[el]);
		}
		else needRedirect = true;
	}
	if(needRedirect === true)
		window.location = window.location.pathname +'?'+ getParameters.join('&');

jQuery(document).ready(function(){


    //jQuery.noConflict();
	var lang_lower = $("html").attr('lang'),
		lang_upper = lang_lower.toUpperCase();
		jQuery("#slideshow").slideshow({
			mode:"slideshow",
			img: ">a>img",
			slide: {mode:"carousel"},
			css: {height:"400px"},
			options: {
				download:true
			}
		});

	/** Tabs **/
	var $tab_list = $("#tabs-list");
	if ($tab_list.length > 0) {


		$("#slideshow").slideshow({
			mode:"slideshow",
			img: ">a>img",
			slide: {mode:"carousel"},
			css: {height:"400px"},
			options: {
				download:true
			}
		});
		var tabs = $tab_list.tabs({
			elements: "#identification,#diaporama,#video,#project",
			heads: ">h2",
			styles: {
				tabLabel: "tab-label",
				tabLabelFocus: "tab-label-current",
				tabFocus: "tab-focus",
				tabHide: "script_none"
			}
		});

		tabs.show();
	}

	var tabs = $("#celebration-form")
	if (tabs.length > 0) {
		tabs.tabs({
			elements: "fieldset.tab",
			heads: ">h2",
			styles: {
				tabLabel: "tab-label",
				tabLabelFocus: "tab-label-current",
				tabFocus: "tab-focus",
				tabHide: "script_none"
			}
		});
	}

	/** Slideshow section **/

	var $gallery = $("#gallery");
	if ($gallery.length > 0) {

		$gallery.slideshow({
			mode:"gallery",
			img: ">img",
			row:7,	// (for gallery mode)
			ratio: 3/4,	// (for gallery mode)
			options: {
				link:{follow:false},
				slider:{enable:true, min:2, max:8, step:1, value:4}	// (for nomal mode)
			}
		});
	}

	var slide = $("#slideshow-home>div:eq(0)");
	$(".slide-pager").bind('click', function (event) {
		var $this = $(this);
		if (!$this.hasClass("pager-selected")) {
			$(".pager-selected").removeClass("pager-selected");
			$this.addClass("pager-selected");
			slide.addClass("script_none");
			slide = $("#slideshow-home>div:eq("+$this.attr('attr')+")").removeClass("script_none").fadeIn("slow");
		}
	});
	$("#slideshow-home").removeClass("script_#cand_intention-formprogress");

	var photo_legend = $(".photo-legend");
	if (photo_legend.length > 0) {
		$(".photo-legend").image({
			container: "dl",
			box: ">dt",
			actions: {
				download:{
					enable: $("#logout").length == 1,
					url: function ($img) {
						return "/download.php?src="+$img.attr('src');
					}
				},
				preview: {
					enable: true
				}
			}
		});
	}

	$("table.sort").sort();

		/*gestion delapage cand_intention*/

	function  fadeinputlangue(en,fr){

		if(en == true){
			$( "textarea[id$='_en']").parent().fadeIn();
			$( "label[for$='_en']").parent().fadeIn();
		} else if(en==false) {
			$( "textarea[id$='_en']").parent().fadeOut();
			$( "label[for$='_en']").parent().fadeOut();
    		//$( "textarea[id$='_en']").val("") ;
		}

		if(fr == true){
		 	$( "textarea[id$='_fr']").parent().fadeIn();
		 	$( "label[for$='_fr']").parent().fadeIn();
		} else if(fr==false)  {
			$( "textarea[id$='_fr']").parent().fadeOut();
			$( "label[for$='_fr']").parent().fadeOut();
    		//$( "textarea[id$='_fr']").val("") ;
		}

	}


 	if( $("#cand_intention-EN_FR0").is(":checked") ==false
 		&& $("#cand_intention-EN_FR1").is(":checked")==false
 		&& $("#cand_intention-EN_FR2").is(":checked") ==false ){

		var valformintentionvide = false;


		$("textarea[name$='_fr']" ).each(function() {
		  if($( this ).val()!=""){
		  	valformintentionvide = true;
		  	$("#cand_intention-EN_FR1").prop( "checked", true );
		  }
		});

		$("textarea[name$='_en']" ).each(function() {
			if($( this ).val()!=""){
		  		valformintentionvide = true;
		  		$("#cand_intention-EN_FR0").prop( "checked", true );
		  	}
		});

		if(valformintentionvide==false){
			if($("html").attr("lang") =="fr"){
				$("#cand_intention-EN_FR1").prop( "checked", true );
			} else {
				$("#cand_intention-EN_FR0").prop( "checked", true );
			}


		}
	}

 	if( $("#cand_intention-EN_FR0").is(":checked") ){
		fadeinputlangue(true,false) ;
	}

	if( $("#cand_intention-EN_FR1").is(":checked") ){
		fadeinputlangue(false,true);
	}

	if( $("#cand_intention-EN_FR2").is(":checked") ){
		fadeinputlangue(true,true);
	}

	$( "#cand_intention-EN_FR0" ).click(function() {
	  if( $("#cand_intention-EN_FR0").is(":checked") ){
			fadeinputlangue(true,false) ;
		}
	});

	$( "#cand_intention-EN_FR1" ).click(function() {
		if( $("#cand_intention-EN_FR1").is(":checked") ){
			fadeinputlangue(false,true);
		}
	});

	$( "#cand_intention-EN_FR2" ).click(function() {
		if( $("#cand_intention-EN_FR2").is(":checked") ){
			fadeinputlangue(true,true);
		}
	});





	// var forms = $("#account-form, #registration-form, #celebration-form, #cand_intention-form");
	// if (forms.length > 0) {
	// 	$("#account-form, #registration-form, #celebration-form, #cand_intention-form").forms({
	// 		optionals: {
	// 			//focus: false
	// 		},
	// 		plugins: {
	// 			'account-face': {id: "account-face", name: "upload", settings: {}},
	// 			'account-CV': {id: "account-CV", name: "upload", settings: {type:"docs"}},
	// 			'account-languageIDs' : {id: "account-languageIDs", name: "select", settings: {type:"multi", label:$label, test:1}},
	// 			'account-nationality' : {id: "account-nationality", name: "select", settings: {type:"multi", label:$label, test:2}},
	// 			'account-country_expertIDs' : {id: "account-country_expertIDs", name: "select", settings: {type:"multi", label:$label}},

	// 			'multiselect comboboxMulti-form' : {name: "select", settings: {default_value: '00001', type:"multi", label:$label, change: function () {$("#celebration-form").tabs().resize(null)}}},
	// 			'input-datepicker' : {name: "datepicker", settings: { dateFormat: "yy-mm-dd" }},
	// 			'upload-image': {name: "upload", settings: {beforeSubmit: function () {return $("#celebration-celebration_activityID0").val() != ''}}},
	// 			'upload-default': {name: "upload", settings: {type:"docs", beforeSubmit: function () {return $("#celebration-celebration_activityID0").val() != ''}}}
	// 		}
	// 	});
	// }

	// $("#celebration-form>fieldset.tab").each(function (index, element) {
	// 	var nb_error = $("p.error-form", element).length;
	// 	if (nb_error > 0) $("h2>span", element).append('<span class="error-number">'+nb_error+'</span>');
	// })

	// if ($('#celebration-login, #cand_intention-login').length > 0) {
	// 	$(document).popup({
	// 		modal: true,
	// 		mode: "center",
	// 		css: {background:"none", border:"none", width:"500px", margin:"0 0 0 -250px"},
	// 		html: $('#celebration-login, #cand_intention-login')
	// 	});
	// }



	$("#registration-participant_typeID").bind('change', function () {
		var $select = $("#registration-select"),
			name = $select.attr('name')
		;
		$select.attr('disabled', "disabled").html("").attr('name', "");
		$("#registration-"+name).removeAttr("disabled").val('00001').attr('name', "select_value");

		$.ajax({
			url:		window.location.href,
			data:		"group=registration-form&participant_typeID="+this.value,
			type:		"POST",
			dataType:	"json",
			success:	function (response) {

				if (response.html) {
					$("#uniform-registration-select span").text('--');
					$("#registration-select").html(response.html).attr('name', "select_value").removeAttr("disabled");
					$("#registration-"+response.name).attr('disabled', "disabled").val('00001');
					$("#registration-select [value='00001']").each(function() { $(this).attr('selected', 'selected');$(this).change();});

					var countryId = $("#registration-countryID").attr('value');
					var org_instID = $("#registration-org_instID").attr('value');
					var country_org_id ='00001';
					if(countryId!="00001")
						country_org_id = countryId;
					if(org_instID!='00001')
						country_org_id = org_instID;

					if(country_org_id != '00001'){
						$("#registration-select [value='00001']").removeAttr('selected');
						var t = $("#registration-select [value='"+country_org_id+"']");
						t.each(function() { $(this).attr('selected', 'selected');$(this).change(); });
					}
				}
			}
		});

	});
	var countryId = $("#registration-countryID").attr('value');
	if(countryId != '00001')
		$("#registration-participant_typeID").trigger( "change" );

	var org_instID = $("#registration-org_instID").attr('value');
	if(org_instID != '00001'){
		$("#registration-participant_typeID").trigger( "change" );}

	if(countryId == '00001' && org_instID == '00001')
		$("#registration-participant_typeID").trigger( "change" );

	if ($("#webcast-description").length > 0) {
		 var timer = setInterval(function () {
			 $.get(""+window.location+'&X-Requested-With=XMLHttpRequest', function (data) { $("#webcast-description").html(data); });
		 }, $refresh_webcast);
	}

	if ($("#slideshow-items").length > 0) slideshow_items_func();

	if ($("#film").length > 0) launch_flowplayer($("#film"), "https://ich.unesco.org/doc/download.php?versionID="+$versionID);
	if ($("#recording").length > 0) launch_flowplayerAudio($("#recording"), $mp3_file);


	if ($('.fullcalendar').length > 0) {
		var date = new Date();
		var d = date.getDate();
		var m = date.getMonth();
		var y = date.getFullYear();

		$('.fullcalendar').fullCalendar({
			header: {
				left: 'prev,next today',
				center: 'title',
				right: 'month,agendaWeek,agendaDay'
			},
			editable: false,
			events: $news_array
		});
	}

});

	/** Live section **/
/*
$("a.popup").live('click', function (event) {
	event.preventDefault();

	var url_href = $(this).attr('href'),
		vars = getUrlVars(url_href),
		width = vars.width || 800,
		offset = width / 2,
		call = vars.call || null,
		mode = vars.mode || "center"
	;

	var instance = $(document).data('popup');
	if (instance) instance.remove();

	var myPopup = $(document).popup({
		modal: true,
		mode: mode,
		css: {background:"none", border:"none", width:width+"px", margin:"0 0 0 -"+offset+"px"},
		load: url_href,
		ready: function (element) {
			switch (call) {
				case 'registration' :	$("#registration-login", element).forms({
											optionals: {
												focus: false,
												ajaxSubmit: {
													'registration-login': {
														success: function (data) {
															if (data.error) {
																var error = data.error;
																if (typeof(error) == 'string')
																	$("#registration-login>fieldset>div>p:first").replaceWith(error);
															}
															else if (data.msg == 'ok') location.href = window.location;
														}
													}
												}
											}
										});
										break;

				case 'examination' :
										break;

				case 'slideshow' :		slideshow_items_func();
										break;

				case 'film' :			launch_flowplayer($("#film"), "https://ich.unesco.org/doc/download.php?versionID="+vars.id);
										break;

				case 'mp4' :			launch_flowplayer($("#film"), $url_mp4);
										break;

				case 'recording' :		launch_flowplayerAudio($("#recording"), vars.file);
										break;
			}
		}
	});
});





$(".dropdown").live('click', function (event) {
	event.preventDefault();

	$(this).bind('click', function (event) {
		event.preventDefault();
		$element = (this.id ? $("#"+this.id+"-element") : $(this).next('span:first'));
		if ($element.hasClass("login-hide")) {
			$element.addClass("login-show").removeClass("login-hide");
			$("html").one('click', function () {
				$element.triggerHandler('click');
			});
		}
		else {
			$element.addClass("login-hide").removeClass("login-show");
			$("html").unbind('click');
		}
	}).die('click').triggerHandler('click');

});

$("#login").live('click', function (event) {
	event.preventDefault();

	$("#login-form").bind('click', function (event) {
		event.stopPropagation();
	});

	$("#login").bind('click', function (event) {
		event.preventDefault();
		$element = $("#login-group");
		if ($element.hasClass("login-hide")) {
			$element.addClass("login-show").removeClass("login-hide");
			$("html").one('click', function () {
				$("#login").triggerHandler('click');
			});
		}
		else {
			$element.addClass("login-hide").removeClass("login-show");
			$("html").unbind('click');
		}
	}).die('click').triggerHandler('click');

});

$("#print").live('click', function (event) {
	if (window.print) {
		event.preventDefault();
		event.stopPropagation();
		window.print();
		return false;
	}
	return true;
});

$("a.link-toc").live('click', function (event) {
	event.preventDefault();
	event.stopPropagation();
	$href = $(this).attr('href').replace(/([ %;&,.+*~\':"!^$[\]()=>|\/])/g,'\\$1');
	$("html,body").animate({
		scrollTop: $($href).offset().top
	}, 1000);
});

$("a.slideshow").live('click', function (event) {
	event.preventDefault();
	event.stopPropagation();

	var myPopup = $(document).popup({
		modal: true,
		load: "scripts_php/slideshow-popup.php?id="+$(this).attr("cote_new")
	});
});

$("#display1,#display2").live('change', function (event) {

	if (this.id == "display1") {
		selected_index = this.selectedIndex;
		$("#display2>option").each(function (index, item) {
			if (index == selected_index) {
				if ($(item).is(":selected"))
					$(item).attr('selected', false);
				$(item).attr('disabled', true);
			}
			else
				$(item).attr('disabled', false);
		});
	}

	//this.form.submit();
});


$("#inscription").live('change', function () {
	$.ajax({
		url: this.form.action,
		type: "POST",
		data: $(this).attr('name')+"="+$(this).val(),
		dataType: "json",
		success: function (data) {
			for (i in data) {
				$("#"+i+">option").each(function (index, element) {
					//alert(parseInt($(this).val())+" != "+$.inArray(""+parseInt($(this).val()), data[i]))
					if ($.inArray(""+parseInt($(this).val()), data[i]) != -1)
						$(element).attr('disabled', false);
					else
						$(element).attr('disabled', true);
				});
			}
		}
	});
});


*/
$(document).ready(function () {
	$("#slider-all").on('click', function (event) {
		var $all = $(this);
		if ($all.hasClass('slider-down')) {
			$(".slider-down:not(#slider-all)").addClass('slide-all').trigger('click');
			$all.removeClass("slider-down").addClass("slider-up");
		}
		else {
			$(".slider-up:not(#slider-all)").trigger('click');
			$all.removeClass('slider-up').addClass("slider-down");
		}
	});



	$("a.stopPropagation").on('click', function (event) {
		event.stopPropagation();
	});


	$(".slider-live:not(#slider-all)").on('click', function (event) {
		var $title = $(this),
			$description = $title.parent().next(".description")
		;
		if ($description.length == 0) $description = $title.next(".slide_show");

		$title.removeClass("slider-live");

		if ($description.length = 1) {

			var $div = $description;

			var func_slideDown = function () {
				if (!$div.is(":visible")) {
					var el = $div.parent(),
						st = (document.documentElement.scrollTop || document.body.scrollTop),
						ot = el.offset().top,
						wh = (window.innerHeight && window.innerHeight < $(window).height()) ? window.innerHeight : $(window).height(),
						offset = (ot + el.height()) - wh + 20;

					if (offset > wh) offset = ot;

					$("html,body").animate({
						scrollTop: offset
					}, 1000);
				}
			}

			if ($title.hasClass("slider-down")) {
				var callback = func_slideDown;
				if ($title.hasClass('slide-all')) {
					$title.removeClass('slide-all');
					callback = null;
				}
				$div.slideDown("normal", callback);
				$title.addClass("slider-up").removeClass("slider-down");
			}
			else {
				$div.slideUp();
				$title.removeClass('slider-up').addClass("slider-down");
			}

		/*	$title.bind('click', [$div, $title], function (event) {
				var $el = event.data[0];
				if ($el.css('display') == "none") {
					var callback = func_slideDown;
					if (event.data[1].hasClass('slide-all')) {
						event.data[1].removeClass('slide-all');
						callback = null;
					}
					$el.slideDown('normal', callback);
					$(this).addClass('slider-up').removeClass("slider-down");
				}
				else {
					$el.slideUp();
					$(this).removeClass('slider-up').addClass("slider-down");
				}
			});
			*/
			$("a.stopPropagation", $title).on('click', function (event) {
				event.stopPropagation();
			});

		}
	});
});
/*
$("a.exam_report").live('click', function (event) {
	event.preventDefault();
	event.stopPropagation();

	var $a = $(this);

	var form = null;
	var myPopup = $(document).popup({
		modal: true,
		mode: "scroll",
		css: {width:"800px", margin:"40px 0 40px -400px"},
		load: $(this).attr('href'),
		ready: function (item) {
			$("head").append($("#exam-css", item));
			$("#exam-css", item).remove();
		},
		afterDisplay: function (item) {
			var tabs = $("#tabs").tabs({
				elements: ">fieldset",
				heads: ">legend",
				tabScroll: false,
				styles: {
					tabPanel: "tabPanel",
					tabLabel: "exam-tabLabel",
					tabLabelFocus: "exam-tabLabelSelected",
					tabItem: "exam-tabItem",
					tabItemFocus: "exam-tabItemFocus",
					tabHide: "tabHide",
					tabFocus: "exam-tabSelected"
				}
			});

			form = $("#exam-form", item).form({
				success: function (data) {
					var split_data = data.split('::');

					$("#exam-save").attr('disabled', false);

					if (form.bt_submit == "valid") {
						$("#exam-save").css('display', "none");
						$("#exam-valid").attr('disabled', "disabled");
					}

					$("#exam-msg").html(split_data[0]);

					$("#examination_valuesID").val(split_data[1]);
					$("#examinerID").val(split_data[2]);
					$a.html(split_data[3]).attr('style', split_data[4]);
					$("#shortcut-"+split_data[5]).attr('style', split_data[4]);
					$("#exam-status").html(split_data[3]).attr('style', split_data[4]);
				},
				change: function ($element) {

					if ($element.hasClass("criterion") || $element.hasClass("score") ) {
						var undefined = new Array(),
							satisfied = new Array(),
							unsatisfied = new Array(),
							referal = new Array(),
							elements = this.form_elements,
							count = 0,
							ready_validation = true
						;

						for (i in c = eval('(' +$("#exam-criterion").attr('value')+ ')')) {
							var name = 'crit'+parseInt(i)+'_satisfiedID'
								element = elements[name]
							;
							switch (element.$.val()) {
								case "2" : 	satisfied.push(c[i]);
											break;
								case "3" : 	unsatisfied.push(c[i]);
											break;
								case "5" : 	referal.push(c[i]);
											break;
								default : 	undefined.push(c[i]);
							}
							count++;
							ready_validation = ready_validation && (element.value != 1)
						}

						var $score = $("#overall_score"), $score_val = $score.val();
						if ($score_val > 10 || $score_val < 0) $score.val('');
						if ($score.length == 1)
							ready_validation = ready_validation && ($score.val() != '')

						if ($("#exam_final_auto").length == 1) {
							if (ready_validation)
								$("#overall_yes,#overall_no").attr("disabled", false);
							else {
								$("#overall_yes,#overall_no").attr("disabled", true);
								$("#overall_undefined").attr('checked', true).triggerHandler('change');
							}
						}
						else {
							if (satisfied.length == count)
								$("#overall_yes").attr('checked', true);
							else if (unsatisfied.length > 0)
								$("#overall_no").attr('checked', true).triggerHandler('change');
							else if (referal.length > 0) {
								$("#overall_ref").attr('checked', true).triggerHandler('change');
							}
						}

						if (undefined.length == 0) undefined[0] = $label['none'];
						if (satisfied.length == 0) satisfied[0] = $label['none'];
						if (unsatisfied.length == 0) unsatisfied[0] = $label['none'];
						if (referal.length == 0) referal[0] = $label['none'];

						$("#undefined").text(undefined.join(', '));
						$("#satisfied").text(satisfied.join(', '));
						$("#unsatisfied").text(unsatisfied.join(', '));
						$("#referal").text(referal.join(', '));

						ready_validation = ready_validation && (elements['overall_satisfiedID'].value != 1);

						if (ready_validation) {
							elements['valid'].$.attr('disabled', false);
						}
					}

				}
			});

			$("#exam-valid,#exam-save", form).bind('click', function () {
				form.bt_submit = this.name;
			});
		},
		beforeClose: function () {

			if (form.notSaved()) {
				return confirm($label['confirm_without_saving']);
			}

			return true;
		},
		afterClose: function () {
			$("#exam-css").remove();
		}
	});
});*/


	/** FUNCTION PART **/

function slideshow_items_func () {
	var slideshowNav = '<p id="slideshow-nav">&laquo; <a href="#" onclick="$.galleria.prev(); return false;">previous</a> | <a href="#" onclick="$.galleria.next(); return false;">next</a> &raquo;</p>';

	$("#slideshow-items")
		.wrap($('</div>', {'class': 'slideshow-flex'}))
		.before('<div id="slideshow"></div>')
		.addClass('gallery_demo');

		$("#slideshow-items").parent()
			.before(slideshowNav);

	$("#slideshow-items")
		.galleria({
		history   : true, // activates the history object for bookmarking, back-button etc.
		clickNext : true, // helper for making the image clickable
		insert    : '#slideshow', // the containing selector for our main image
		onImage   : function (image, caption, thumb) { // let's add some image effects for demonstration purposes

			// fade in the image & caption
			if(! (jQuery.browser.mozilla && navigator.appVersion.indexOf("Win")!=-1) ) { // FF/Win fades large images terribly slow
				image.css('display','none').fadeIn(1000);
			}
			caption.css('display','none').fadeIn(1000);

			// fetch the thumbnail container
			var _li = thumb.parents('li');

			// fade out inactive thumbnail
			_li.siblings().children('img.selected').fadeTo(500,0.3);

			// fade in active thumbnail
			thumb.fadeTo('fast',1).addClass('selected');

			// add a title for the clickable image
			image.attr('title', 'img_suiv');
		},
		onThumb : function(thumb) { // thumbnail effects goes here

			// fetch the thumbnail container
			var _li = thumb.parents('li');

			// if thumbnail is active, fade all the way.
			var _fadeTo = _li.is('.active') ? '1' : '0.3';

			// fade in the thumbnail when finnished loading
			thumb.css({display:'none',opacity:_fadeTo}).fadeIn(1500);

			// hover effects
			thumb.hover(
				function() { thumb.fadeTo('fast',1); },
				function() { _li.not('.active').children('img').fadeTo('fast',0.3); } // don't fade out if the parent is active
			)
		}
	});
	var t = $('#slideshow-items img').first();
	location.hash = t.attr('src');
}

var launch_flowplayer = function ($element, url) {

	$element.flowplayer("/swf/flowplayer/flowplayer-3.2.7.swf", {
		clip: {
			url: url,
			scaling: "orig", autoPlay: true,
			onMetaData: function (clip) {
				//alert(clip.height + " " + clip.metaData.height)
				//var wrap = $("#film").css({height: clip.metaData.height});
			}
		},
		canvas:{backgroundGradient:"none", backgroundColor: "#FFFFFF"},
		play: {replayLabel: $label.play_again }
	});
}

var launch_flowplayerAudio = function ($element, file) {

	$element.flowplayer("/swf/flowplayer/flowplayer-3.2.7.swf", {
		clip: {
			url: "https://ich.unesco.org"+file,
			scaling: "orig", autoPlay: true
		},
		plugins: {
			controls: {
				autoHide: false,
				fullscreen: false
			},
			audio: {
				url: '/swf/flowplayer/plugins/flowplayer.audio-3.2.2.swf'
			}
		},
		canvas:{backgroundGradient:"none", backgroundColor: "#FFFFFF"}
	});
}

var flowplayerSeek = function (name, seconds) {
	$f(name).seek(seconds);
	return false;
}

function alert_func (string) {
	alert(string.replace(new RegExp("CR", 'g'),"\r\n"));
}
