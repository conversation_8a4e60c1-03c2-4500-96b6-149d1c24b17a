function getFbConfig(element) {
    var config = {
        padding: 10,
        width: 640,
        autoSize: true,
        fitToView: true,
        helpers: {
            title: {
                type: 'inside',
                position: 'bottom'
            }
        },
        tpl: {
            closeBtn: '<a class="fancybox-item fancybox-close" href="javascript:;">' + _closeTxt + '<span></span></a>',
            next: '<a title="' + _nextTxt + '" class="fancybox-nav fancybox-next" href="javascript:;"><span></span></a>',
            prev: '<a title="' + _prevTxt + '" class="fancybox-nav fancybox-prev" href="javascript:;"><span></span></a>'
        },
        beforeLoad: function () {
            var title = element.data('title');
            if (title) {
                this.title = title;
            }
        }
    };

    ['margin', 'width', 'height'].forEach(function (attr) {
        var val = parseInt(element.data('fancybox-' + attr), 10);
        if (!isNaN(val)) {
            config[attr] = val;
        }
    });

    return config;
}

$.initFancybox = function () {
    $('.popup').addClass('fancybox').attr('data-fancybox-type', 'iframe').removeAttr('target');
    $('.fancybox').fancybox(function () {
        return getFbConfig($(this));
    });
};

$(document).ready(function () {
    $.initFancybox();

    function handleFancyBoxClick(event) {
        event.preventDefault();

        $.fancybox.open({
            href: $(this).attr('href'),
            type: 'iframe'
        });
    }

    $(document).ready(function() {
        $('#slideshow-container').on('click.slideShow', 'a.fancybox', handleFancyBoxClick);
        $('#slideshow-clip').on('click.slideShow', 'a.fancybox', handleFancyBoxClick);
    });

});
