/* ===================================================================================
 * File : global.js
 * Description : JS Scripts
 * Authors : Hamza Iqbal - hiqbal[at]actualys.com
 *			 <PERSON><PERSON><PERSON> - mg<PERSON>lin[at]actualys.com
/* =================================================================================== */


/* =================================================================================== */
/* JQUERY CONTEXT */
/* =================================================================================== */
(function($)
{
	/* =================================================================================== */
	/* GLOBAL VARS */
	/* =================================================================================== */

	// Anchor
	var _siteAnchor = window.location.hash;

	/* User infos */
	var _user =
	{
		terminal: '',
		version: 0,
		screenWidth: ''
	};

	var _page =
	{
		doc: $(document),
		win: $(window),
		html: $('html'),
		body: $('body')
	};

	// Uniform - Default config
	var _uniformConfig =
	{
		selectClass: 		'select',
		checkboxClass: 		'checkbox',
		fileBtnClass: 		'form_btn',
		fileButtonHtml: 	'',
		fileDefaultHtml: 	_selectFileTxt,
		fileClass: 			'input_file',
		filenameClass: 		'input_file_name',
		selectAutoWidth: 	false
	};


	// Site main elements
	var _header = $('#header');
	var _navigation = $('#navigation');
	var _userLogin = $('#user_login');
	var _headerPortal = $('#header_portal');
	var _content = $('#content');
	var _mains = $('.main');
	var _main1 = $('#main1');
	var _main2 = $('#main2');
	var _actionsBars = $('.actions_bar');
	var _tabsBlocks = $('.tabs_block');
	var _countriesProfile = $('#nav_countries');
	var _autocompleteFields = $('.autocomplete_field');
	var _listingsDetails = $('.listing_detail');

	// Text size
	var _txtSizeCurrent = parseInt($.cookie('unesco_ich_txt_size')) || 0;
	var _txtSizeMin = 0;
	var _txtSizeMax = 5;
	var _txtSizeClassPrefix = 'size_';
	var _txtSizeClass = _txtSizeClassPrefix+_txtSizeCurrent;


	/* Autocomplete configs */
	_autocompleteCountryConfig =
	{
		appendTo: '#form_country_search',
		minChars: 2,
		maxHeight: 288,
		lookup: _countries
	};


	/**
	 * Init user's infos
	 * $.initUser();
	 ******************************************/
	$.initUser = function()
	{
		$.detectTerminal();
	};



	/**
	 * Terminal detection
	 * $.detectTerminal();
	 ******************************************/
	$.detectTerminal = function()
	{
		var terminalAgent = navigator.userAgent.toLowerCase();
		var agentID = terminalAgent.match(/(iphone|ipod|ipad|android)/);

		if(agentID)
		{
			if(agentID.indexOf('iphone') >= 0) _user.terminal = 'iphone';
			else if(agentID.indexOf('ipod') >=0 ) _user.terminal = 'ipod';
			else if(agentID.indexOf('ipad') >= 0) _user.terminal = 'ipad';
			else if(agentID.indexOf('android') >= 0) _user.terminal = 'android';
		}
		else
		{
			_user.version = parseInt($.browser.version);

			if ($.browser.safari)
			{
				if (terminalAgent.indexOf('chrome')>=0) _user.terminal = 'chrome';
				else _user.terminal = 'safari';
			}
			else if($.browser.mozilla) _user.terminal = 'mozilla';
			else if($.browser.opera) _user.terminal = 'opera';
			else if($.browser.msie)
			{
				if(_user.version == 6) _user.terminal = 'msie6';
				else if(_user.version == 7) _user.terminal = 'msie7';
				else if(_user.version == 8) _user.terminal = 'msie8';
				else if(_user.version == 9) _user.terminal = 'msie9';
			}
		}

		_page.body.addClass(_user.terminal);
		return _user.terminal;
	};


	/**
	 * Init site's anchors
	 * $.initSiteAnchors();
	 ******************************************/
	$.initSiteAnchors = function()
	{
		//var anchors = $('#quick_links, #page_top');
		if(typeof(disableScroll) !="undefined" && disableScroll)
			return;
		/*$.localScroll(
		{
			hash: false,
			duration: 1000
		});*/
	};


	/**
	 * Init the main navigation bar
	 * $.initMainNavigation();
	 ******************************************/
	$.initMainNavigation = function()
	{
		var nav = _navigation.find('> ul');

		nav.superfish(
		{
			popUpSelector: '.subnav',
			hoverClass:    'hover',
			delay:         800,
			animation:     {height: 'show'},
			animationOut:  {height: 'hide'},
			speed:         400,
			speedOut:      200,
			cssArrows:     false
		});
	};

	/**
	 * Init country profil display
	 * $.initCountryProfile();
	 ******************************************/
	$.initCountryProfile = function()
	{
		var form = $('#form_country_search');
		var btn = _countriesProfile.find('> a');

		if(!_countriesProfile.hasClass('opened')) form.hide();

		// Open the form
		_countriesProfile.bind('open', function()
		{
			form.slideDown('fast', function()
			{
				_countriesProfile.addClass('opened');
			});
		});

		// Close the form
		_countriesProfile.bind('close', function()
		{
			form.slideUp('fast', function()
			{
				_countriesProfile.removeClass('opened');
			});
		});

		// Open / Close trigger button
		btn.click(function(e)
		{
			if(_countriesProfile.hasClass('opened')) _countriesProfile.trigger('close');
			else _countriesProfile.trigger('open');

			e.preventDefault();
		});
	};


	/**
	 * Login form display
	 * $.initToggleLogin();
	 ******************************************/
	$.initToggleLogin = function()
	{
		var btnLogin = _userLogin.find('.btn_login');
		var formLogin = $('#form_login');


		if(!_userLogin.hasClass('opened')) formLogin.hide();

		// Open the form
		_userLogin.bind('open', function()
		{
			_userLogin.addClass('opened');
			formLogin.slideDown();
		});

		// Close the form
		_userLogin.bind('close', function()
		{
			formLogin.slideUp(function()
			{
				_userLogin.removeClass('opened');
			});
		});

		// Open / Close trigger button
		btnLogin.click(function(e)
		{
			if(_userLogin.hasClass('opened')) _userLogin.trigger('close');
			else _userLogin.trigger('open');

			e.preventDefault();
		});
	};



	/**
	 * Portal header display
	 * $.initToggleHeaderPortal();
	 ******************************************/
	$.initToggleHeaderPortal = function()
	{

		var navPortal = $('#nav_portal');
		var btnToggle = _headerPortal.find('.btn_toggle');
		var overlay = $('<div id="overlay_portal" />').hide();

		// Overlay insertion
		overlay.insertBefore(_headerPortal);

		// Open the nav
		_headerPortal.bind('open', function()
		{

			_headerPortal.addClass('opened');
			navPortal.slideDown();

			overlay.fadeIn();
		});

		// Close the nav
		_headerPortal.bind('close', function()
		{
			navPortal.slideUp(function()
			{
				_headerPortal.removeClass('opened');
			});

			overlay.fadeOut();
		});

		// Open / Close trigger button
		btnToggle.click(function(e)
		{
			if(_headerPortal.hasClass('opened')) _headerPortal.trigger('close');
			else _headerPortal.trigger('open');

			e.preventDefault();
		});

		overlay.click(function()
		{
			_headerPortal.trigger('close');
		});
	};

	/**
	 * Init actions bar
	 * $.initActionsBar();
	 ******************************************/
	$.initActionsBar = function()
	{
		_actionsBars.each(function()
		{
			var actionsBar = $(this);
			var items = actionsBar.children();
			var btns = items.children('a');

			var btnShare = items.filter('.share').children();
			var btnUrl = items.filter('.url').children();
			var btnSend = items.filter('.send').children();
			var btnPrint = items.filter('.print').children();
			var btnsSize = items.filter('.sizedown, .sizeup').children();
			var btnSizeDown = items.filter('.sizedown').children();
			var btnSizeUp = items.filter('.sizeup').children();


			// Print function
			btnPrint.click(function(e)
			{
				window.print();
				e.preventDefault();
			});


			// Text change size functions
			_content.bind('changeSize', function()
			{
				// Class change
				_content.removeClass(_txtSizeClass);
				_txtSizeClass = _txtSizeClassPrefix+_txtSizeCurrent;
				_content.addClass('size_'+_txtSizeCurrent);

				// Cookie update
				$.cookie('unesco_ich_txt_size', _txtSizeCurrent, { expires: 1, path: '/' });

				// Enable or not the size down button
				if(_txtSizeCurrent == _txtSizeMin) btnSizeDown.trigger('disabled');
				else btnSizeDown.trigger('enabled');

				// Enable or not the size up button
				if(_txtSizeCurrent == _txtSizeMax) btnSizeUp.trigger('disabled');
				else btnSizeUp.trigger('enabled');
			});

			_content.trigger('changeSize');

			btnsSize.each(function()
			{
				var btn = $(this);
				var li = btn.parent();

				// Disable
				btn.bind('disabled', function()
				{
					li.addClass('disabled');
				});

				// Enable
				btn.bind('enabled', function()
				{
					li.removeClass('disabled');
				});

				btn.click(function(e)
				{
					e.preventDefault();
				});

				// Size down
				if(btn.is(btnSizeDown))
				{
					btn.click(function()
					{
						if(_txtSizeCurrent > _txtSizeMin)
						{
							_txtSizeCurrent--;
							_content.trigger('changeSize');
						}
					});
				}

				// Size up
				else
				{
					btn.click(function()
					{
						if(_txtSizeCurrent < _txtSizeMax)
						{
							_txtSizeCurrent++;
							_content.trigger('changeSize');
						}
					});
				}
			});
		});
	};


	/**
	 * Init tabs
	 * $.initTabs();
	 ******************************************/
	$.initTabs = function()
	{
		_tabsBlocks.each(function()
		{
			var tabsBlock = $(this);
			var tabs;
			var tabsLi;
			var tabsHTML = '';
			var tabActive;// = tabs.filter('.selected');
			var linkActive;
			var contents = tabsBlock.find('.tab_content');
			var contentActive = contents.filter('.tab_content_selected');

			var msgTabActive = $('<span class="invisible">' + _tabActiveTxt + '</span>');

			// Creates the tabs HTML
			contents.each(function()
			{
				var content = $(this);
				var title = content.find('.tab_title').text();
				var id = content.attr('id');

				tabsHTML += '<li><a href="#'+id+'" id="tab_'+id+'">'+title+'</a></li>'
			});

			// Inserts the tabs
			tabs = $('<ul class="tabs" />').html(tabsHTML);
			tabsBlock.prepend(tabs);
			tabsLinks = tabs.find('a');

			// Defines the active tab
			if(contentActive.exists())
			{
				tabActive = $('#tab_'+contentActive.attr('id'));
			}
			else
			{
				// Check if an anchor is defined and a matching tab content exists
				if(_siteAnchor && contents.filter(_siteAnchor).exists())
				{
					contentActive = $(_siteAnchor);
					tabActive = $('#tab_'+contentActive.attr('id'));
				}

				// Else the first tab is activated
				else
				{
					tabActive = tabsLinks.first();
					contentActive = contents.first();
				}

				contentActive.addClass('tab_content_selected');
			}


			tabActive.addClass('selected');
			tabActive.prepend(msgTabActive);


			// Run trough all tabs
			tabsLinks.each(function()
			{
				var tab = $(this);
				var content = $(this.hash);

				// Tab selection
				tab.click(function(e)
				{
					if(!tab.hasClass('selected'))
					{
						// Active tab
						tabActive.removeClass('selected');
						tabActive = tab;
						tabActive.addClass('selected');

						// Active content
						contentActive.removeClass('tab_content_selected');
						contentActive = content;
						contentActive.addClass('tab_content_selected');

						// Active message
						msgTabActive.remove();
						tab.prepend(msgTabActive);
					}

					e.preventDefault();
				});
			});



			// Tabs system is ready
			tabsBlock.addClass('tabs_ready');
		});
	};

	/**
	 * Init autocomplete fields
	 * $.initAutocompleteFields();
	 ******************************************/
	$.initAutocompleteFields = function()
	{
		_autocompleteFields.each(function()
		{
			var field = $(this);
			var config = eval(field.data('config'));

			field.autocomplete(config);
		});
	};

	/**
	 * Form styles
	 * $.initForms();
	 ******************************************/
	 $.initFormsStyles = function()
	 {
	 	var form = $('form');
	 	var fields = form.find('select, :file, :radio, :checkbox').not('.sf, .filter-select2');
	 	//var configSelects = $.extend({}, _uniformConfig);
	 	fields.uniform(_uniformConfig);

	 	// Create span inside uniform's selects
	 	$('.select span').after('<i class="arrow"></i>');
	 };

	/**
	 * Init Webcast item refresh
	 * $.initWebcastRefresh();
	 ******************************************/
	$.initWebcastRefresh = function()
	{
		if ($('#webcast-id').length) {
			var webcast_id = $('#webcast-id').attr('data-webcast-id');
			var timeout = 60000; // By default request every minute
			var webcast_refresh = function() {
				$.ajax({
  					url: '?webcast_refresh=' + webcast_id
 				})
 				.done(function(data) {
 					try {
 						data = JSON.parse(data);
 						next = data.next;
 						item = data.item;

 					} catch(e) {
 						next = timeout;
 						item = false;
 					}
 					if (next != 0) {
 						if (item) {
 							$('#webcast-item').html(item);
 						}
 						setTimeout(webcast_refresh, next);
 					} else {
 						$('#webcast-item').html(' ');
 					}
 				})
			}
			setTimeout(webcast_refresh, timeout);
		}
	}


	/**
	 * Init listing detail display
	 * $.initListingsDetails();
	 ******************************************/
	$.initListingsDetails = function()
	{

		var hash = window.location.hash.substring(1);

		_listingsDetails.each(function()
		{
			var listingDetail = $(this);
			var lstDtlItems = listingDetail.find('.lst_dtl_item');
			var btnToggleAll = listingDetail.find('.lst_dtl_toggle_all');
			var changeAllTxt = btnToggleAll.find('.txt');

			changeAllTxt.text(_unfoldTxt);

			// Toggle list item functions
			lstDtlItems.each(function()
			{
				var lstDtlItem = $(this);
				var id = lstDtlItem.attr('id');
				var desc = lstDtlItem.find('.lst_dtl_desc');
				var btnToggle = lstDtlItem.find('.btn_toggle');
				var changeTxt = btnToggle.find('.txt');
				changeTxt.text(_showTxt);

				// Open the description
				lstDtlItem.bind('open', function()
				{
					desc.slideDown(function()
					{
						lstDtlItem.addClass('opened');
						changeTxt.text(_hideTxt);
					});
				});

				// Close the description
				lstDtlItem.bind('close', function()
				{
					desc.slideUp(function()
					{
						lstDtlItem.removeClass('opened');
						changeTxt.text(_showTxt);
					});
				});

				// Open / Close trigger button
				btnToggle.click(function(e)
				{
					if(lstDtlItem.hasClass('opened')) lstDtlItem.trigger('close');
					else lstDtlItem.trigger('open');

					e.preventDefault();
				});

				// Trigger open if anchor
				if (id == hash) {
					btnToggle.trigger('click');
				}

			});

			// Toggle all list items functions
			btnToggleAll.click(function(e)
			{
				if(listingDetail.hasClass('all_opened'))
				{
					lstDtlItems.trigger('close');
					listingDetail.removeClass('all_opened');
					changeAllTxt.text(_unfoldTxt);
				}
				else
				{
					lstDtlItems.trigger('open');
					listingDetail.addClass('all_opened');
					changeAllTxt.text(_foldTxt);
				}

				e.preventDefault();
			});
		});




	};



	/* =================================================================================== */
	/* FUNCTIONS */
	/* =================================================================================== */
	$(document).ready( function()
	{
		//$.initUser();

		$.initSiteAnchors();

		$.initToggleLogin();

		$.initMainNavigation();

		$.initCountryProfile();

		$.initToggleHeaderPortal();

		$.initActionsBar();

		$.initTabs();

		$.initAutocompleteFields();

		$.initFormsStyles();

		$.initListingsDetails();

		$.initWebcastRefresh();
	});



	// Display error message for Dive on IE
    var ua = window.navigator.userAgent;
    var msie = ua.indexOf("MSIE");
    var $message = $('.dive-browser-msg');

    if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
        $message.show();
    }

})(jQuery);
