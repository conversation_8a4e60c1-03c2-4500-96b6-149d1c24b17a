var fpdefs = {
vertex_shader :
'attribute vec3 a_vertexPosition,a_vertexNormal;attribute vec2 a_vert' +
'exTexture;uniform mat4 u_mvMatrix,u_pMatrix,u_nMatrix;uniform vec3 u' +
'_lightPosition;varying vec3 v_normal;varying vec2 v_texture;varying ' +
'vec3 v_eyeVec,v_lightRay;void main(){vec4 vertex=u_mvMatrix*vec4(a_v' +
'ertexPosition,1.);v_normal=vec3(u_nMatrix*vec4(a_vertexNormal,1.));v' +
'ec4 light=u_mvMatrix*vec4(u_lightPosition,1.);v_lightRay=vertex.rgb-' +
'light.rgb;v_eyeVec=-vec3(vertex.rgb);gl_Position=u_pMatrix*vertex;gl' +
'_PointSize=1.;v_texture=a_vertexTexture*3.-vec2(1.,1.);}',
fragment_shader :
'precision mediump float;' +
'uniform vec4 u_lightAmbient,u_lightDiffuse,u_lightSpecular,u_materia' +
'lAmbient,u_materialDiffuse,u_materialSpecular;uniform float u_shinin' +
'ess,u_alpha;uniform sampler2D u_sampler;varying vec3 v_normal;varyin' +
'g vec2 v_texture;varying vec3 v_lightRay,v_eyeVec;void main(){vec3 L' +
'=normalize(v_lightRay),N=normalize(v_normal);float lambertTerm=dot(N' +
',-L);vec4 Ia=u_lightAmbient*u_materialAmbient,Id=vec4(0.,0.,0.,1.),I' +
's=vec4(0.,0.,0.,1.);if(lambertTerm>0.){Id=u_lightDiffuse*u_materialD' +
'iffuse*lambertTerm;vec3 E=normalize(v_eyeVec),R=reflect(L,N);float s' +
'pecular=pow(max(dot(R,E),0.),u_shininess);Is=u_lightSpecular*u_mater' +
'ialSpecular*specular;}vec4 finalColor=Ia+Id+Is;finalColor.a=u_alpha;' +
'gl_FragColor=finalColor*texture2D(u_sampler,v_texture);}',
texture :
'/9j/4AAQSkZJRgABAQEAAQABAAD/2wBDABALDA4MChAODQ4SERATGCgaGBYWGDEjJR0o' +
'OjM9PDkzODdASFxOQERXRTc4UG1RV19iZ2hnPk1xeXBkeFxlZ2P/wAALCAIAAgABAREA' +
'/8QAGwAAAgMBAQEAAAAAAAAAAAAAAAUDBAYCBwH/xABkEAABAwIDAwUICwoLBQQJBQEB' +
'AAIDBBEFEiEGMVETQWGRoRQiMnGBsbLBBxUjMzVCUnJzotEWJDRUYmN0gpLhJUNTZIOT' +
'o7PC0vAmNkRVlEZ10/E3VnaElaS0w9QXJ0WW4uP/2gAIAQEAAD8AfYxtbh+G5o4bVVQN' +
'MrD3oPS77FnMMx2uxbaaiFRJli5TSJmjRoevyr0TKOARlHALMeyALYFHbT74b6LlNjG1' +
'uHYbmjhtVVA0ysPeg9LvsWcwzHa7F9pqIVEmWLlNImaNGh6/Knu34tgMdtPvhvouU2Mb' +
'W4dhuaOG1VUDTKw96D0u+xZzDMdrsX2mohUSZYuU0iZo0aHr8q9EyjgEZRwCMo4BZj2Q' +
'BbAo7affDfRcpsY2tw7Dc0cNqqoGmVh70Hpd9izmGY7XYvtNRCokyxcppEzRo0PX5V6J' +
'lHAJbV1ghx7D6QW93ZLceIAjzFMso4BGUcAjKOARlHAIyjgEZRwCMo4BGUcAjKOARlHA' +
'IyjgEZRwCMo4BGUcAjKOARlHAIyjgEZRwCMo4BGUcAjKOARlHAIyjgEZRwCMo4BGUcAj' +
'KOARlHAJbSVgmx7EKQ29wZFYeMEnzhMso4Bed4njtdhG01aKeTNFymsT9WnQdXkWjwfa' +
'3D8RyxzWpag6ZXnvSeh32qH2PxfApL6/fDvRatPlHAIyjgEZRwC87xPHa7CNpq0U8maL' +
'lNYn6tOg6vItHg+1uHYjljmtS1B0yvPek9DvtUOwAvgMl9fvh3otSLE8drsI2mrRTyZo' +
'uU1ifq06Dq8i0eD7W4fiOWOa1LUHTK896T0O+1Q+x+L4FJfX74d6LVp8o4BGUcAvO8Tx' +
'2uwjaatFPJmi5TWJ+rToOryLR4Ptbh+JZY5rUtQdMrz3pPQ77VLjWzuF4mS6TLT1LjlE' +
'jCAS7gRz+dZvDNna3C9pKV5yzwMls6WI3y6fGG8L0FCy/sg/AMf6Q30XK3jGzuF4q6Qu' +
'ywVLRmdJGQCOlw51nMM2crcM2hoZrNqKUvuJ4tW2sd/DzJx7IHwDH+kN9FyuY1s3hmJh' +
'8kgbTzgZjKyw8rhuO5ZvDdm67C9oqKUtE9NnuJo9Rax38PMvQEIWX9kH4Bj/AEhvouV3' +
'GNmcOxUuc5ghqbX5SPQ+Uc6zeG7N1+FbRUUrmiem5S4mj1FrHfwW/WIx+u5Pbug1s2HI' +
'w9GYm/YVt0IQoXThtTHBlcS9pdmtoLW+1fH1lMxxa+oha4bwXgELqOpgmcWxTxvIFyGv' +
'B0XHd1J+NQf1gXbamB0bpGzxljd7g8WHlXHd1J+NQf1gXbqmBsbZHTxhjtzi8WPlXHd1' +
'J+NQf1gXclTBC4NlnjYSLgOeBouWVlM9wayohc47gHgkofWUzHFr6iFrhvBeAQuo6mCZ' +
'xbFPG8gXIa8HRcd3Un41B/WBdtqYHRukbNGWN3uDxYeVcd20n41D/WBdmogbG2QzRhjt' +
'zi4WPlXPdtL+Mw/1gXUlRDEQJJo2Ei9nOAXxtVTvcGtnic47gHgkqGfEI4HyMLJHGO2a' +
'w4gnn4BpVxCELEYBXcpt3X63bNnYOnKRbsC26wGI7M1+L7R1kjGcjTmT32TcdBuHOtLg' +
'+zGH4Vle2Pl5x/GyC5HiHMqPsffAMn6Q70WrUIQsBiOzNfi+0dZIxnI05k99k3HQbhzr' +
'S4Psxh+FZXtj5acfxsguR4hzKj7H/wAAyfpDvRalGI7M1+L7R1kjGcjTmT32TcdBuHOt' +
'Lg+zGH4Vle2Pl5x/GyC5HiHMqPsffAMn6Q70WrUIWAxHZqvxfaOskYzkacye+ybjoNw5' +
'1pcH2Yw/Csr2s5eoH8bILkeIcykqKaRtbykbH5XTAmwBv4GpvzWadegKbDopI56hzg4M' +
'Ng3O2x8JxPPr4W/13TBCy/sg/AMf6Q30XJtUgirlf3NJI1sTrtA0ebC3jva2vAeW3RNy' +
'0sdw4Ei5zNym5NzpzarPeyB8Ax/pDfRcmzopm1tVLLFnje1uTIbkgB2hFrc5PjIVuha5' +
'lFCx4ILWAa7/AC9KnQhZf2QfgGP9Ib6Lk9dTtNZKXMcWysa0kE2Ojgb9FipaRpZRwMcL' +
'ObG0EcDZTLybaOpMu0lZM06tmyg/N09S9VglE8Ecrd0jQ4eUXUiEKN8eaaOS/gA6cbrI' +
'YrsXU1+J1FUyriY2V5cGlpuFc2b2XmwaulnmqI5WviMeVoI3kH1K39yOB/iP9q/7Vdpc' +
'Fw6jpZaaClaIJTd7HEvDj5br57RYT/y2l/qmqx3DScgyDuaHkmeCwsGVviC59raH8Spv' +
'6pv2KZ8EMhBkijcQLAuaCvjaaBjg5sEbXDcQwAhdGGNxuY2EnnLQhsbGG7WNaegWRyUf' +
'yG9S+hrQLBoA6AjK35I6l9sLWsEZRwCLIsFSraKSoe50bmjM0N77xPB9PsV5CFHPKIIJ' +
'JXbo2lx8guvKtnKkxbSUczjq6bKT87T1r1lCWyMcGTs5aS/K5gL6kXaerWyU+x98Ayfp' +
'DvRatQhC4lc5sT3RtzvDSWtva54JTE48lSCRzxMJbBj/AArZ7HcSL2159Lqh7H/wDJ+k' +
'O9Fq1CRiSDkGs5azTOBES4gDnvrrfePnBU/Y++AZP0h3otWoQq9a0ugbZzm2kYe95++G' +
'iTF8nte7v2cqJCct3DM/J5dQ4+UjmK0KEIWd23pKiswaOOlhfM8TtcWsbc2s7VaJCzu2' +
'9JUVmDMjpYXzPE7TlY25tZ2q0SEIQs7tvSVFZg0cdLC+Z4na4tY25tZ2q0SEJVJs3g8k' +
'jnvoY3OcSSSTqT5UyhiZBCyKJuVjGhrRwA3LtCEIQhCEIQhCEIQhCEIQvijfUQs8OaNv' +
'jcAq9RXYbJC+Kespsj2lrg6ZouDv50nFPslBI17X0Qc0ggia9iPKr7tpsGZvxCLyXPmC' +
'hdtdgbf+Nv4o3/YvtLtXhVXVxU0EsjpJXZW+5kC/lUGxFJUUeDSR1UL4Xmdzg17bG1m6' +
'rRIQhCzuxFJUUeDPjqoXwvM7jle2xtZuq0SFndiKSoo8GkjqoXwvM7nBr22NrN1WiQhC' +
'Fjm7fQCRzZaCRtja7JA71BXItuMIk8MVEXzowfMSmVBtBheIztgpaoPldezC1wJsL84T' +
'NCEIQhCEIQhCEIQhCFw6WNnhSNb4zZQvr6Nnh1cDfHIB61E7GsLZ4WI0o/pm/aoXbR4O' +
'zfiEHkN/MoXbWYI3fXNPijcfUoXbZ4K3dPI7xRO9a4+7TDD73FVyfNi/ej7rone9YViL' +
'/wCiH2o+6asd71s/Xu8bSPUj2+xl/vezkw+dLb1I9s9pX+BgcbfnTj7Qjurax+7D6GP5' +
'z7+ZyLbWv58Oj/a/ejuTat3hYlRM+bHfztR7VbRv8PHmN+bAP3I9oMXd75tHUfqx2/xI' +
'+5iqd75j+IO8TyPWj7kIHe+4niL/ABzD7EfcVhZ8N9VJ86X9ykbsbgjd9M93jld9qmbs' +
'pgjN1C0+N7j61K3Z3B27sOg8rb+dTNwfDGeDh1KP6Fv2KZtDSM8Clgb4owFM1jGeC1rf' +
'ELLpCEIQhCEJDtBtK3A6qGF9KZhIzNcPtbW26ypRbe4a732nqWHoDSPOrsW2GCyb6p0Z' +
'4Pjd9idwysnhZNE4Oje0Oa4c4O5dE2F14mTck8V8Wg2IbfaWA8GPP1SvTlTGKUZrZqPl' +
'vviFnKPjym+XiNNfIoDtBhYw+Ov7rHc0snJsfkdq7XS1r8xVj2zo/bB1Byw7pYzlHMse' +
'9bxJ3L7h+IUuJ0/L0cvKxZi3PlIBI4XGqtIQhQVlR3JSSzmN8nJtzZGC7j4kj+6md3vW' +
'BYi7+jI9SPuhxV3vezlT+s/L/hR7b7Qv97wAN+dOP3I7t2qd4OF0jPnSg+ZyM+1r/wCK' +
'w6Pyn7Sjufax++toI/mtJ87Ue1u0z/DxuFnzYR9iPaPG3++bRSD5sNvWj7mq53vm0Ncf' +
'mkt9aPuSDvfcXxF/9Kj7jMOPvlRWyfOlH2LpuxeDDfFK7xylTN2SwNv/AAQPjkefWpmb' +
'NYMzdh8PlufOpW4JhTd2G0nlhafUpm4fQR2y0dMzmFo2j1KZsMTPAjY3xNAVLaCWSDAq' +
'ySF7o3tjJa5psQuKbaHCaqoZBBWNfK82a0NdqepNEIQhCEIQhCEIQhCEIQquIYhS4bT8' +
'vWS8lFmDc+UkAnjYaL57Z0ftg2g5Yd0vZyjWWPfN4g7lXG0GFnD5K/usdzRScm9+R2jt' +
'NLWvzhTnFKMVsNHy33xMzlGR5TfLxOmnlVxee+yI7+FqZvCC/wBYrJoXsODNy4LQt4U8' +
'fohWah2WnldwYT2LxVC0mwbb7Qg8InHzL0pZ3amLuSWix2IWkopA2Yj40LjZw8l/Os7V' +
'Uwgoa3DgO8gxiN7BwY/d2KesMs1Vjb4DaoxCsZh0TvktAGc+Ky21FSRUNHDS07csUTQ1' +
'oU6EIQqbqtwxiOjyjI6B0pdz3DgLdquIQhCEIQhCEISvHPBoP02LzlNFQxymlq8GqqeB' +
'uaWSMhova5U0eH0UTw+Ojp2PbqHNiaCOxWUtwV73iuzuc7LWSgXN7C+5MkIQhCEIQhZj' +
'ajaWpwWtigghika+POS+973I5j0Jns7icuLYUyrmYxj3OcLMvbQpohCEIQoK2kirqOWl' +
'qG5opWlrgsTRmWGqwR85vUYfWPw6V3ymuHeHxWUFNTCeho8OI7yfGZHvHFjN/YtFstF3' +
'XLW45KLvrJC2En4sLTZo8tr9S0S859kF18diHCnb6Tll0L2XDm5cOpW8IWDsC+4g7Lh1' +
'S7hE89hXjKFqPY+bfHpDwp3H6zV6Mq2IUra3D6ildumjczrFlh4nmYRvf4c4w97r/Ka8' +
'sPaFfwGPujFqZ5F8hq6sji58hY3sBWijrH1FNUGppKmhYxly+RzbkWNyMpO5WKIOFJHn' +
'Jva9i7MQL6AnnsNFYQhCVu/3pj6KJ3ptTRCEIUU9Qyna0yZu+dlGUE/63KVCEIQhcSF4' +
'jcYwC+xyg7r8yzuTaKtqaRtdS0ccEU7JXOjcb6HpJWlWW2/mfFg8Aje5pdONWm2mVywH' +
'dVR/Ly/tlb32PnPkwypfI9zjy1gXG/xR9qkZU4zh1TWR0+DmoikqHyNk5UNuCeC0kZc6' +
'NrntyuIBLeB4LtCEIQhCELz32Q/hem+g/wARWXZPKxuVkr2jgHEL1XZhzn7O0TnEuJZq' +
'SekpqhCELmT3t13ZBY99w6Usjq5qakiMNFVVjZC4tyPaS1t9Ll7gdR1JFj0fc+LVMgFs' +
'5pasDg5koY7sIVCV5gEj2eHB7YPbb5Tnhg7Stxh9K2iw+npW7oY2s6grK8128dfaEjhC' +
'0edZtC9ppm5aaJvBgHYq+MOy4NXO4U8h+qV48haz2PG/wvUu4QEfWC9CQsNyXJ4g2K2j' +
'KlrB4hVhw7HhMtj2ZhJMeanhYPLmefTC0r2NkYWPaHNcLEEXBS7HWsg2drmxNaxogcAG' +
'iwGi8uixGtg95rKiP5shHrVyLabGYfBr5T8+zvOFrdjccr8WmqWVkjXiJrSCGAG5PQtW' +
'lf8A2pHRRf400QhC+KniVM+pZFyYu5jiR31stwQHeQ2KuoQhCEIQhY72RXWoqNnGRx6h' +
'+9YJejex+3LgMh+VUOPY1ahCEIQhCjkmjiy8pI1md2VuY2ueARNKyCGSaQ2ZG0uceAAu' +
'V2CHAFpBB1BHOvq8/wDZBje/FqYtY5w5DmF/jFT7N7LUGJYQypq2zCUucCA62gPCy2ND' +
'SRUFHFSwZuTjFm5jcqwhYL2QZpI8TpOTe5hEJN2m3OVnYsaxSH3vEKkDgZCR2q7Ftdjc' +
'X/GZxwfG0+pej4dK+rwqlmnDS+aFrni2hJAJ0ViKKOFgZFG2Ng3NaLBZvbFmURzD8XmY' +
'fJlePQKWclymIOh5n1LmHxGrLj2MK3SF5jtw6+0sw4MYPqhZ9fQLmy9sAsAOCjqYGVVN' +
'LTygmOVpY4A20IsVnJdhMLf73LUxnocCO0KlL7HzD7ziLh0Piv60x2Y2bnwSrnllnjlb' +
'IwNGUEHfdaRCx2Ijksdl4Coa4ft0p85Ku7N8vT4LJJTU4nkJiaGF4ZcCGNu8+IplWyOk' +
'wdzqxjaaQg966S7Wu1tdw8h8dlFtM/8A2ZrHWIvFuO/UheUIW19jhvumIO4CMektykla' +
'3lMbqW5nN/g7e02PhlR7Q4xiGD07J6XD21NO2PNLIZMuTyc6RUPskwuL+76GRg0ycgQ6' +
'/G9yE+wHauhx6rkpqWKoY9jOUJla0C1wOYniruNV3cdGQw2lk71tjr0notx5lHs9V8vQ' +
'iJxGeGw/V+L5ObyK1SRS901FTPcOe7Ixl7hrG3t5SST5QOZfa0PJgEcz43OlA721nDeQ' +
'bjgCrShqml1LK0Oc0lh75psRoqT6vEY8rafDWzxhrbSGoDb6DmsipxSWhwiavrqJ7OS1' +
'dFE8POXTW+g5+xRO2ipRUYZFycpGJMzQvAFhpex136hct2lpHNxU8lMBhhtLcDvt/g69' +
'HPbevjtpaYRYY4QTl+JG0MYAuBpqdd2oTtCxHsju+D2fSH0ViF6bsM3Ls5GflSPPbb1L' +
'QqOeUQQSTOBLY2lxA36C6VfdHS8nn5GbxWF/OrU1ZVmGCWioe6GyszHNMGFtwCOY3Vun' +
'fI+BjpouSkI75mbNl8vOpEIVLET7yzknuD3gOc1hdlbvO7dewC+YpLG/A6yVrg6M00hD' +
'gbgjKVU2drIjh9DQOf8AfDKGGUg87SLertCcqGaqp4HBs08UbiLgPeAe1R+2NF+OU/8A' +
'Wt+1WGPZIwPjc17Tuc03BXSF537Ibr41AOFOPScsqhex4U3LhNG3hAwfVCrRyujmrnUc' +
'YqHjKeTz5buzODrk7jp1AJftGZ6jBWSVNOIJA6VpYHh+hhkbvHjVLDhyuORcDUOcf26o' +
'+cBbFCSYpsth2KVT6mflmyvtcsfbcLbiEql2ApT7zXTM+e0O+xVHbA1DJGujrongEHvm' +
'Fv2rdoQhCELI7QDJi8jvyY3dckY/wJxs0zLhDOmR/Y4t9Sam24215ik22DsuzFaeho+u' +
'F5Whbn2OG+5Yg7iYx6S2qVs12pl6KJnpuVjFW0b8NnbiJApC33UkkC3jGqyPcWwP8pF/' +
'Xyfam2zlPszFWyOwN7DUGOz8sj3d7ccTxsq20Jm9sXCYWYG+58Mv/n5rn4oUOEVfcVex' +
'7jaN3ev6AefzG51K2L3tjY573BrWi5J3AKvRSOq6aOomiDCSXxtO8NNw2/TY9q6fVNZW' +
'Mpi03cL5riw329EqdCqVMc9U+allhi7iliLC/lDnJIsRltby3WEgL2YTgbpffcNxXuV/' +
'QC6/2Bd4ieQodsCN76mNo8rv3pvQUgl2xght7nhFAyMDg9wt5j2LWoWC9kV162iZwjce' +
's/uWOXqexrcuzFH05z9cp2oqpnK0s0fy2Ob1hYTOtc/E6DC4oKarqGxPETbNIO7d6l9O' +
'LU9TRSy0EvLFrms71tyC4gbjbirlNIZaaKQm5cwEm1ubhzKVCgpag1DX3jLCx+Ugm/MD' +
'60mx+J9FgGJRxOBZUkMhZztdIcrh4rm/lKVQVkGGYljOMSDNBQsjoYQN7iAMwHlstjTz' +
'x1NPHPE7NHK0PaeIIuEnxzAsHxiqj9sHZakMszLLldluebx35kiqfY1pHXNLiE0fASMD' +
'/NZarBKB2F4RTUT5BI6FuUuAsDqr6F5vt+6+PtHCBo7Ssyhez0TctFTt4RtHYpha5Atf' +
'eUr2lZmwh3RIwdbg31pNs+M+LxHg2R3VJIP8a1yEIQhC+EhoJOgGpUdPO2oiztDm62LX' +
'CxClQhZTagWr3u/Mw/3hPqTKh7ujwqIUMVO9xkkLhM8tABe46WBVhzap2JUj5Ym5RGQ5' +
'zHXDXEHN4xoLFUttXW2ZqRxLB9YLy9C3vsdN+8qx3GRo7FsUri12nqeikjH13qbGY6SX' +
'CalmIPMdK5lpHA2sFifazYX/AJjJ+27/ACp1srSbOU9fK7Bap005iIc0uJs2413Dnsnm' +
'K0Ar6QsFhK3Vh6eH+ug8yxxaWOyuBBBIseY8/l6zxWrwWpbXYcYZgHujHJva4XuOa48W' +
'nkKYyyMhifLI4NYxpc5x3ADeVWdTGWvhq2luQMG9pzbnaeXN9UK4hUC/Fu67CCj7mz+E' +
'ZnZ8t99str26VkMaj7mfj8Y0bFVU1Y39YgOPWjEY882LQ/y+K08fXYp/s03la/G6075a' +
'wxA8RGLDzlP0Lzz2Q3XxmnbwpwfrOWUXrOy7cmzlCPzd+s3TVfFgMn3xk5lv1xNE2aMx' +
'vvY84NiDzEL7FG2KJsbBZrAAPEu0KhTuNJVCCdwc+pLnte1uVpIAFvnEC/kPBLNqKxsM' +
'9K1wzMpQ6skbxLe9jHle4dSQNoWTOgoK6QNocNb3XiUh3Pmdrl6bXt1rQbN7RvxyqqWs' +
'oZIaOMDkJS02dbQgndfdoFT2w2TqcfrIqmmqIozHFkyyA66k3uPGs19zW1uGa0kkpaPx' +
'eosOokeZbrCZMQbg2Hd2FwqHENn5Qd9z/uThC8z26dfaN44RsHYs6he1xNyxMbwaAqMT' +
'aoYjXPETbujZyRc7vTbNYHhqT1qvXd2yYVKK+OnY4SRloheXAgPadbgJbsuL18bvzM39' +
'4D61q0IUU8phiLxG+Q6ANZa569F3G9skbZG6tcAR4iukLl1w05QC62gKr4eyVlK0TtaJ' +
'SSXlrswJJ1N7D9y+Ye5xE7XSOkDZO9cSDcFoN9ObUrumqO6HzgNs2KTID8rQX7SR5F1H' +
'OJJ5Ygx45MgFxtYm19OtZzawWlv8qNg6nEp9hYth8PSCes3U5miEwhMjBKRmDM2pHGyT' +
'7YU1RV4FJDSxOlkL2nK0XNgV5xLhlfB77RVDPnROHqVUgtNiCD0r0D2O2/wTUu4z2+qF' +
'rUrp9dpa3opoR2vU2NOo2YRUuxBhfSBnurRe5HkWF7t2D/5fP9f/ADJ5snUbNS4hK3BK' +
'WSGcREvLs2rbjiTz2WsWc2joMju7Yh3rrCQDjzH/AF1aqhg9Z3FXMc42jf3rxwHHyLVV' +
'lKKtjI3uIjDw57beGBrbxXt1W51YS7D3O7urGk3AIN897nM/m5tLDyK7JNHEWCSRrC85' +
'WhxtmPALir7q5H7yMIlv/HAltvIsltZERVYhe15sLzG24ujkB9a4lHK4w78vGYHdUOZP' +
'NkG32fhmPhTvkmPTmeT5rJ2hebbeuzbQW+TC0ec+tZpev4E3JgVA3+bs9EK+hYXL9/Zf' +
'1e2y1dXg9HWTmaYTF5AByzyMHUCArckkdPFnle2NjbDM42A8q7X1UJpJPbenjyyCPK7v' +
'gO9JIO/xWHWrU8EdQGCQXyPD2kGxBBv/AK6FjK6r7pr5aprOVHKNfHH/ACmUlsDP1n53' +
'+IBVJY6buYsr53HDKWUuqXs8KvqjqQ3iBu/1cMI5trq1rH4dSU2GUjQOThkAuW8CLadQ' +
'Xza2r2kpqulmwuOobGYByrYmCRrX3NxuPWkkW3+N0rslXTQSEbw+Msd2H1Lf4RWDF8Jp' +
'a2SIMMgz5Ab5TfimCF53thheIVGPTzw0c8sRa0B7GFwPejgs3LTVEPv0Ekfz2EKONuaR' +
'reJAXta4E0RnMIkZyobmLM3fW424KDFBfDpugA9RukOyYvID8mN463NK0FRVcjVU8FmX' +
'mvq5+XdbcLanXoX2rqDByQa0OdJI1uvMCQCe3tCgxgxtoXF7gHA3ZckXdY28vDpspq9k' +
'slI9kJYHOsDncWi19RcDTTRTsBDGggA23DcF0hKdncV9s6Fwl72qpnmGdh3hwNr+X7Va' +
'woFuHxAgg66HxlfcOLDBIY4mxjlpGkNG+zi2/YvlDFTxSVXc4IvLZ4PGw3da+U0VsRqZ' +
'A2Fu4OLPCdexGbxc3jSfa0awdIPZ/wCaYxR178Nou4aiCH3FuflYS++gtazhbnUzmyDF' +
'GObyurAH6e5lozdtyPIVeQo5IYpRaWNjx+U0FfIKeCmaW08McTXG5DGhoJ46KVK6T/eL' +
'EjwhgHpqzijpWYbO6npm1Uob3sLtzzwWS7rx3/1RpP2Wpts7PiMtbIK3A4MPjEdxJGBd' +
'xuNNP9aLRLiSNssbo5BmY4WI4hYqvpHUNU+B24atPEcVpcCre6qEMcbyRd67pHMf9cFP' +
'UCeaugiZmZBH7pI8aZjua31nxDirYaASQACdT0qnXm8tMzknuDni72sLsoBBtpuuQPIC' +
'oHwY2XuLK+iDb6A0riQP20o2pikAg5ZzXyuoaqN7mNyhxyB2gubeCl2e1dynCq5Xqo7r' +
'V7Pxcjs/h8fOKeO/jyi6YoXmG2zs201QPktYPqg+tIF7LhzcmG0rPkwsHYFZQsWR/DWT' +
'+c5f7VaSphxZ1Q40tZSRwm2VslO5zhpzkPHmUVZDWOwzJUyiWXPq+niIIFtLNJPPoegp' +
'my+RuYAGwuBzKOpqBTsa7I+Qudla1lrk+UgIq4TUUz42yOjeR3r272nmPWluKV8sWCAz' +
'A09RMDG62uTQ53DxAOI46cVlyclyXCnLcxLhryNgGucPmNyxt4uLk+wPBGl0NdWQcnyT' +
'ctHSndTs4ni87yVolmdpdrfufxGGndR8uySLOXCTKRqRw6FVj28wOsbkrKeVgO8SRte3' +
'sPqWmw6WknoIZaAMFM4XjyNyi3i5laQhfFBJQ0kpvLSwPPF0YKsKi1sgxSQs5UNcwl2Y' +
'd5ezQCOnfp+5Qyx17MNre7qiCb3F2TkoSy2hve7jfmS7ZIazfkgDrP7k7qo+UmgaZgxp' +
'dfIW3LyCHCx5ty+VsVLK6nFTE17uVHJktBLXDvvJ4KkrJjBE14aHXljZY/lPDb9qixUF' +
'2HygAk6aDxhVdosV9rKFoi76qqXiGBg3lxNr+T7E2QsPHO7Eax2NbPPY3EY+8rKF7rCY' +
'DS/7/Xve4ZtPQVr+QnLqKsGjqeo7xwPQTvTangbTxljSSHPe/X8pxd61xSwuikqXOtaS' +
'XO23DKB6lzTRvZWVb3NIa9zS08e9ASfasX7l/X/wpzh3wdS/Qs8wVlCELgSMc8sD2l4F' +
'y0HULtIpHxR4ljD52h8Yjp7tJ0J76wPRchVNpKrFqPBo2YXGx8Hc4z1fKhhj3WIuVlMK' +
'rK+qdKMR2pfQhoGQ8sJM/Hc7RavZfL3fLbaV2K+5H3Eg97qO+3nxeVahCW43Qd2UmZgv' +
'NFq3pHOFncJrO4q5j3G0bu9f4itmvqFxKHuieInhkhaQ1xbcA8xtzrPbQwzNpKDumVs0' +
'ofLG57WZAc0MnNc25udIJn5YJ5OcRPd/8kwetb6mj5Kmij+QwN6gpULyra12faatP5TR' +
'1NASde1RNyRMZ8loC7Qsfl/2kt/OvXdaGXEzHVGDuCtfZwHKMiBZ4733K+hVMRpnVVMY' +
'mMicSf4zcLgi46Re67pKkVDZBlLHxSGN7TvBG7rFj5VmMdrO6MQdlcBHD3jXWuAQbk25' +
'9W3t+bt8ZS4DhYqZhPMy0ELhZhN8z27gTzhmvjeXHmC1SFjtscFbiWJwyvpayZrIcpNN' +
'JHcan4rtT41lanZ3D4/CrayiPya2icB+024Xo2zMLafZ6iiZNHO1rLCSMnK7U7rq5X5x' +
'SPMbyxwsbjfa+5WVwZI2vDC9oeRcNJ1K7QhCrYj8HVX0L/MUn2UFu6v1P8Sb1Mb31lI9' +
'rSWsc4uPDvSF1VQulkpnNtaOXO6/DKR613UQNqI2scSA17H6fkuDvUlOJ7T0FC/kICa2' +
'sOjaen79xPTbckUk7sOrG41tE9jsRf3lHQsdcRA6X/f6924QsBWx7KV9Y6qo8VdhlaHG' +
'7mAsGbjY+ohfZJJ5oxFUYxs/i0Q0Hdbgx4HQRuKKdz6bSlvTAc1Ji8b2fsyaK9FjWJR/' +
'8Q9/0sUD+1krfMrLNo6weHDC/wDVc3zZksxXEMUxqthpqCmpg+OJ0rg9z91wN5a3oVvD' +
'do6x+GUxEUN+SbzHh41M7aDEuaGAjoab+dQvxzEXc08f0cbXecKF+JYg/wAOqqLcHU7m' +
'9rCFXkkll98fJJ0PdKex8bgnGyrQ2ecBoaMo0DQOfoa0di0qV0Hw9ivihH1SrOKMkkw2' +
'dsNPFUyFvewy2yv6DdZLuDFf/VTBetn2pts7TVkNbI6pwXD6BhjsJKbLmcbjQ25vsWiQ' +
'hZTH6DuWp5eMWilN9OZ3OE22fre6aPkXn3SHTxt5vsTVCrVsNRPG1tLVmmcDcuEYfccN' +
'UqxiCaPD6VtRUGokFU3vywN3gt3DxrM25Slt8uEDrp6cf4l6IhC8j2jdn2grz+ecOo2V' +
'GnbnqImfKeB2r2pCFk8v+0//ALzfsWkfW0jHFr6qFrgbEGQAhEtUBFFJCBKx8jWZmuBA' +
'BNrr5QVLqqEve0NII0BvoWhw7CrKXYpLHQQTVUYtUTARgjW5F7G3PYEnptZZihpJK2qj' +
'hiJaTqXg3yAW1vz272x57RnmctpBDHTwMhhaGRxtDWtHMApELJ7WMpn4jC2ZmFvdyWgq' +
'pnQybz4LhpZUYo6uJmeCHGIWDcaOtZVM/ZOq1OHVLY8PpBO+YySCwMsORxN7d80eDvA8' +
'qYOAcCHAEHmK+rNbVMD54AWhwynQtB5+lrh2JPHJLF72+RnQx0oHUyNoVhmJYgzwaqot' +
'wbTud2vJUzcbxJu4TyfSRtb5gpW7Q4l8aGAeNpv51FiW0dW3DKkmKEnknfFPDxqphWIY' +
'rgtdNTV9NTOfLE2VoY5+65G8Nd0pm/aOsPgwws/Vc7z5VWkxrEpP+Icz6KKBna+U+ZUZ' +
'3PqdKrNUg81Xi8bGfsx6IjknhjMVPjGz+ExHQ9yOD3nxk718oo9lKCsbVVmKuxStLhZz' +
'wXjNxsL9pK36F5/XnZLDqt1NT4c/E60uN2Ruc/vuF726gV0+GWOISy4FgWFQnca4hzj5' +
'BrdcwMFTrStiqBxosGbl/bfYK9HguISa9yOZ9IaaP0Y3KyzZusPhvp2frZvM1qW4nQ4p' +
'gmIQz0E1K980TojmY4AC4POTwVnDNnK0YXS5ZYNYmnVx4eJTO2dxH+Xi/VcfWFG/A69v' +
'8XUSfNla0edQPw2tZ4dHK3xyyP7GBV5I3RayRmPpfG5vbJI1OtlbGectIIyjVtiN/EEj' +
'tWlSvD/hvFvnRD6imxmNk2E1MclNJVMcyxhjNnP6AsR7U4f/AOqGJ/1zvtTvZWipabEJ' +
'XwYFV4e4xEGSd5cHC401O/7FqkIUFbSsrKV8L9Mw0PA8xS3AMOfS8rNO0tkJLAOgHUpy' +
'uJHtijdI++VoJNhdEUjZo2yMvldxFilOIU80OGxioqn1ThWQEPexrSAZWC3egdKy9GMz' +
'KVvymwjrbRj1r0FCF47jDs+MVzuNRIfrFc4W3PitGz5U7B9YL2RCFl7f7S3/AD9+xPZM' +
'MoJZHSSUNM97jcudE0knx2UvcsTYGwxMbFG1wcGsaABZ193jXNJTCliLA8vud56AAOwB' +
'WFkserO6q4sabxw3YOk8+7xeMWBCd4LQdxUuaQe7y6vvvHAdpJ6SUyQhZnaiVzKmMOnk' +
'ii5O5DsP7oiOp3neCs8xtFNJmjjwOZ/NyE76OTqOi2NLFM7C6KNsLxZzXOzVAkLLOB1d' +
'8YWv2eRskmymKVGL4W+oqsnKCUsGQWFgAfWqu1RDZ4C4gDKdXWA38SQO1JY4nS6xxmTp' +
'Yxzu2ORyssw2tf4NHK4dEsjOx4UzcDr3bo6iM/lTNcPOpG7OYjv5eP8AXcfUFDieztac' +
'Lqs0kGkTj4R4eJVsMocUxvEZp6+ala+GFsQyscQRcnmI4pk/ZusHgPp3/rZfO1yrS4Li' +
'Eevcjn/Rmmk9KNqozsFNrVNipxxrcGbl/aZcLpkMskRliwLAsVhG80JDXDyHW65oTsli' +
'NW2mqMOfhlaHABkjnM77he9usBegIWHigOF1jsH2fjZJiknf1da8XEIOtv3efmeYbsvQ' +
'0j+6KrNX1h1dPU98b9AOgTvduQvqzu1B93pejN5k4wz4LpPoWeYKyCDexBtpogEEkAgk' +
'aHoRcXIuLjeEXF7XF99lw2GJkhkbGxr3CxcGgE+VSJXh3wzi/wBJEP7NqnxhpfhdQ1ra' +
'hxLd1M7LIfmnisX3HN+K7Vf9SE52Xgkir5S+HGYwYiL10oezeNw4/vWoXwkAXOgUcdRB' +
'K4tjmje4czXAlSoQuJS8RPMYDn2OUE2BKWjCppI4/wCEKylsLcnE5hAFyRclpJNiBe/M' +
'uK6kfS4VIH1c9SeWifmmLSRaRp0sBwWaw9t56Nv5UQ6u4z6lvUIXi9W7PWTv+VI49qt7' +
'Ptz4/QD8+w9RuvXUIWZt/tRl/OX7Lpv7XS918v7ZVuXPm5G7Mlr+D4N7eVXkb19SSnwQ' +
'R4s6VwBp2WewcTzDyb+q3OE7QhCzm0UdQ6vidTxYg60erqKraxw1O9h8LxpHU1DwLVtT' +
'O1o+LimFB4/bathgZYcHpjEacsLdO5wRHv8Aig6hX1l/Y++AZP0h3otWkdDE+QSOjY57' +
'dA4tBI8q7uL2uL77IuLgXFzuCCQCASAToOlBIFrkC+mqrYp8F1f0L/MUn2XPu9V05fMt' +
'EviN+9JMS2Xoat/dFLmoKwatnpu9N+kDQpHLAcTrG4PtBGyPFI+/pK1jbCYDW37vNz7h' +
'CUbOYV7W0T3y99V1LzNO87ySbgeQetN0JbjAJFORkLs9mB9/C5iNN49ZUzJMQNcWPpqc' +
'UtzaQTEvtzd7lt2pPtQfviDob6nJzhfwZTdEbR2IoGua2ozNIvO8i43i6KNrm1NcXNID' +
'pwWkjeOTYNOooia4YpUuLTlMUYBtobF9/OhrXe20j8pymBgvbS+ZytoSrDSBimLuJsOW' +
'Zr/RtVnFo3y4ZUMjjllc5ujIpOTcfE7mWP8Aayu/5TjH/wAUam+zVHU09dI6air6dpjI' +
'DqisEzSbjQAbj0p7X1sOH0clVUOyxxi54ngB0pCzCq3H7VOMSyU9M7WOjjNtOYvPH/Wi' +
'r0Wy2G1FRiDGtlhdBOGxPjkN2jI08/SSr1FV1uEVseH4rLy8Mpy09Wec/Jd0rQIQhKcS' +
'pRT4ZWvE00mfv7SPzBtjezeAWdw1t8RpW/nQOpkB9S3CF8ccrS47gLrxMm5JO8ptsq3P' +
'tJQj8u/UCV6uhCzlv9r7dN/qJhLPjQleIqGidGHHKXVTgSOa4yaLqqzOhppa1vJluflG' +
'RvLmgZHX1sLqbC8hoIzHkyOLnAM3Nu4mw8W7yK2hCEIXnHslSStxalYxjS0wb+TBN8x3' +
'G11n6er2joohJDJiMcXMSH5O3Rep7M1M1Xs/RVFS7PK9l3OsBc3PBNFnNhqeemwSRlRD' +
'JE8zuOWRpabWbxWjVRzXe20b8pyiB4vbS+ZqJWuOKUzg05RFICbaC5ZbzIrGudU0Ja0k' +
'NnJcQNw5N416wiva5zafK0m07CbDcLoxT4MqemNw7Em2XP3xP0t9TU4fJiArgxlNTmlu' +
'LyGYh9ufvctu1Q4QC01IGTKH2u2/fO5ybjfuumSEo2iwr2yomPi72rpniaB43gg3I8o9' +
'SboQhC5c9jLZ3NbmNhc2ueC6WY2qP3zF9GT508wr4Og6G27VHhr5XS1DZM5s693OzAkk' +
'3twGg0XzD5JH1lWHucW3727r/GcN3NutbovzlSxM/hKd4fIWhjQWl5Lbno3DQDdxKjbJ' +
'IcYc12bkw3K2z9NwPg9evkV9CSU3J8rjJlAyd0tBu24HeM1I4DeVUxyTE2YRBFBQ009I' +
'6nHdBqJuTy7tL5hZZempqeQu5WjwKK27Nijtep5Wj2UpqeHEJXQswtrjEQe5Kx0zrXG8' +
'E6DpXWN1hkxmF0kfKYdh8rO6DzB7hoekN061qAQRcG4KWYT+HYt+lD+7YotqXRnCHUzo' +
'+VmqHCOBnPnO4+Tep8Cq5aqgyVP4VTuMM3S5vP5RY+VMkKKpgFTA6Fz5GB3xo3lrh4iN' +
'yW1lAyiwbEMk9TLmgcfd5nSWsDuvuSLCxfGKcfnz2RM+xbRCgrXZKGof8mNx7F4wnuxb' +
'c209Kfkh5+oV6ihCz1v9sPJ/9tMo8Yo5KvuZpm5XMW608gF/Ha3ar6EIWe2r2gODMgjg' +
's6eRwcQeZgOvXu607pKmKspIqmB2aOVoc0qZCzO00mWuiYJcpdH4IksTqeYSNv8AsuSQ' +
'sEJzuYIifjuYI/rFkfplbTB3Z8Kp3Zs12+FmzX145nekVdVXDawV+HwVYYWCVubKTeyt' +
'Ki6ok9thDd4iygeCMpcQTv33sPFoehST8r3bTiOZzWnMXsAFnNA37r7yOdR19RLDUUzY' +
'yQ17u+sAdMzRrfm77m1vbmujFJ5aaBskRcACScrM17AkA8ATzrvFfg6fpbbtSPZU/fMv' +
'TGD5lp1yx7H3yOa7KbGxvY8F0hCEIQhLsWY95psocbSA6NzXOlh0X481lKwYj3ceUNL3' +
'Jc2DQ7lLc3Qke1R+/Ix+YcfOneEG+GwnjfzlWo4o4s3JsazM4udlFrk856UMijje97I2' +
'tc83cQLF3jXQaASQACdSeK55KPleVyN5TLlz21twvwXaErwfWqxU/wA8PoMVjF7+1dRl' +
'uTl5oeV+p8bxLGe7fJk//r/7032de6KqnklDmsbCSS7DO5txHxr6+JMMDpWz4ETUsDjX' +
'ZppQefP+6y+YDNJAZsJqXEzUdsjj/GRHwT6ipcJ/DsW/Sh/dsVfDv4WxaTE3a01PeGl4' +
'E/Gf5dwU0Y7l2nlaNGVsAk/XYbHsI6k2QoaaobUsc9oIAeWi/PY71RxKjhgw3FJow/PN' +
'A8vvI5w8E7gTYeRI8KH8OxdFS/8Auf3LYIVLGnZMFrncKeT0SvHlo9hG5tomn5MTj6vW' +
'vS0ISC3+1/6v+BP1XmqmwytY9rrFjnl1tAB61JTy8vTxS5S3lGB2U81xeykUVRPHTU8k' +
'8zsscbS5x4ALyPFsQkxTEZquTTOe9b8lvMFp9gcXySPwuZ2jrvhvx5x6+tbpCzO07z3d' +
'FGHnWO+QOOup+KHa/sHx8ySECmOawp3HnsIj5oT2laillmbh+Hua+TkyfdHNc0k98AL5' +
'i4ka8xJ6U5SvZn/d2h+iCaKI08ZqBOW+6AZQbm3Vu5zr0rssaZA8jvmggHoNr+YLiWni' +
'mfG+RpLozdupHA+XUDqRPBHUBolaSGuzDUjXyeZQYsbYbMfF5wkeyp++5B+YafMnjxiP' +
'dw5M0vclxcODuUtz9Ciwhj2CozBwvJfVuWx5x02486YoQhCEIUU9TDT5eWlbHnOVuY2u' +
'eClWW2n1xFo4Ux85TrAzfCKc9B85V9CEIQleC++4mf5670WqbGS0YTUl5YG5NS+Uxt8r' +
'hqFiM9L/ACuHf/GZlapZomUOJuhfSud3I5vuNe+c6kDwXbt+9beCIQwRxN3MaGjyBKse' +
'hkgMOLUzS6aj8No/jIj4Q9YSk1xqpK2jw+S8uIVIDXj4kfJszO6lqaWnjpKaOnhbljja' +
'GtHQqGKd5iuEzfnnxH9Zh9bQmq+EXFks9oaJ8QjmbJI1jiWXleMoNtND0LmbC6Oiw6uN' +
'LG5rnwPBvI53MeJKTYPrjbT/ADl/90Vr0JZtI7Js9Xn8yR16LyRan2PW3xyZ3yad3pNX' +
'oqEJFb/a2/5P+BWX7P4ZI9z3QPLnG593kGv7Ssvw+J0UULczYo25QA43tpbW9+ZTUsPc' +
'9LFDmLuTYG5jz2ClWJ2+xewZhcLtTZ81uxvr6lh1JBNJTzxzROLZI3BzSOYheuYPiMeK' +
'4bDVx2BcLPb8lw3hXlmdp7vrYoxd1475Bd3OfijN6B8aSBwpzlDhATzAiI9QMJ7Cthhs' +
'Ec2G0jpWlzmDM03PHt3DffcExSvZn/d2h+iCaIQhCoY2cuEVB4AecJLsxpiLhxph5wtS' +
'ooamGoz8jK2TIcrspvY8FKhCEIQhQVLHSGFgF2mQF3QBcjtAUTIa4VxkfWROprm0Igs4' +
'cO+zepIdou+xV/RAB2lONnjfBafoDh9YpkhCEISvBPCxE8a2TzBWMWJGGVBaXA5dC1zW' +
'nrdoPKsdyk/8tVf9bSfYu3OmdhmIAyzH3EW5SeCQDv280Yv16Jv7X7Uf86p/6hv+VcTU' +
'e0sUL5JcbpmxtaS4mBtgOf4qzGzMNecSkZRVUdJNIw5XSRA5xobC400INuC1XtftR/zq' +
'n/qG/wCVQS0uNQ1+GuxLEIqmI1QAYyMNIOV2ugHNdapC+EhoJcQANSTzJdNBQltbPTth' +
'NRLC4SPYQXOFudI8C1xjxTuP1CFrkJLte7JszWn8lo63ALyta/2Om3xCrfwiA6z+5b9C' +
'Ej/7V/q/4Vdhp8TbVB81fDJBc3jbTZSRzd9m9SvoVTFK+PDMPmq5d0bdB8o8w615FVVE' +
'tXUyVEzs0kji5x6VChabYjF+4cR7jldaCpIAv8V/MfLu6l6QsftlX0tNiEMVTK1pMWYN' +
'eLg6n8h47FnWbQUEZyMmkYPyGODfquYPqL0LAZWTYLSyRODmOZcEc+p6B5kwUNI+GSli' +
'kpgBC9gcwBthYi405lMhCEJbtCbYJU+Jo+sEn2e73FWjjAR2hPnw1xrhIysibTXF4TBd' +
'x499m9SlpmOaJXvFnPkcT4hoOwBToQhCEIUU1RHBk5QkZ3ZRYE6+TcOlSrKY4b4rN81o' +
'7P3pts2b4QwfJc4dqaoQhCErwPwa8/z2XzhWMWBfhc4Y3OSzQBrXX8ju9PlWO7nqPxR3' +
'/SUf+ZWaalmkocSY6AsvSPseQgZcixGsZJ5vEtdRzCpo4JxqJI2v6xdKsZccRrocGiJy' +
'OtLVOHNGDo3xkqnJQPqDib6QBtVS1Ykp7cRGzvfERonuG1seI0EVVFoHjVp3tPOD4iqm' +
'Je6Y1hUPB8kx8jLDtcmqFzIxksbo5GtexwIc1wuCDzEKg2jw2m5dlJT0sM/JkOETGtdb' +
'ptrbckWzmuKuP5bj2Fa5Cz+27suzU4+U9g+sD6l5itr7HDe/xB/ARj0vsW5QhJbf7U3/' +
'ACP8KmdilW15AwWucAbXDorH665r5Kh3Iubnj5aMAxOflyHOy9y3n1tp5ExpX8pSwv17' +
'5gPfG53c6wO3WL911woIXXhpz39vjP8A3butZRCF9BIIINiF6rsviwxfCWSPN54u8lHT' +
'x8v2pHtrhMFfiEMswmu2HLdrmsaNTvJBWbZs5RuYXZ55B8ppDWDxuI18gXouzkLKfAaS' +
'GNwe1jLAg3vqedM1RwT4EoP0eP0QryEIQlO0pthDx8pzR23SnAzbFYPmuHZ+5axRQ1Ec' +
'+fkyTkdlNwRr0X3jpUqEIQhCFRxCnkmfA+IOJY62j8uW9tTxAtuXTKWqbXGZ2ISPhJNo' +
'DGwNHltftWcx11sXnHzfRCb7Mm+HP6JT5gnCEIQhI6Fsj8NxIQtzudWTC17XGex18V1V' +
'x9+MjCoY6GkpHwOgHL90lvenTTviB1rL0kVYS7uylwto0y8iykPXdwWj2WiaK6YGGnaD' +
'EQeTZTgnUfyZJ69FdwuujwzBamOpdrhr3REc7he7LeMEBWsBo5YKZ9VVj78q3crL+Twb' +
'5AjCfw7Fv0of3bFXZ/A+PGPdR4i7M3gybnH6w7VLRnuvaKsqBrHSxtpmni4987/CE3Ql' +
'zcVghfN3dUw04bIWsEnudxci93Hvr25l9gqcKqqiR9JNRy1TmHM6JzXPLdN5Gtt3YkOy' +
'+uJP8RK1yFmdvnZdn2j5U7R2E+pebreexy21LXP4vaOoH7VskISa3+09/wA36lehxTD5' +
'5xBDXU0kpJAYyVpcbb9LqxJFHK0tkY17SLEOFwQuty8826wjuSvFfC20NSe/t8V/79/W' +
'sqhC+gXNhqV6psthHtThLGPbaol7+XoPMPJ9qobUZfbCHwM3J6XYXu3nc3d5Sk79ZByn' +
'h83K+6P8jBoFrqCpbT4ZScqJiX2aCWXIJNhe2g3pkqOCfAlB+jx+iFeQhCEn2mNsOZ0y' +
'jzFKMCdfF4P1vRK0b6WqdXCZuISMhBF4BGwtPltftXOGU8lMyRjw4DNYXfmv0jgDwV5C' +
'EIVTFK+LC8PlrJmucyO1wzebkD1rP/d7h34tVdTftTPBNoqbG5JWU8UzDEASZAOfxFOE' +
'LF446+KTv/Kt1AD1J1ssb4dJ9KT2BOkKpimIRYXh8tZM1zmR2uGbzcgetZ/7vcO/Fqrq' +
'b9qZ4JtFTY3JKynimYYgCTIBz+IpwleAfgtSeNZP6ZU+MuyYTUuLg2zN5LQB+0COsLEd' +
'1t/Gof62l/8ACTrZWYSV8oEzH+5E2a+E84+Qxp7VJtBRxU+K0mJzZjRmRoqmjdcXyPI5' +
'wCfMtK1wc0OaQQRcEc6WYT+HYt+lD+7YotqnwjCHRSAunlcG07WeEZL96R4lbwehOH4d' +
'HC92eU3fK/5TzqSryFWZRRCSR8l5i8/xgByi5Nhpu1K5jloGVrqaJ9O2qDbmNtg8N423' +
'2Wd2VH8IyfRk9oWoqZhT00sxY54jYXZWi5Nhew6Ulp9qoZ6qGB2H1sRleGB0kYDQT5VS' +
'9kN1sIpm8Zwepp+1eer0L2O22wmpdxnt1NH2rWJBUbVQwVU0DcPrZTE8sLo4wWkjyp5G' +
'/lI2vsRmANjvCVf9qP6G6Ztp4WPztija7iGgFRVFdDTue12Zz2NDi1oudTYKy0hzQ4G4' +
'IuFUxWgjxPDpqSXdINHfJPMV5FU08lLUyU8zcskbi1w6QokLS7E4R3fiXdcrbwUxB13O' +
'fzDyb+pekrM7Tlwq4/Dycnr34Yzed53+QJK3SM5NI+fk/co/K46la3DI+Uw2gLMhYw5j' +
'lva1nWtfpITNRU0UcFNFFD71GwNZrfQDTVSrNVu2lBR1k1M+nqHPieWEtDbEjyqOPbrD' +
'5JWRtpqq7iANG8/lWpQku1Jth0f0oPYUlwN2XE4H/lW6wR61tEIWardtKCjrJqZ9PUOf' +
'E8sJaG2JHlUce3WHySsjbTVV3EAaN5/KtShZ/bh2XZqYfKewfWB9S8xW29jhvfYg/gIx' +
'6S3CFRmwihnc50sGZziXE53b+tT0tHBRsLKdmRp3i5PnU6Fn9uHZdm5h8p7B239S8xW2' +
'9jhvfYg/gIx6S3CV7PfgEp41Mx/tHKzimf2tn5PPmy6ZM+byZO+6lkvv7jiH/wA79ibb' +
'O90d2ycsarLyenK90WvcfymnVqn80Uc8T4pWB8bxlc07iFnRDi+z5LKKI4jh/wAWIutJ' +
'F0A84VWjxnEWVFaabA6t0tTNnbygLGt70DUkdCa4XhNR3X7ZYvK2attZjG+BCODenpTp' +
'CFy5wYwucbNaLkqlR4th1dUcnS1DJJct7BpvbqSPZYfwjJ9EfOFq0rxzwaD9Ni85VjEc' +
'Lo8UjZHWw8q1hu0Zi2x8hS/7kcD/ABH+1f8AarOAQ0cOHuFBTmCIyvu0uLu+Byk3J/JT' +
'NK8D8Gv/AE2XzhNEo/7T/wBCujgjSSfbHEhfhUlSVtA+aERttK1rGtLZXkF9jzm3lV6N' +
'pbG1rnZnAAE8Su1iNvsI8DFIW8GTW7HerqWIUkMMlRMyGJpdJI4Na0c5K9cwbDY8KwyK' +
'kZYlou93ynHeVeWY2nt7YQ2y5+T0tGXv3ncNw8aTO98773zmze7SfsjRq2eEX9q6fNmv' +
'l1z2vv57K6quF/BdJ9Cz0QrS8dxh2fGa53GokP1iucLbnxWjZ8qdg+sF7IhQVVHBWMDK' +
'hmdo3C5HmUEOEUMDmuigylpDgc7t/WryELx3GHZ8ZrncaiQ/WK5wtufFaNnyp2D6wXsi' +
'Fmdv3ZcAaPlTtHYT6l5ut57HLbUla/i9o6gftWyQhCELM7fuy4A0fKnaOwn1Lzdbz2OW' +
'2pK1/F7R1A/atklezvwVfjPMf7RymxosGEVPKWDcmubLb62nWsLnoflwftUv+ZPNk3U5' +
'xCXkXRF3JG+Qwk2uPkElaxCEIQhCr92Q929yXfy2XN727Lb51rdqz2zA/hGT6E+cLUqK' +
'aCKfJyrA7k3h7ehw3FSpLiOGYrU1j5aTGHU0JAtEIg62nG672Xa5mCsZI/O9ssoc61sx' +
'5R2qbqCmpY6US8nf3WR0rrnnO9TpV/2l/oFNTPxY1LRVQUbYNbujmc53RoWgdqvoQoqm' +
'njqqeSCZuaORpa4dBXkWK4fJheIzUkupYe9d8pvMVp9gcIzyvxSZvesuyG/Oec+rrW7Q' +
'vP8A2QX4kMVp20RnEXIXdkvlJzHfzLLGbHC2wFQxvBrMgPkAF16nsuZTs7RGdpbLyffA' +
'tyneeZNlVwv4LpPoWeiFaXi9Y7PWTv8AlSOParWANz49QD8+w9RuvXkIQhCF4vWOz1k7' +
'/lSOParWANz49QD8+w9RuvXkLJ+yI62E0zOM9+pp+1eer0L2PG2wiodxnI6mj7VrEIQh' +
'CyfsiOthNMzjPfqaftXnq9C9jxtsIqHcZyOpo+1axKtnSBgsbiQBykpufpHKziriMLnc' +
'1xHeXBa4jtDm+cLHd0TfjE39fJ/+QnGzUsj66QPle8cmdHSPdzjjK/zeVaVCEIQhKW4o' +
'KeSofVCoczlMrGshL7Wc4XGUE273n9anpMWpqycQxMqQ4i95KaRg63ABLsAoqmmrXvmh' +
'cxpjIBPG4WgQl1RjuF0s74Z62NkjDZzTe4UX3S4N/wAwh7VxDj+BQMyRVsDG3JsL7ybn' +
'tVinx3C6qdkMFbG+R5s1ovcpihLe55Pb/l8h5PkcubmvdcfdBRfyVd/0Uv8AlRUzzvqG' +
'CCoexkrAbZAOTvYjeL3Nnb0xgeZII5CLFzQSPGFIhZ3azZ92MNglp7NqGODHE87Cderf' +
'1p3R0sVFSRU0DbRxNDQp0LMbTgGuic4942O9uOpSW599dvOjQtpg4Iwqnzb8uvWrqipY' +
'e56WGDNm5NgZe1r2FlI45WkncBdeJk3JJ3lNtlW59pKEfl36gSvV0IQhC+OOVpcdwF14' +
'mTckneU22Vbn2koR+XfqBK9XQsb7IzrUtCzi9x6gPtWDW92HxKgpcIdBPVwxSumc7K9w' +
'bpYDn8S10cscrc0T2vbxabhdoQhCxvsjOtTULOL3nqA+1YNekbAty7PuPyp3HsA9S0yR' +
'YY1r9l4w9xawucSQ3NYcqTu4Kvj8mOe1UQw6np3ROgHLB4Ic06aNF1msPGKuL+7qdzRp' +
'lygjx/FK0mzTXiulzxvaOTOrr8R+SFplmdoNoJ8BxymdPE9+GzxBr3AeA8E6jyW0Wgpa' +
'mCsp2T00rZYni7XNNwVMhCFFHBHFJI9jbOkN3G5/0N56ypUIQvKdpmOl2nrGMF3OlAA8' +
'gTKiwuCmYAWtfKd73Dn6OHOitwuCpYQGtZKNz2jn6ePMl2zUbotqKON4s5spBHkK9VQh' +
'Cqy0FNLJI97HF0gAd37gDa3NfoCsgWFhuX1CEIQsxtOL10Rce8Edz06lJr/xjx0MC0kE' +
's0WH4cWl9iRmDbakuA16LE9nQnigoXF9DTucSXGJpJJ1Jsitdkoah/yY3HsXjCb7LVEF' +
'LtBSz1MjY4mZruduHekDzr06nr6Oq/B6qGX5kgKsoQhCgrXZKGof8mNx7F4wnuxbc209' +
'Kfkh5+oV6ihYb2R3d/h7OAkPo/YsUhdxyyROzRvcx3FpsUxp9osXprcniExA5nnP57pn' +
'T7c4pFYSsp5hxLSD2FNKf2QIjYVNA9vTG8O7DZM6fbPBprZppISeaSM+q6Z0+LYdVW5C' +
'tp3k8wkF+pZP2R33fh7RzCQ+j9ixS9O2Ibl2agPynvP1iPUtAlezXwDS9IcfrFT4wAcK' +
'qAQHDJuP/kfMsVyUf4uzq/8A+SdbLsY2vlLYmsPJHUDpH5DfOtQs1tJj+H0FezDMWpeV' +
'pKiEOL7ZspuRqPJvGqUUmDSwPdV7G4zG+N2rqaR9x4j+8A9KZR7VV9D3mOYLUwW3zQDO' +
'z93WUyo9qMErbcliMLSfiyHIfrWTZj2SNDo3tc07i03C6QhCELzqsiadr8RqZGcoyl90' +
'DD8Z2gYP2iFeeBE6SJ8kb3Rd7KdModYX3kDeeI5vEfgILRlylttC21vJYkcec8/iFamg' +
'DNq8NnaPfHEHxgH/AF5F6ChCEIQhCFznbnyZhntfLfWy6WY2nGatizG0YjuespNm/jXb' +
'vitWvwuFkuGUjpmBz2DM243G6Yqvh/wfTfRN8wUWMuyYLXO4U8nolePIX1W6fFcQpbch' +
'W1DAOYSG3UmdPtljMNs07JgOaSMeqyaU/sgTCwqaCN3TG8t7DdM6fbrC5LCaOohPEtDh' +
'2H1JpT7R4PU25PEIQTzPOTz2UmKzxuwOukika9op5DdpuPBK8hWj2Ebm2iafkxOPq9a9' +
'LQsB7Irr4hSM4RE9Z/csghCEIQhdFziACSQNwJ3Lleq7INybM0Q/JcetxKcpXs1/u/Rd' +
'Md+0qfGSBhVSXEAZN5IA7SB2rEcrD/Kw/wBZH/4id7LPjdXyhj43Hkj4LmnnHBxWpWd2' +
'lrdnY546XHY2Oe5mZjnREkC5GjhqNyzL8M2Nlk5SixqWkeN1nGw6xftVqF81NbuPbmB7' +
'RubUAHzkr7LLUT/hFdsrXdMxAd2KAUtM12ZtFg4f8qlxUxetTxzTs97mqoxwjxqJ4+uF' +
'YZieIs0bWVZH5UlJJ6wpm43iTf48n59PC70ZgpW7Q4gPCELv6C3mkKkG0dWN8EJ/bHmB' +
'XQ2lnHhUcXkfL/4ST0UoqsWqql7A3la2G7Rfc1r384B3tHMpsPZNUS4ZRRvEbpoH1k8w' +
'F3AudvF+fmvzXKlrad9JWSQyPMpb33KOADnNNrE2+NvBPPlCgEkcGIUM8hOVk9ybXNsp' +
'+0dS03t5RczpD+oVyceoh/K/sFc/dBR8zZz+ouTtHSc0NQf1QPWuDtJB8WCT9ZzR61wd' +
'pWDdTg/0n7lG7adx8GlaOkyX9SiftDXHWOKDxFp+1QPxzEnjUSx9MMbXDqIKqy1tZKPd' +
'amd3jZLH6Nx2Jjsu21dMcoF49SGgX1H5Db+VadYzbTEqajxGBlQ5wvFmytbcnUrPHaCh' +
'0kJkJG5gbu8a9DwCdtTglLMw3a9lwbdJTFQ0kboqSGN/hMja0+MBUtpHZNnq8/mSOvRe' +
'SIQhCEIXQc5t8pIuLGx3rlan2Pm3xyZ3yad3pNXoqF517ITr45C35NO30nLLL6QRvBC+' +
'IQhCEIXrezbcmz1APzIPXqmRNmk8Et2bFtnqD6EKfFiRhlQWlwOX4t79hb5wsdys3y6n' +
'rk//ACE42ae91dIHumI5M+GXW3jjK/zeVaVZ3aXDtn5p46vHJWscxmVrXSltxcnQDU71' +
'mZa/YmB+SkwmaredBlDrH9p1+xWIW90/gWwzbHc6ocGj6wXctNPB+EYVsxQ9FS4E9ir9' +
'0UmbKanZ8O4U2GulPmU8cD5PeoJJBxiwJjR1uKnZhldJ4FBU2/KpaSP7VM3A8RP/AApH' +
'zn07fNCVK3Z2vO9kDfHO0+aEKQbN1Z3yU46z6guhsxOd9TTj+ief8YSmhiFLi1VTPeDy' +
'VbEC4AgWc17OcnncOdfYa12FDCsUfG58UUTqGoDd7C1xt9vkUjsRGLyy1kcb2ROdybc4' +
'tdoAt5bk38YXIjjnxChgkByyTgWvY2yn7B1rSe0OH3ygPBte2dcnZ+gsTeQW3nOvh2co' +
'vlzD9YH1Lk7NUnNLKPI37FwdmYOaY+VgXDtmGHdU2/o/3qN2zD26sqWE9LLfaon7O1/x' +
'JoQPnO+xQPwKvj+JNMfyJWtHabqrLRVMIvLSyN8fKydt2t7Uz2WINbNYtNo7aEG2o4Od' +
'51p1i9tcLp67EqeSYvBbFbvTa4uVnvufobl1pDf4ubQL0PZ6BlNgdJDGSWsZYX37ymSi' +
'pnmSmikd4TmAnyhK9r3ZNma0/ktHW4BeVL7YkEgGw3r4hCEIQha/2Om3xGrfwiA6z+5b' +
'9C8027dm2icPkxNHr9azi9dwumhkwOhjmiZIBTxiz2g/FCiqNmcGqL56CJp4x3Z5rJXU' +
'bCYbJcwzVEJ4XDh5vWllRsBUtv3NXRP6JGFvmullRsfjUFyKZsoHPG8HsOqV1GG11Lfu' +
'ijnjA53RkDrVZfEL2HBm5MFoW8KeP0QrUptC88GlUMALWbPULnENaIGkkmwGilxfXCqi' +
'2t2cx/cfMVjMp+S7+sb/AOCnOzAIrpdCPcjvcDzjhG3zrTrM7S7PUOI17MSxSq5Gjp4Q' +
'1zQbXNydTw15tUppMZ5SR1HsbgseVujqqRlh4yftN+hMo9mMTr+/xzHKiS++GmORn7+p' +
'XqTZrAKJ0YZRQvfJ4LpfdC42vz3TGKemiZEIIw1j5XQgMaAA4ZgfRK+PrssT3iPwKhkO' +
'p33c0X+suJq2SOOsLWtvA9rW357hp1/aXytqpoRX5CByFKJWG3xu/wD8oU5fL3cG68ly' +
'RN7aZr8fEoKGWomZh8ryXMfSl0htoXnJb/EuaV1RLDQvzOcOUdypvvFnAX8tljaqQfdh' +
'iNK5+QVXuQcfiv0LD+0AmMU9WwzVlJRNrIZzauoSO+jmGhIGu/xFE8tXMyOaspva+lb7' +
'nS0TAM8jyeGm7yKlTzX2sw6AfxbyXDgSD/ryraP+GYv0d/pNVSX4Gxb+n8xVqs/CcO+n' +
'P909fYPhSr+ZH/iVRrnNwOEhxB5VguD+dCuSOcMWp2BxymCUkX0JDo7ec9aibLIIMSdn' +
'N43uya+D3jTp5V2+eRsdCQ7WV7Wv03jIT5wpWTPdiM0BtkZFG8eMl4PohRtrrUD6l7PB' +
'e5uUHg8t9SmJhbVMBa0TvYbHLqWi19fKFMsztP8AhsX0frKS6WC1eHVkUGH08ZzOk5LM' +
'GtGp74AdZKZRyNliZIzwXgOHiKjovwKD6NvmSbbd2XZqcfKewfWB9S8xW19jlgc/EHEX' +
'Foxr+t9i1dRg2G1V+WoadxPPyYB6xqldRsVg818kcsJ/NyH13Syo9j9huaavcOAkjv2g' +
'+pK6jYfFor8kYJx+S+x7bJZUYBi1NflcPnsOdrcw6xdL3sdG7K9paRzEWXKFtfY4b3+I' +
'P4CMel9i3KF5dto7NtPVD5IYPqBIl7PRNyUNOz5MbR2KdCEL4q1RhtDVX7opIJSed0YJ' +
'615ltTTwUm0FTBSxiOJmWzRuF2gnzpQvZ6JuShp2fJjaOxdVJtTSngw+ZKqRjjspRBpL' +
'fcYSSG5iB3pJt4rqrjbcZdhkDaBlKIDAOXbKHNcDpoMuoWdpaauu7upsVvi5DOf8QWg2' +
'ZidHXSki3uZ/lOI+U4haZZjaLAp8dxqmZPM6PC4Is8gBsHOudB02G/m8qdUzKehDqGjg' +
'bEyKIPaGjTUkdeijgllkqqAyEhz6R7nt3d9ePm8pXLYDTSYZC4hxYXC45+8K7qIWQOo2' +
'MvY1Tn68XB5PaVLWU2eDLCwZjPHI7mvZ7ST1Bc4qAMOmIABOW/TqFNWwmpop4GkNMsbm' +
'AnmuLKY+D5FUwj4IovoI/RCmpYBTU7Yg4uDb6nx3Xl21JttJWkb+U9QV6jxikqi2Wsqq' +
'jD69rQ01UAuJQN2do50VmMUlIXS0dVUYhXuaWtqpxYRA78jTzqjsuSdpKIkknlOfxFeq' +
'GNhlEuXvw0tB6D/5BVaWnD4KqKdhySyyAg6ZmldVjSamgIBIbOSbcw5N6nbCxsz5QO/e' +
'AHeS9vOqdFAJsKijkzAB2bTQ3D7jzKSX4Ypv0eX0o0VEAho65wJPKhzzfmOW3qQ2AzQU' +
'Tg63JFr92/vSPWiL4Yqf0eL0pFWnhfBgszJBZ3KOdv5jISOwqzN8LUv0MvnYrizG1H4Z' +
'F9H6yk1hcBaijp5JcNpHxZSWM0BNrnO11vqlM6aMw00UTiCWMDSR0BfKQFtHAHAgiNoI' +
'PNos/t87Ls+B8qdo7CfUvN1u/Y5bamrn8XsHUD9q2aEIQo5YYp25ZomSN4PaCFkdt8Lw' +
'+kwhs9PSRRSuma3MxuXSxO4eJYNbv2OW2pq5/F7B1A/atmheUbVOz7SVx/LA6gAlIFyA' +
'N5XtjRlaGjcBZfUIQhC8o2qdn2krj+WB1ABKQLkAbyvbGjK0NG4CyhrTahqDwjd5lBgo' +
'tglAP5vH6IX3GADhVQCARk3EA+fTrWK5OP8Ako/6qD/MnOyzGtr5crGN9yO5kY5x8kkr' +
'UqlV3rI62gYA15gsHE6d+HAeZdtY4YrJJlOQwMaHc18ztO1cy/DFN+jy+lGrZAJBIBI3' +
'Hgq9WxjpKYvlZHlmBAcfCOUiw6dVZVXEnxR0Ez5ml7GtvlBsXHmHXZWlHUSGKnlka3MW' +
'MLgONgo6BwdRxZcoAGWzWloFtLWOotaysJZU4BhVVO+eejY+V5u5xJ17VH9zGC/iEfWf' +
'tR9zGC/iEfWftUlNgGFUs7J4KNjJWG7XAnTtTJwJaQDYkaHgk+Gvk7tiDpHPHc4aczyS' +
'TZpuRutrv4kqfE3TtlaInkiRgaGB2TXlGAnMNRo7yKZ0kjsI5WJ5bIYczXP1INt54lcU' +
'VWaiuqY89xG1gDbbj31z1jzL5V1FTG+cRmNojaxzLi5dfMLeMuACs1kvJQE8lygJDS3o' +
'Jsb9C4oqgztcDGGBtiwA3u07j0eJDJYjiMjGxuEpjAc/mIbrbfzZ+HOuq9kclHI2WVsT' +
'NLvduGqn36r6sxtR+GRfR+spNbvls8H+Cqf5vrV1cRP5WFklrZ2h1uF1lvZDdbCKdnGc' +
'HqaftXnq9C9jttsJqX8Z7dTR9q1iEIQhZP2Q3WwinZxnB6mn7V56vQvY7bbCal/Ge3U0' +
'fatYheRbQOz4/Xn8+8dRsqlI3PWQM+VI0dq9oQhCEIXkW0Ds+P15/PvHUbKpRtz1kDPl' +
'SNHavaFWxE2w2qPCF/mK4wkWwiiHCCP0QvmMC+FVA/J6PXosXyX5X1oP8yc7Lsy18pvf' +
'3I88fEfJJK1Cpw/C9V9DF53q4q8s0jKuGPI3k5LtzE63sToPJ2qwqWIgkQhsb3O5Rti1' +
't7WcDY8Bpv6FdVeupGVtK+B7nNDhoWkix8inaMrQBc2FtTdfJGcpG5mZzcwIu02I8S4p' +
'4uQiDMxebklzrXJJud3SVKhCEIXIYwOzBjQ61rga24L5JFHM0tlja8EWIcL6cOwL6WNL' +
'MmUZbWtzWQGNDi4AAkAX6Bu85UFTRR1Dg5zpGuBBBY6x0vb0ipZoxLGWE2vbXyqOlpu5' +
'g/vy/MdNLZWjcFGykc3E31JyZXNtf43Np4hYnynymJtc6k70yCz2kmNuZ1r8wsb9SsU4' +
'cKeIPaGuyC4G4GykWA2/ZiJxendRcoGCn1INm3zHjpdZQMxsxnWoDeLnWv4id/kXqmy3' +
'K/c5Q90ZuV5Pvs2/eU2UNJ+BwfRt8yyfsjOtSUTOL3HqA+1YNekbANy4A4/KncewD1LT' +
'IQhCFjfZGdakomcXuPUB9qwa9I2AblwBx+VO49gHqWmQvG8UdnxWsf8AKnefrFdYO3Pj' +
'NC3jURj6wXsSEKlLi1DC5zZJw0tOUjKd/Upqargq2F1PJnaN5sQp0LxvFHZ8VrH/ACp3' +
'n6xXWDtz4zQt41EY+sF7EqmKm2E1h4QP9Er7hgthlIOELPRC5xcF2F1AALjl3Bgef2To' +
'VjeRk/kJf/h0P2pxszG5ldKXRvZ7mdXUrIucc7T2LTKFkLW1cswddz2NaW8AC77T1KZV' +
'3U7nVzKgSkBrC3JlBBv083N1KwhCFXr3OZh9S9hLXNicQRvBsVzhj3SYZSPe4uc6FhJJ' +
'uScoVpCEIQhCEuwOWSajldK9z3ColaC430DyAExQhCEIQsxtOAa+GzMzxFoXHvW6nVJh' +
'3zi5pEjhvmf4LfEtngxBwqnIeX974R59VdUNJ+BwfRt8yxvsju77D2cBIfRWJXp2w7cu' +
'zcJ+U957bepaBCgqquCkYHVEmRp3GxKhhxahnc1sc4cXGwGU7+pXULD+yO7vsPZwEh9F' +
'YlenbDty7Nwn5T3ntt6loELKS7CUMsr5HVdTdzi4+Dz+RS0WxVFR1sNSypnc6F4eA61i' +
'QfEtMhCxeONy4nOz8q/WAfWnWyw/g6Q/nSOwJ0hZSXYShllfI6rqbucXHwefyKWi2Koq' +
'OthqWVM7nQvDwHWsSD4lplSxk2wauP8AN5PRK7pHshw2ndI9rGNibdzjYDQKPFi2XB5y' +
'3K9rmXGhcCPEN6xXIs/F4v8ApJPtTrZaNrK+UiJjPcjq2B7Occ5K1KrRxhuITyAHv447' +
'k9Bd/ryqyhCEIWdxCbaSU1MEOH0roH5mMeZLEtNwD4W+ydYfE+DD6aKQWfHE1rhwIABV' +
'hCEIQhCFl4fujoXTQ0uH00kJmkexz5BchziePStLEXmJhlAbIWjMBuB512hCEIQsxtRl' +
'NdC1we/3PSNvPqdSkxu5wa4CVw3Rt8BnjWkhklbRYaGv57uyOsLZ2jy77W6b8yeLmMtd' +
'G0s8EgEeJKMc2dp8blifPNLGYgQAy2t/GEr+4Gg/G6n6v2LQ4Vh8eF4fFRxOc9kd7Odv' +
'NyT61cQku1Ivh0f0oHYUlwNubE4GflX6gT6ltEJNjmztPjcsT55pYzECAGW1v4wlf3A0' +
'H43U/V+xaHCsPjwvD4qOJznsjvZzt5uSfWriEIQqOLukbStEZtmdYnOWaWJ8IbtwRBWz' +
'SVQhOH1LI/5dxZlOnAOv2LO48L4vP+r6ITjZkWw5/TKfME4QhCFQxw2wLED/ADeT0Suy' +
'He1ADBd/IgNFr620SraWHFmUbWYW6kZSMiyyNmcWnTdY83WstRtrLv7s7lI0y8lXAdd3' +
'FaPZgAV0tsvvR3VQl5xzDzrToQhCEIQhCEIQhCEIQhCEIQhZjagkVcWaTKwx6hvhO1Oi' +
'THvWhrgY2ndCzwneNbDC4WPwyl5SFl2DM0EXyno6UwUNL+Cw/Mb5lMhCEJPtMP4OZ0Sj' +
'zFKMCb/C8B+d6JWjfWTNrhTigqHRkj3cFmQfWv2KPBpHyUrjKSXlwcbvLt7Qefdv3Jgh' +
'CEIQhcSRslYWSMa9p3tcLgrtZPHBbFZvmtPZ+5N9mxbCGH5TnHtTVCEIS7aA2wCv+gf5' +
'ldgFoIxwaPMq2L6YXUa273flzdnOsZmP8q7/AKNOdmCTXS3eXe5HfT8nzjnWnQhCEIQs' +
'r7IEj48KpnRvcxwn3tNj4JWOp9oMXprcniE9hzPdnHbdM6fbnFYrCVsEw/KZY9hTSn9k' +
'CM2FTQObxMcl+wgedM6fbTBprZ5ZYT+cjPqumdPjGG1NuRrqd5PNygB6ldBBFxqF9QhC' +
'EIQhCEIWL21xWHD8Sga8OEjob5mtuQLncs590VE0e5tnbfwnZQXntsvRdnpo6jA6SWIO' +
'DHMuM2/ed6ZKGl/BYfmN8ymQhCEp2lF8Iefkuae23rSnAxfFYPmuPZ+9axcMjZGCI2Na' +
'CS45Ra5O8rtCEIQhCqYnEyaidG9oIeWtHQSQAfJdcxT1ndnIuoQymFwJuWB0tp3trpDt' +
'F3uKv6YAe0pzs8LYLT+Jx+sUyQhCEt2jNtn6/wChd5kwYLMaOAVTFwThdQByl8v8X4Xk' +
'WM5KX5WJ/tD7U52YY9tdKXGsI5I+/nTeO1adCEIQhVsSmfT4ZVTxmz44XvabX1DSQsvt' +
'y90uz1BI/VzpGuPjLCsEhCEKeCsqqY3p6maL5jy3zJnT7V41T2ArHSDhI0O7bXTOn2+r' +
'mWFRSwSj8klp9aaU+31C+wqKWeI/kkOHqTOn2rwWosBWtYeEjS3tIsmtPUwVUfKU80cz' +
'N2aNwcOxSoQhCEIWM21w6krK+B9SCCIsocJGs5zxPmaUg9oaBsd3Quy/LJLfrPLR9Ur0' +
'DZ6GODA6SKH3trLN77NznnsLpkuY2tbG1rPBAAHiXSEIQlu0IvgtR0Bp+sEn2e77FW9E' +
'BPaE+fUVorhEyhDqe4vNywFuPe2Rh0McUcpjaGh8rjYbhY5fVfyq4hCEIQhcuc1rbvIA' +
'4krpZbafvcRaeNMfOU6wQWwmnHQfOVfQhCEr2l/3erumIhM1UxdufC6huRz7t8FpsSsZ' +
'3L/MKj+t/cnOzMPJ10p7mli9zIu99wdQtMhCEIQk+02JUtDhU8VRIWvqIZGRjKTc5ejd' +
'vCzO0+JUuI7M0ZpZC/kpWsfdpFjkPFY9CEIQhCELYbLsxA7O1L8NqmU7453OeXMDswDB' +
'pqCtRs97ZPohUYjVsqOXYySMNYG5ARexsBxCbIQhCELM7SuIxCIMcQ4xbmuIJ1PM1pce' +
'sBJCRHJ8WOQ/NY49ed/mWvw6o5LD6NjmSvfIODiQL7zm1tqOtMlFTfg0XzB5lKhCEKhj' +
'YvhFQOgecJLsxriLjwph5wtSuWua4HKQQDY2POukIQhCEJfjTC+gLdQC7V2TPbQ83Pw8' +
'q6gfiRqgJYKZtL8oSuMm7TTLbtSXar8LjP5hw86d4QLYbCPH5ypqepZUZ8ocMpt3w3ji' +
'OjQohqWTSyxtDgYzYkjQ6kaeUEeRDajNVvp+SkBa3NnNspHXfrHMV9FQ01JgDHkhty63' +
'ejo8amSvaX4Aq+loHaEye9rGF73BrQLkk2AVTFwH4VUDKXgs3N1JWL7nH4nUfsn7U52X' +
'iDK+U8hLH7kdXggbx0rUIQhCELh8UcluUY19t2YXVWuwmhr6cQVNO10YdmAbdtjuvokd' +
'RsJhklzDLUQngHBw7R60sqPY/nF+5q6N/RIwt810sqNjsZgvlp2TAc8cg8xsUrqMLr6W' +
'/L0c8YHO6M261VXxCEIW72K/3axH57/QC02DfAtB+jx+iFdQhCEIWZ2nDnVcYyuczk9R' +
'lcW7zv1DespLE64LITcc7YiSPK2IAdblpqSjM9Dh72tj9y+ULZe+BuAL697bfz706UVN' +
'+DRfMHmUqhNQ0VIgyPJy3LgO9G/Q9RXyWo5KeKIxSO5U2DxawNiddb8xRPUsgkjY4OJk' +
'NhYbtQLnykDyoqallM1peHEONu9G4WuSegAKLFhfDZh0DzhI9lfwyQ/mG+pPHuxHu4CO' +
'KlNJcXe6Rwfbn0y27VFgrXMp5Q4EXffVuW3etBHTa1r86YoQhCEIXEsscLC+V7WMG9zj' +
'YLtZjaoffMX0ZHnTvCvg2DpbftRQ0rqeSd7wByju9AcXC1yRv3b925FHSvgqqmRwZaU3' +
'BBJLtXG54aEDyKdkZbUSyuI74NaLcB+8lVxRuGKGpDWZXN1dfvtwGXxaX8aupXtJ8B1H' +
'TkH1wrWIgmjdlvcOabhuYizhrbntvSLHosYGDQmhlp4qdlOBPHKDmJ03aLLUUk9393Ps' +
'NMvIgnx3uVptlXROxCXk3zE8kfDFhvHStUhCEIQhCEIQq1Rh9HVX7opYJb874wSldRsj' +
'gs9z3KYieeN5HZuSyo2ApXX7nrZo/ntD/NZLKjYPEY7mCop5RwJLT5vWldRsxjNPfNQy' +
'OHGMh/mS2annpzaeGSI8HtI863vsefBFT9OfRC1a+oQhCELL7VBgrYXPDNI9HODNNTzv' +
'PmaUnfd7AZAXsG4vBe3reWs6gVssGIOE0xaQRl0sQRv6AB1K8uI2cnG1l75QBddqk6jc' +
'cUbVBrLZQC6/fbiMvi1B8isPjLqmOS4ysa4W6Tax6r9agraV9RPTPaGEROucxII1abjp' +
'sCPKjEqV1XC2NtvC1JcW2BBB3b9+46FfcV+DZ+ht+1I9lR98yn82B5lp1xHLHMCYnteA' +
'S0lpvYjmXaEIQlOzuK+2dE5sve1VM8wzsO8OBtfy/amyFSxVj3Ul4w4vabizM+8EbvL5' +
'N6+U8OIsqWmWqp3UwFuTEJD93ys1uxKdqBeeH5vqcnOF/BlN0xtPYige57Z87i607wLn' +
'cL7kUj3Oqa4OcSGTANBO4cmw2HlJRE9xxOpYXEtbFGQL6Akvv5ghr3e2sjMxyCBhDb6X' +
'zO1VtK9pPgWbpfGP7RqaKnixIwuoLX5Dl0dwWNzz/jzur96cbMukNdLnqTKOTOh5tR0r' +
'TIQhCEIQhCEIQhCF8c0OBDgCDzELiGCGAOEMUcYcbkMaBc8dFIhCEIQhefeyD3c3G6V9' +
'JM+EdzgF4m5MXzHnJAWZOGY+890MbUTu354ZhKetpK9S2Y5f7naLurlOXyd/yt817nff' +
'VNVHASYIyTclo18ikVRz3e2sbMxyGB5Lb6XzN1RK9wxOmYHENdFISL6Egst5yire5tTQ' +
'hriA+YhwB3jk3mx8oCK97mNgyOLbzsBsd4vuRinwZU9Ebj2JNsuPd5uhvqanD48QNcHs' +
'qacUtxeMwkvtz99mt2LjCYpIopRJmAzgNDmZTYNA3eTfz82ivoSnaLFfayhaIu+qql4h' +
'gYN5cTa/k+xNkLDxzuxGsdjWzz2NxGPvKyhe6wmA0v8Av9e97hm09BWv5CcuoqwaOp6j' +
'vHA9BO9Ol9Qs7tOPd6Xpv5k4wz4LpPoWeYKw1rW3ygC5ubDnQGtBJAALjc2G8oDWhxcA' +
'MxFibalGVubNYZiLXtrZfBIxzywPaXgXLQdQl20fwQ4cZoR/aNTRUsXIGF1BLM4y+DxW' +
'MzxfiXa77U52Ycw10uWn5I8kddeI4ladCEIQhCzNbie0lJHPO7DqTkIgXF2fXKOe2ZaO' +
'N2eNrjvIBXaEIQhCELNNxTaGokmdR4fSyQMlfG1zn2JyuI49C0g3ar6hCEIQsztPK6Ks' +
'icJnRDk9SJpYgdTzhrmdYukbWMqXZmxsqDzOEcFQeuMsf2La4QC3C6cFpaQ3cWubbXg4' +
'kjyq6o6f8Hi+YPMvpkja8ML2h5Fw0nUr7lbmzWGYC17a2QWtLg4gZgLA21CC1pIJAJab' +
'i43FDmtdbMAbG4uOdV8T+C6v6F/mKT7Lj3eq6Mq0SF8SXE9p6ChfyEBdW1h0bT0/fuJ6' +
'bbkikndh1Y3GtoXsdiL+8o6FjriEHS/7/Xu3CFgK2PZSvrDU0eKOwytDjdzAWDNxsR5i' +
'F1JJPNGIp8Y2fxaIaDushjx4iOdfIHPptKUmnA5qTF43M/Zk0V6LGcSj/wCIc/6WKB/a' +
'yVvmVlm0dY3w4YX/AKrm+bMlmK1+KYziEEFBTUwfFE6Vwe59rXA3lreIVrDNo6s4XS2i' +
'hBETRqDw8amdtDiXNDAfE0386jfjeJO3ieP6ONrvOFA/EsQf4VVUW4Op3N7WEKvJJLL7' +
'4+SToe6UjqfG4Jxsq0MnnAaGjKNA0NG/oa3zJjtD8GtHGohH9o1NFTxfN7V1GTwsuixt' +
'6rg1ONmeW7ul5QADkzu8YWmQhCEIQl20HwBX/QP8yuwe8R/NHmUiEIQhCEJXs/8AgM36' +
'VN/eOTRCEIQhCzW02dlbFI3OwCP3xvLttqfjR3A8oSIvZVmxcypPO0vp6gjyPDHrTwyP' +
'psNw6JrJIwXtzZYXNAGYCxFzlvfnPMna4iaWQsad4aAVndqmB88ALA4ZToWhw39LXeZJ' +
'45ZYtI3yR9DXSgdTI2hWGYliDPBqqi3BtO53a8lTNxzEm7hPJ9JG1vmClbtBiXPDAPG0' +
'/aocT2irG4XVZooLmJw3Ec3jVXCq/FMGxCeCvpqYvlibK0Mc+1rkbw13Apm/aOsd4EML' +
'P1XO8+VVpcaxKTTuhzPoooGdr5XeZUZ3PqdKompB5qvF42M/Zj0X2OSeGMxQYxs/hMR0' +
'PchD3nxk71zRR7KUFY2qrMVdilaXCzngvGbjYX7SVv0Lz+vOyWHVbqamw5+J1pcbsjc5' +
'/fcL3t1Arp8MscQllwLAsKhO41xDnHyDW65gYKnWlbFUD+ZYM3L+2+wV6PBcRk/4RzB+' +
'cNNH6MblZZs3WHw307P1s3ma1LsUoMVwSuhqaGalc6WF0TszHDS4O4k9HBWcN2crRhlM' +
'BJBfkm/GPDxKV2zuI/y8f6jjftCjdgdc3+KqJD+TM1o86gfhtazw6KQDpkkefqBVpI3R' +
'e+xmP58bm9skjfMneypBnnLcpGUatII38RcdqY7QfgcA41cA/tAmT3sjYXyOaxjRcucb' +
'AKpi5DsJqCHhoLPCWKsPxtvWnWy4Ar5bTCT3I6A9IWoQoJ5JGT0zWBuSR5a8k6gZSRby' +
'hToVaillkErZiC+OTLcNtzA+vqVlVKyeWGWnDLiNz7PcGZrC4AG/S996tb96FXonTO5b' +
'lpGyASFrCG5dBv5+Nx5FZVLEqp9IyN7SGgu1u297C+XoJ4q6vhuAbC55gl9JV1EssXK5' +
'A1wDXNaPjWJuDw0smKpYnUyUsDHxWuX2N230sT5N2/mCuriOKOJpbExrASXENFtSbkqr' +
'Q1T6iaoa86MeQ0ZbaBzm7+fwfP0K6quI1RpKUyNLc5Nm5t3E9gKsr6q0MkprJo3ua5jQ' +
'CA0eDe+hPOdL9CsoWZ2mic6uikbE51o7F7YZXEan40bgR1FJHziQ8nJUCU/IfUxyX/Vn' +
'YD2rZ4PG1uE0zcgaA3RuRrba8GkgeQq1JLHFk5RwbncGtvzngvlPUR1MfKROJbe2oI86' +
'z+1RAngLsoGU6usBv4mw7UljjdLbkozJ0sY53bHI5WGYbWv8GjkcOiWRh6nhTtwOvd/F' +
'VEfzpmOHnUjdnMR/l4/1nH1BRYls3WuwypBkgvyTvjHh4lWwugxTHK6apr5qVrooWxNy' +
'scdLk7gR0pi/ZusHgvp3/rFvna5VpMFxGPXuRz/ozTSelG1UZ2Cm1qmxU4/nuDNy/tsu' +
'F0yGWSIyxYFgWKwjeaEhrh4wdbrmhOyWI1baapw5+GVocAGSOczvuF726wF6AhYeOA4Z' +
'VuwbZ9jJMUk7+rrXi4hB1t+717neF7NYfTkVU7jiNW7V1TOc9z0DcE1o5jPC5zgBlkkY' +
'AODXlo8y5o5Hvlqw5xIZNlbfmGVp9a5pb93Vt72zst+wEp2rNu5P1/8ACnOHfB1L9Czz' +
'BWF9Qvi4bDEyQyNjY17hYuDQCfKl+Pe8UY41kPphW68E0ji292ua4WYXbnA7hqd3MkOO' +
'U2Ne1EAoqqGGKOnAnZIy5cdN1gbLNUUVcC/u2cOGmXk2v7bhaTZdtq+Xv3u9yPhNI5xx' +
'WoS3GMZgwcU3LxyP7olETcgGhPG5XAxamm2hOE8nL3RAzls+mXdbjf4yqRbW0UuA1GLi' +
'GcQQS8kWkDMTpu1t8Zd0+1NJUQRytgnAkaHAEC+t+n/V18o8aooQI4oqg8o5uriDvLGj' +
'n/LaombZUL6s0wp6jMIWTXs21nNaRz7++CknxyhmbHK+Kosw3GUgX1BsdeIHUvtXtXR0' +
'lI6ofDO5gF9AL+dEG1VHNTNnbDOGuAIBAvr5V2zH6OLMxsUwtd53c5uefpVcbY0Jre5e' +
'QqM+TlL2ba3WpZ8co6jK18VQMrr2BAvqRY67kVG1dHT075nQzlrG5rADXQHj0op9q6Oo' +
'p2zNgnDXNDgCBe3WuG4xQRTcq2KovG0NtcW8dr79d65ftjQsxDuM09RnyF97NtaxPHoU' +
'lXjdDUMySxVFmknvbC+liN/Pey7qNqKSCGSV0MxbG1zjYDm8qjo9raKspmTxwVAa69g4' +
'C+htx6F1DjVFDJI9kU95Dc3sbak6a8ST5VDPtpQQ1rKV1PUF72F4IDbW16ehWKjGqCpD' +
'qeenke0ycnq0HUi4O9B2npGwmUwzWEbpLWG5psRvUeG7XUWIxMkigqGh83IjMBvyl19+' +
'6wRR43R8rysbKq9SYHZXEEAykhvP0apth1dHiNIypia5rXE2Dt+jiPUrSxG3GI0tFiUA' +
'mja+Uw3beDNpc/GD2uCzrNqoyMkkdY1nNlqc7f2ZA7zr0fZ6aOowOkliBDHsuAWNZznm' +
'boPIrdVEZmMAAJD2m/AX1UeHRSRQHlWZHEjvbg2s0N5vFdTuhifIJHRsc9ugcWgkeVdr' +
'6hfFXxH4OqvoX+YpNsob91fqf4k4qJXsq6RjXWbI5wcONmkrqpndC+na0AiWXIb8MpPq' +
'XdRM2CMOeCQ57WWHFzg0edKMS2Xoat/dFLmoK0atqKbvTfpA0KRyQHE6xuDbQRsjxSPv' +
'6StYLCYDW37vMd+4QlGzmFe1tE98vfVdS8zTvO8km4HkHrV3DY3xUMbHtLXC9wfGUUBj' +
'MUgjbI0CVxIkGt3HN1d8ijmE0lTlY1rWy5QQNXaC5Plv5LLmnkl9sKhkpfkOsQu0tsLA' +
'7he9+KTbWnWDoB7f/JMYpK9mG0XcNPBN7i3PysxZbQWtZpvzrt4AxqJ3eGQxBpaWatb3' +
'xzB3C9h5Vfe9sbHPkcGsaLlzjYALiKohmF4po5PmuBUqErx3wKEca2HzpoqeLgnC6gC9' +
'8vMFjeSf8p/7DvsTjZljm10pJcfczvaRzjitMsvtozlKjA2fKxBg7VVpH/8A7hY3NzRU' +
'YH1WfYs+z3P2MJPztZ6x9ivYW21PRNO60Q63U/8A4i7pXWZTvO8CJ3ZSu/wlKqAA7STs' +
'O4UrGdTWD1JrDq18LlQxh38BVLDvbl9IKxhoBoKFnNybXO6lNcujJ55XWHiSpljtZMPi' +
'8kGjxWA+1Nrm2bntfy2B87SqmLi2GVNuZpt5DbzEL7hJHtZSk7mssfEVbA0Ad8x3qSki' +
'+08YdvdCAfIfsCbNN7ZuexPWXH1KriNzhlQOcxG/7JcfOFxggDMIp/mk9ZKuOddKKhmf' +
'aWnbxp3+i9OC/wAKX6CX1FQV3udHUDma2pj62khVNme8wqB/CpqH/s05+1NaJuWekYea' +
'WgYf1YXOT/ZBxODUw/MNd1lx9afLH7Zswd1bCcVoK6Q8lpUU40aLnQ626fKsx7UbLVf4' +
'Jj0tO75NTF69AvRdnqZlJgdJTx1EdQxjLCWM3a7U6hMkKKWpgheGSzRsc7UNc8Aldtc1' +
'wu0gjiCukJdigHLUru8Lmv7xj2XDjcbjzG1z18FxLJXvw2t7up4IfcXZOSmL76G97tFu' +
'ZLtkjrOPlAHq/wDNOauYR1tIwtb3xdZ7gTl3DQjQE3511Wuiaad0ocSJmhgHyjceYlGI' +
'BjoGCSVkQ5WN13GwNnh1uxfMUc5uHyuaSCLag9IVPaLCvbKiY+LvaumeJoHjeCDcjy/Y' +
'm6Fy7wTrl038FWw3WkBDnuY4kszuLjl5rk69PlXVFDLEJjMGB0j81mE23AX8trrqClgp' +
'nSuhiZGZXZnZWgXNl0yCKOV8rGWfJbMeKze1hvLb5MbD1uIT7CzfD4egEdRsrRIBAJFz' +
'uHFLto3Zdn68/mXDsXkgJBuNFZixGtg95rKiP5shC2WwuJVtdPVtq6mSZsbG5Q83sSSn' +
'2Of/AMeONbH600VLGDbCqg2B7znF1i+UH8nH+wE52Xdmr5e9YPcj4Lbc4WoWe2nZnxHA' +
'R/PgeoEpPRv/ANoNrJ/5OAi/iafsSat9z9jDDx/KVZPa/wCxX6XvIYD8lkZ6mU5/+2h3' +
'ucJ/IYexjv8Aw0pgdye1ld+Q5zeogepOZPc6lsnM7eqOOstRVHyXNv2qTDiRhcTucxsa' +
'3qCt6Nk6IW9pSdjb7RuHxu59fGd3nCb3Ga/Ne/kvfzOKp4rcYXPf5Fj1W87V9wkA4ZSj' +
'mczKfHzK34Q13vFj4wk8l37TMeN/IHtBb5ynDhe9ue4HlIaPMVWxJw9rqp35p5Hl0HYF' +
'Bgri/CoPER1Epg1thqlj3AbW0V+eO3XmHrV8NJpbHeaZ7fKxy4xPv6OstuJa/wAj2EKp' +
'g/uez0TvzNdJ/Zsb603HeV4/Jqh/Z0n7082S0oGs+RT0464wfWn6z+PvxFtfEMOxqjo3' +
'cnrT1GXv9TqL69GnBKqiPH3C9ds/heJs+XFYE9f2LQ4dOKfC6Me176UOs0wg3EN3W1Jt' +
'zkJohef+yI7+E6VvCG/1ispHLJEbxvcw8WmyuRY1ikPveIVIHDlCQvVMJkklwmjllcXS' +
'PgY5zjzktBKtEgEAkXO4cVVxQ2w6a/OLdZskOyZtKB8qN56nD7Vop6flpI3GR7WsNywb' +
'nWIIv4iFxWUndLonCR7HRvDhY6HvgTfqXOKBzqNzGRue512jKLkEg9h3HoKkrXFtI9wk' +
'EdtS7LmsOgc5UsReYmGQAPLRmA5jzrtC+EAggi4O8FcxRRwsyQxsjZ8ljQAu0IWU2oN6' +
'97fzMP8AeEetM6I1z8KiNAacO5SQO5cOIsHuGllYcyqOJUj5WMc1sZDnMcbBxBvpw0HO' +
'odqnZdm64/m7doXk6FtvY4b3+IO4CMektJjfvmGjjWs9FyaKni3wXUc3e/65wsbmHyu3' +
'/wD2nGzJBrpe+v7mefpH5RWmSHGpIpNoMEpc7eWEzpcnPlyO16ws7Rus3auYkffszqWm' +
'N/fJDns0dOoVLFKeWXYvCcPgYX1Ec8jZIxva5uYuHkBViBj3UUYa273QsyjiXMkydeVt' +
'vGpKjK+OYscCx7JC033tLaog/slpS7uSb7pMQncwiN+chx3El1/tTJ4z0wB8JvT871Nd' +
'1Kri7Hy4PI1rbygHvefS1+wgqWhicympIXtyujble0/KAvZSAZ4hu90786/FB3pa2nn+' +
'6R9SGHkpGDI7mNrfYmYZclgscosddw1Hmsq2IsdNQTsYMz3sNgDz6Hz3Rh0L4sOijkGV' +
'wYLg8xH7lZILs2W1zZw13FLnwyjaOOfkzyPJ2zc19TbrTEAtcG/GABAvwB9ZUFbE6Shm' +
'jYMxc0saAd5DbAdZK4weB0GGwxSDK8B1wfGT61bNzoOe3Px3JTUQTux2mqY4y6NjBdwO' +
'g1JTcNtMGm2USyjf8Vw07bKCZpfRSM+M6mYLX+M07upV6SnliwJkDm5ZRR1DC07w50jQ' +
'B1C6Y1JHdNQ9liDNVvbrvBhDG9Zunmy7gJKuO40bAG9IETQe0ELQLK7WspnVcXdez0+J' +
'R8n7/CTmZqdLDr386zcZ2ajk9xrMYweXgb2HVcrb0EUk2HYe6CtFbCzvnTS3Bl10Pk6e' +
'cBN0Lzv2QnXxuAcKcek5ZVC9kwtuXCqNvCBg+qFDVsqjiUT4mMLWxuDHOcbNcQd4HNo3' +
'nVauNczCpTXmnLjJGG8gHAWL2jW6W7Lm1fG38zN/eAepatCFDU08dVFycmbLcHvXFpuD' +
'cahStGVoAvoLam6+oQhCELI7QHPi8jfyY29UkZ/xpzs0/NhDOiR563F3rTGaaOCMyTPa' +
'xg3ucbBKdsHZdmK09DR9dq8rQt17HDfca93FzB6S0GM+/wCFj+eN9B6ZOcGi7iALga8S' +
'qOKSxy4RUvie17cpF2kEXusZm6B1D7E62WN6+XQD3I8w4halZHFP/SVg/wCjv80iT0vw' +
'fF/7SNViH8Kj/wC8q7+7cjD99F83DfReqsf4BD+ht/8ApJlan/CZvnu87ly/wXfrehUr' +
'mp97m+ZN6DFN/wAa79Kf6Chh97i/RHekV0zfQ/Nd619g/CKjxFRjwl27wXfOPooj3+Qe' +
'ZfJfDZ4/Uuj+GN+YvjfifTFEfvjfHJ5l9b4Tf6LzlRx+8/qN9EqV3vh8f+VQu8B3iHol' +
'TSb3frekuX+F5Hpzst+Gz/RNWnWY2mflxGHk9o/aubktIni7Hi51Nzbo8iqNO00kfudT' +
'g+MRcDa56rBafChK3DYBPSx0kuXvoY7ZWG+4W0VtC832+dfaBo4QNHaVmUL2ijblo4G8' +
'I2jsXTZo3Svia9pkZYuaDqLpdtI/LhD+mRh6nB3qSbZ85MXib+TI3rkkP+Ba5CEIQhYZ' +
'u38rJHNloGPAJF2yFvqKtxbf0TvfqOdnzSHfYmmGbUYbilUymp3SiZ97Ney24X3p0hY7' +
'EjyuOy/pDWj9ulHnuruzZqJ8FkZSzshlDoiHvZnFjDG7dccSmtRHOMLMc7jUTEFpfDGG' +
'nW9iGkniAl22ZI2WnDrBxLAbfOC8xQt97HTfvGsdxlA7E8xfWtwkfzq/9m9XK2ISxx97' +
'mLJWOHRZwueq6z9VhmNVlKI6CsZRwkBsjJYrlxDWjnF94K4wrZSqjdJ7aVwmBAyci0Nt' +
'xvceJO8Pwinw+Z0sL5XOc3Kc5FuwdCYLHYlI1/sm4Uxpu5lO4OHDvZClVL8Hxf8AtI1W' +
'IfwqP/vKu/u3KLBqmKqFM6F2YMdh8btCLOaHgjrUUf4BD+ht/wDpJlZqDaonJ5nu87lx' +
'nEkOdpu1wJB6MlSip97m+ZN6DF3LKyGrzSGwNYWjTnLLDtK4h97i/RHekV0zfQ/Nd61x' +
'HNGyukic6z5Guyi2+29A8Jdu8F3zj6K4hlY6Z8YPfsa0uHC408y6l8Nnj9S6P4Y35ijg' +
'kZK1jmG4E7h5Qu4/fG+OTzIc9sbc7zZrRGSei5UcDg+na5uoMbSP2VM73w+P/Kq08rIo' +
'iXmwOVo8ZFgrMm9363pLl/heR6a7M1EUeKOgc60ksILBbfa1/OtWoJ6KlqXB1RTQyuAs' +
'DJGHEDyqNuGUDXAtoaYEc4ib9itAACwFgF9QvM9unX2jkHCNg7FnV9XtcbcsbW8AAqE0' +
'dTJWS9zHkHiOwlfHmbe7TcC4vcXB15h5Vm0fdEGCsZVTsmlLpCXsjyCwhkduueCpYaeS' +
'x2HgKhzfr1Q89lsULObTbSy4HVwRR07JWyMLjmcQRrZL4vZAiPv2Hvb8yQH1BXItucJf' +
'4bKmP5zAfMVoqeeOpp454jeOVoe0kWuCLhSE2BPBeJk3JPFfFoNh232lhPBjz9Ur05Cw' +
'3K8piAm5n1LXjxGrDR2Rpnse/KJIT+LwvHkzMPoBaOSRsUbpHmzWgkm19En2kpJsYwJ0' +
'VC0Pe97TZ3e7jrv3FYSXZbGovCoHn5jmu8xVKXC6+D32iqWdLonD1Lcex6wtwmpJBB5c' +
'jX5oTfFdcSwgfzhx/s3JohVMSxCDDKQ1NUXCMEA5Rc6qrhW0FBi874aR0hexuY5mW0vb' +
'1pqsPV/+laj+iP8AdvVGl+D4v/aRqsQ/hUf/AHlXf3bkq2P94/8AfKX0pFbj/AIf0Nv/' +
'ANJMrFX79UfOf/iUNN+ARfR//bqVJU+9zfMm9BihxP35n/eTP8Kmh97i/RHekV0zfQ/N' +
'd61Sk+HYPmy+YK0PCXbvBd84+iqlJ8KVf0cXmKtS+Gzx+pdH8Mb8xVMM/Bx+lSekVaj9' +
'8b45PMoqz8Dm+iZ/iXNF+AQ/Qs9BWXe+Hx/5UuxP8GZ9NEmMm9363pLl/heR6nwP/eqj' +
'/R5PM1blCEIQsJtTs5ilfjU1VSwCSJwaBZ7QdGgbiVn5tn8Xh8PD6g/NZm8yqGkqI5Wt' +
'mgkju4Dv2EL2ZRU9RFUsL4iSAbatI6efoIWe2xfmbHCPxeZ58oawemUs5Xk8QdNzMqXP' +
'PiFWWnsetyhee+yG7+F6ZvCAH6xWTQvYcHblwahbwp4x9UKxUuy00ruDCexeLIWk2Dbf' +
'aEHhC4+ZelKviFU2ioKiqduhjc/qF1hoWmERsf4cAw9jr/KdIZD2lMMBl7nxamYTblDV' +
'UhPBzJS9vYStFDT1tLBOXVj62Qt9zbIxjADrwA36b1Yo2FlLG1zXNdbvg4gm/OTbTXep' +
'0L4lmJa4xhA/OyH+zcmioY5Vy0OD1NVBblI23bmFxvXnOJ7T4jilG6lqTFybiCcrLHRM' +
'/Y8+F6n6D/EF6El1LSmPHa+oc6MiWKENaD3zbZ9T479hV/K3gN99y+5RwCWYFA2GnqRn' +
'jfmqpZAWcwc4uHYQmWVvAdSr4jGH4dVMu1uaJwzO3C4K+YXEIsLpIyWuLIWNLm7jZtla' +
'yjgOpLsbpTU0TGtdGzJURSEvNhZrwT5dExyjgOpGUcAllZAHY9h02ZgEcczS03u7Nl3a' +
'dBTPKOA6kZRwCWUcAjx3EZS+M8qyKzBvGUG9+sJnlHAdSMo4BK8AibFRzR52SHumVxyg' +
'6ZnFwGoGtiE0yjgFVxOIS4XVxgtaXwvbmduF2nevuHRtZh1My7XZYmtuNxsAFZyjgEsx' +
'2ATU1OM8ceSqikJfpcNcCR1ApnlHAIyjgErro2Nxmjq3SxsjpY5BICe+7/KGm3DvXdSa' +
'oQhCEIXxfUqZSV1RFmFTLQSl7nSBrY35tBbfewAFkix6XujFqmMG/J9y0gPFz5Q93YAq' +
'EzTMJGM8OcYgxtvlNeJB2hbnD6ptbh9PVN3TRtf1i6sLzn2QXXx6McKdo+s5ZdC9lw9u' +
'XDqZvCJg7AjEXZcOqncIXnsK8aQtR7Hzb47KeFO70mr0ZZ3aqbut9HgcRvJXSAygfFha' +
'buPlt51naqpE1FXYiD3k2MRMYeLWbuxT1fKQVONNhF6jD61mIxt+U1w78eKy21HVRVtJ' +
'FVU7s0UrQ5p6Cp0IQqVVSPnxChqGloZTueXA7zdpAt1q6qOM0b8QwmopInNa+VtgXbhq' +
'vPcW2UrMJoXVc80D2NIFmE318YV72PPhep+g/wAQXoSgjiy1s0gYAHsYC4c5Bd6rKdCp' +
'4dnMUj5IXwvc++R1tBYAWsd1gPLdXFHMSIXlrDIQLho51HQsdHRxseCCBudvtzX6bb+l' +
'WFSxSCeopQ2AsuHBxa4aO/0bHyK6hUqiN5r4HsZJpoXh/ega3BHPzW3q6hUaeGVmJ1Dy' +
'H8m7W5PenRtrDm3Ov5OhXkKClY5jHl4s50jnHpF9OyynUFax0lHMxgJc5pFhv8nSvlCx' +
'0dHG14IIG52+19L9PHpVhUcXhknpAyEPz3IBbbS7SOfxq8hLK+mkM0ksUOclrQQ213HK' +
'9vZmHkTFos0C97BdIQhCEIQoKyqioqSWqqHZYomlzj0BYmk5SepwVs4tUYjWvxGRvyWt' +
'HeDxWUFLUiGiosRJ7yDGJWPPBr9/YtFstL3JJWYHKbSUUpMQPxoXG7T5L26lol5rt46+' +
'0JHCJo86zaF7VTty08TeDAOxVsZdlwWudwp5PRK8eQtZ7Hbf4XqXcILfWC9CVIYVRium' +
'reSPdMzOTfIXuvl4DXTyWUB2dwo4dHh/cv3rFJyrGCR2jtdb3vzlWPauj9sX4hyP3y+P' +
'k3PzGzm8CL2PUusOw6lwyn7noozHFmLgzOXAE8Lk2VpCEIQhVq6hpsRpjT1cfKREgluY' +
'jd4lXw7A8OwuZ0tFT8k9zcpOdxuPKUxQhCEIQhCEIQhCEIQhCEIQhCEIQhCEIVXEcOpc' +
'Tpu562MyQ5g4szloJHGxF1z7V0ftiyv5H75ZHyTX5jZreAF7DqVcbO4UMOloO5fvWWTl' +
'XsMjtXaa3vcbgpzhVGa6Gt5I90ws5Nkge6+XgddfLdXV5jtu6+0s44MYPqhZ9fQLkDiv' +
'bALADguJoY6iF8Mrc0cjS1zeIO9JJdjcFk8GnfH8yQ+u6pS7A0DveqqpZ87K71BXdntm' +
'hgdVNKKrlhIzKAWZba34lP0IQhCEIQhCEIQhCEIQhCEIQhCEIQhCEIQhCEIQhCEIQhCE' +
'JVX7O4XiM7p6qmzTOtd4e4HQW5ilsuwuFP8AAkqY/E8EdoVR2wEQka6LEHgA3s+MG/aF' +
'slTfiEbJ+RyOzcpyepDb6Aki+/wh2qSmqm1D5GtaQGbifjC5F+tpVhCz221ZUUWDRy0s' +
'z4XmdrS5hsbWdonElXknfEYnEhuZtrd/0D/XHgpYJeWhD8uUm4I32INikW21ZUUWDMlp' +
'ZnwvM7WlzDY2s7ROH1bWyyxNa5z42g2HOTc27O1SwSieCOUCwe0OA8akQhZ7basqKLBo' +
'5aWZ8LzO1pcw2NrO0Tl1UxsssZa+8bQdG+FcE2HHcpYZBNCyQAgPaHAHpXaVv2iwiN7m' +
'Pr4muaSCDfQpjFKyaJksbg5j2hzXDnB3FdoQhCEISbH5pITDydU6Eua9rg3mabXk/U32' +
'6Ur9sa3Nm5WQOzE8nm3HLbJ5G+6diK/EaupweOkw+aR2IscCMju/ljA0k8TtD5VLJijn' +
'Y3TVkVU44RFFkqHhxyCTXQ9OrexU6LFaqfCtoJ21cj+SeTA/N4IubWXyvxaqpsIwGpdV' +
'St5R153B2rwCL3U2OV9Zg2N0tc2oklwyotdma7RprbyahcbSsxoV8MuEVVRJT1TbtbG/' +
'QEC+nQRr1pXye2HGu/bWj2RbjDe6/bfl/icnypv8q9uxaNCEIQhC4llZDE+WRwaxjS5z' +
'jzAbylzNosIke1jK+JznEAAX1KaIQs9sTWVFbg0ktVM+Z4nc0Oebm1m6LQoQhCz2xNZU' +
'VuDPlqpnzPE7mhzzc2s3RaFCz2xNZUVuDSS1Uz5nidzQ55ubWbotChCFTnoeVnEokAOc' +
'ON23073TraNV1S0nITTSlzXOlIuWsy7r7+sq0hZf2QfgGP8ASG+i5PJqWaSaSSOoDCYy' +
'xh5O5be3PfXUX61PTxmGBkZLSWC3etyjqufOs57IHwDH+kN9FyeGiyzzTQyuY+YAHNdw' +
'uL81+kdXSp6eLkKdkV75Ra4Fh1cylQhZf2QfgGP9Ib6Llo+SIqDKHeE0NItzC/29i+wR' +
'8jBHFe+RobfjYKReTbR0xi2jrIWjV02YD52vrXqtPEIKeOFu6NoaPILKRCEIQhCSbRh3' +
'3taBrxcgE2752lovE/cebTVI9Mt+UJba/K85F7B/jLvcvm9CHgYJGcfjeZpnOMDqZ2jY' +
'TzsB4NIsOZfTQNp5m7MCQuirx3S6cjvmEa2A/UHWquFwimwHaOAHMInZATz2uFzjELqj' +
'Z3Z6BhAdIcgJ4mwVvAz7cYLVbP13eVVNfky7eLHTqOniKhwraSbCcHno6iIPqqJ4axjz' +
'bvb2I8h7Cj/9QKj8Qi/bKfbL7QSY73VykDYeRyWyuve9/sT9CEIQhCjniE9PJC7dI0tP' +
'lFl5Vs5TGbaOjhcNWzZiPm6+pesoS2UPDKtvLvBDy4C+pFmGw6NbeVKfY++AZP0h3otW' +
'oQhcSOc2NzmNzuAJDRznglEcp5KmEsjxMJrBjzY++EHcbXtzcLqj7H/wDJ+kO9Fq1CRm' +
'aHkCzuoBrpw2ImQjUjeSeGunygqfsffAMn6Q70WrUIVeuBNMcr3NIc03bz6jRJnyye18' +
'pdK3OJCXNzkXdYaX5iHEabr2CsYxtLh+E3ZJJys4/io9SPGeZZvD9p6/Fto6OJzhDTGT' +
'3pnPod551vULL+yD8Ax/pDfRcr+MbS4fhILJJOVnH8VHqR4zzLNYftPX4ttHRxucIacy' +
'e9M59DvPOm3sgfAMf6Q30XK/jG0uH4SCySTlZx/FR6keM8yzeH7T1+LbR0cTnCGnMnvT' +
'OfQ7zzreoQsv7IPwDH+kN9Fyv4xtLh+Egskk5WcfxUepHjPMs1h+09fi20dHG5whpzJ7' +
'0zn0O8863yw+P0PKbd0Gl2zcm89OUm/Y1bhCEIQhCEi2lMd6fNK9pyyFwHxY+9zvH5TR' +
'YjxpN32axY0SZiMnNmy3LfEGd/8AO6V8pg6irBiGKNHtQ6MMiLu/EhNsry3XviBcm28q' +
'NlPVR4dNhkxd7dTv5SmJddwj03P5tGu0v51BgzJI9ndoWTX5Vuj7m+ovfVfcVm7mwDZy' +
'fLm5J2e3G1irW0I7irqPabDe+iltygG43HP4xp4wrlfFhzMRpNoZA11FPHlluzMMxHek' +
'js8i79v9lfkQf9Kf8qaYLX4VXct7VhgyZeUyxZN97cwvuKaIQhCEIQsPgFDye3dfpZsP' +
'KPHRmIt2OW4WCxDaevwnaOsia4TUwk96fzaDceZaTB9pcPxYBkcnJTn+Kk0Pk4qh7H3w' +
'DJ+kO9Fq1CELBYhtPX4TtHWRNcJqYSe9P5tBuPMtJg+0uH4sGsjk5Kc/xUmh8nFUPY/+' +
'AZP0h3otSrENp6/Cdo6yJrhNTCT3p/NoNx5lpMH2lw/FgGRyclOf4qTQnxcVQ9j74Bk/' +
'SHei1ahCwOIbT1+E7R1kbXCanEnvT+bQbjzLS4PtLh+LAMZJyU5/ipNCfEedVsZ2PoMR' +
'zS0/3rUHW7B3rj0j7Fm8LwKvwnaai7phJj5TSVmrToefm8q9GQsv7IPwDH+kN9FylxnY' +
'+gxEulp/vWc63YO9cekfYs5heBV+E7TUXdMJMfKaSs1adDz83lT72QPgGP8ASG+i5TYx' +
'sfQYjmlp/vWoOuZg71x6R9izeF4FX4TtNRd0wkx8ppKzVp0PPzeVejIQsv7IPwDH+kN9' +
'FylxnY+gxEulp/vWc63YO9cekfYs5heBV+E7TUXdMJMfKaSs1adDz83lXoyWVdDy2PYf' +
'Vge8MlufGAB5ymaEIQhCEKtWUcVYIhLmHJvDxa2tuY9B51S+5+lyZOUn8ENvmF9H5r7t' +
'/N4tF9q8ApaygdRSyTCJ0xm71wuCSTYabtVLJhFPJi0GJF8vLwR8m0AjLbXfp+UVDHs9' +
'SRwV8LZJstc4ulu4XBN92nSo6zZiirKClo5ZJxFSghha4XPj0UtNgNLBhEmGcpNLTPvp' +
'IQS2/DTjr41ENmaQYO7DDPUmnL84u4XaeA03Kj9weFfy1X+23/KmmC4DS4Jy3cr5Xctl' +
'zco4Hde1rAcU0QhCEIQhLKSh5HHsQqyPf2RWPiBB8wTNec4pgVfi201b3NCRHymsr9Gj' +
'Qc/P5Fo8G2PoMOyy1H31ONbvHetPQPtUXsffAMn6Q70WrUIQvOcUwKvxbaat7mhIj5TW' +
'V+jRoOfn8i0eDbH0GHFstR99TjW7x3rT0D7VF7H/AMAyfpDvRakOKYFX4ttNW9zQkR8p' +
'rK/Ro0HPz+RaPBtj6DDsstR99TjW7x3rT0D7VF7H3wDJ+kO9Fq1CF5zimBV+LbTVvc0J' +
'EfKayv0aNBz8/kWkwfY+gw8tlqPvqca3eO9aegfav//Z'
};

window["fpdefs"] = fpdefs;
