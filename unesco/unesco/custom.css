

.field_margin {
    margin: 5px 10px;
}

.field_padding {
    padding: 5px 10px;
}


.field_textarea_big {
    width: 311px;
    height: 150px;
}

.field_textarea_average {
    width: 311px;
    height: 100px;
}



/* Dive Fancybox */
.fancybox-wrap  {
    left: 50%!important;
    transform: translate(-50%);
}

.fancybox-inner #dive_modal {
    height: 100%;
    padding: 0px;
}

.fancybox-inner #dive_modal > div {
    height: 100%;
}

.fancybox-inner #dive_modal iframe {
    height: 100%;
}

.styleless-btn {
    border: none;
    background: none;
    padding: 0;
    margin: 0;
}

.container .aside .module .clearinghouse-search-form .module-content {
    margin-bottom: 0;
    padding: 0 0 10px 0;
}

.clearinghouse-filter-label {
    font-size: 1.1em;
    vertical-align: middle;
}

.clearinghouse-filter-buttons-container {
    vertical-align: middle;
}

.clearinghouse-filter-buttons-container .checkbox {
    display: inline-block;
    vertical-align: middle;
}

.clearinghouse-search-reset {
    width: 100%;
}

.js-clearinghouse-ressources-list {
    margin-top: 10px;
}

.js-clearinghouse-ressources-list #map {
    height: 300px;
}

.leaflet-popup-content-wrapper, .leaflet-popup-tip {
    max-height: 150px;
    overflow-y: auto;
}

.leaflet-popup-content-wrapper hr, .leaflet-popup-tip hr {
    margin-bottom: 5px;
    margin-top: 5px;
}

.cd-top {
    z-index: 1000;
}

.wrapper_navigation .navbar {
    z-index: 99;
}

.container .aside .module .sidebar-filters .module-content {
    margin-bottom: 0;
    padding: 10px 0px;
    overflow: hidden;
}

.container .aside .module .sidebar-filters .module-header {
    margin-bottom: 10px;
    padding: 10px 10px 10px 20px;
}

.container .aside .module .sidebar-filters .module-content .input-form {
    width: 100%;
    margin: 0;
    border: 1px solid #d4d4d4;
}

.container .aside .module .sidebar-filters .module-content .select {
    width: 100%;
}

.container .aside .module .sidebar-filters .filter-submit {
    width: 100%;
    margin-top: 10px;
}

.facilitator-card {
    padding: 5px;
}

.facilitator-card > div {
    height: 150px;
}

.facilitator-card .facilitator-card-image-container {
    height: 100%;
    padding: 15px;
}

.facilitator-card .facilitator-card-image-container img {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    max-height: 100%;
    text-align: center;
    margin: 0 auto;
}

.facilitator-card .facilitator-card-text {
    height: 150px;
    overflow: auto;
    padding: 10px 0;
}

.facilitator-details-label {
    font-weight: bold;
}

.facilitator-details-content {
    margin-left: 30px;
}

.back-to-list-button {
    font-size: 13px !important;
    vertical-align: middle;
    padding: 8px !important;
}