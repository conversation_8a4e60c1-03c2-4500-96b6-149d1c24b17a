*, *:before, *:after {
  -webkit-box-sizing:border-box;
  -moz-box-sizing:border-box;
  box-sizing:border-box;
}
a {
  background:transparent;
  text-decoration:none;
}
a:active, a:hover {
  outline:0;
  text-decoration:none;
}
a:focus {
  outline:thin dotted #fff;
  outline:5px auto -webkit-focus-ring-color;
  outline-offset:-2px;
}
#body {
  font-family:"Inter", sans-serif;
  font-size:16px;
  line-height:1.4;
  background-color:#fff;
  color:#fff;
  cursor:default;
  margin:0;
  padding:0;
  width:100%;
}
a { color:#212121; }
.disable_selection {
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
}
#html {
  overflow-y:scroll;
  overflow-x:hidden;
  font-size:62.5%;
  width:100%;
  margin:0;
  padding:0;
}
.h { font-weight:bold; }
img {
  vertical-align:middle;
  border:none;
}
input::-moz-placeholder { font-style:italic; }
input:focus::-webkit-input-placeholder { color:transparent; }
input:focus:-moz-placeholder { color:transparent; }
input:focus::-moz-placeholder { color:transparent; }
input:focus:-ms-input-placeholder { color:transparent; }
p { margin:0 0 10px; }
pre {
  word-wrap:break-word;
  white-space:pre-wrap;
  white-space:-moz-pre-wrap;
  white-space:-pre-wrap;
  white-space:-o-pre-wrap;
}
.title {
  margin:20px 0 10px;
  line-height:1.1em;
  font-weight:bold;
}
.bg-color-blue_2 { background-color:#004d79; }
.block { display:block; }
.bold { font-weight:bold; }
.comment { display:none; }
.hide { display:none !important; }
.hyphens {
  -webkit-hyphens:auto;
  -moz-hyphens:auto;
  -ms-hyphens:auto;
  -o-hyphens:auto;
  hyphens:auto;
}
.newspaper {
  -webkit-hyphens:auto;
  -moz-hyphens:auto;
  -ms-hyphens:auto;
  -o-hyphens:auto;
  hyphens:auto;
}
.newline { display:block; }
.no-border { border:none !important; }
.no-bullet { list-style:none; }
.no-wrap { white-space:nowrap; }
.relative { position:relative; }
.reset {
  margin:0;
  padding:0;
}
.underline { text-decoration:underline; }
.visible { visibility:visible !important; }
.wrap { white-space:normal !important; }
.important {
  color:#CB0000;
  font-weight:bold;
}
.notice {
  padding:0px 0px 10px;
  background-color:#b6eeb6;
  padding:20px;
  margin:20px 0 20px 0;
  border-radius:5px;
}
.alert {
  background-color:#FFF6BF;
  padding:10px;
}
#home-saveguarding .grid-1 {
  width:30%;
  vertical-align:top;
  padding:10px;
}
.text-center {
  margin:0 auto;
  text-align:center;
}
.margin-left { margin-left:20px; }
.margin-right { margin-right:20px; }
.margin-top { margin-top:20px; }
.margin-bottom { margin-bottom:20px; }
.no-margin { margin:0; }
.margin-all { margin:20px; }
.clear { clear:both; }
.f-left { float:left; }
.f-right { float:right; }
.disabled {
  cursor:not-allowed !important;
  opacity:0.4;
  filter:alpha(opacity=40);
  -moz-opacity:0.4;
}
.button-link.disabled, .button-link.special.disabled { background:#F1F4F6; }
.grid { display:table; }
.grid-full {
  display:table;
  width:100%;
}
.grid-cell {
  display:table-cell;
  vertical-align:top;
}
.grid-cell._50pct { width:50%; }
.grid-cell._30pct { width:30%; }
.grid-1 { display:table-cell; }
.grid-2 {
  display:table-cell;
  vertical-align:top;
}
.grid_60px {
  display:table-cell;
  vertical-align:top;
  width:60px;
}
#main {
  display:block;
  background-color:#fff;
  color:#212121;
}
#body-popup { background-color:none; }
#body-popup #main {
  background:#F1F4F6 !important;
  padding:0;
}
#page-title + #main-content { padding-top:0; }
#main-content:after {
  content:"";
  display:table;
}
.header-layout {
  min-width:1024px;
  max-width:1024px;
  margin:0 auto;
}
.main-layout {
  min-width:1024px;
  max-width:1024px;
  margin:0 auto;
}
.main-layout.emblem-big {
  padding-top:25px;
  padding-bottom:65px;
}
#news-aside {
  max-width:285px;
  padding-left:35px;
}
@media print {
  header#header, footer#footer, #ich-info-feedback, nav.nav-left, aside.page-widget { display:none; }
}
.container .section-content { width:100%; }
@media (min-width: 992px) {
  .container .col-md-3+.col-md-9.section-content { width:75% !important; }
}
@media (min-width: 768px) {
  .container .col-sm-4+.col-sm-8.section-content { width:66%; }
}
.container .sp-wide { width:100%; }
.nav-left { padding-top:27px; }
.nav-left ul { float:left; }
.nav-left ul li.link-list {
  float:left;
  padding:0;
  width:auto;
}
.nav-left ul li.link-list a {
  border-bottom:4px solid transparent;
  color:#7F888F;
  padding-bottom:21px;
  text-decoration:none;
  transition:0.5s;
}
.nav-left ul li.link-list a:hover {
  border-bottom:4px solid #0077D4;
  text-decoration:none !important;
  transition:0.5s;
}
.nav-left ul li.link-list.selected a {
  border-bottom:4px solid #0077D4;
  text-decoration:none;
  transition:0.5s;
}
.nav-left .menu-title {
  color:#fff;
  float:left;
  font-size:14px;
  margin:0;
}
.nav-left .menu-title .link { color:#fff; }
.container fieldset { border:none; }
.container#page-main .row .col-lg-3 + .col-lg-9 { width:75%; }
.container#page-main .row .col-lg-9 { width:100%; }
.container .module h3.title.primary.concepts, .container .module h3.title.secondary.concepts {
  margin:30px 0 10px 0;
  text-transform:uppercase;
  font-size:0.90em;
}
.container .module ol.module-content.terms { margin-bottom:0; }
.container .module ol.module-content.terms .wrap {
  display:inline-block;
  margin-bottom:5px;
}
.container .aside {
  float:none;
  margin:0;
}
.container .aside .module {
  margin:20px 0 0 0;
  width:100%;
  background:#F1F4F6;
  border-radius:8px;
  padding:24px;
  position:relative;
}
.container .aside .module:first-child { margin:0; }
.container .aside .module .wiki-text, .container .aside .module .wiki-list { word-break:break-word; }
.container .aside .module .module-content { margin-bottom:20px; }
.container .aside .module.sp-country .module-header { background-color:#06a1b5; }
.container .aside .module.sp-country .module-header a { color:#fff; }
.container .aside .module.sp-country ul {
  margin:0;
  padding:0;
}
.container .aside .module.sp-country article { padding:5px 0; }
@media screen and (max-width: 768px) {
  .container .aside:not(.foo) { background:none; }
  .container .aside:not(.foo) .module.sp-country {
    border-bottom:1px solid #c7c7c7;
    border-top:1px solid #c7c7c7;
  }
  .container .aside:not(.foo) .module.sp-country .module-header { background:none; }
  .container .aside:not(.foo) .module.sp-country .module-header a {
    color:#06a1b5;
    font-weight:normal;
    position:relative;
    display:block;
  }
  .container .aside:not(.foo) .module.sp-country .module-header a:before {
    background-image:url('/design-img/icons/plus-20-gray.png');
    background-position:left top;
    height:20px;
    width:20px;
    position:absolute;
    top:0px;
    content:"";
    right:0;
  }
  .container .aside:not(.foo) .module .module-header {
    background:none;
    color:#58595b;
    font-weight:600;
    margin-bottom:19px;
    line-height:15px;
    position:relative;
  }
  .container .aside:not(.foo) .module .module-header h2 {
    font-size:18px;
    font-weight:600;
    margin-top:0;
  }
  .container .aside:not(.foo) .module .module-header .paper_clip {
    background-image:url('/design-img/icons/plus-20-gray.png');
    background-position:left top;
    height:20px;
    width:20px;
    position:absolute;
    top:6px;
  }
  .container .aside:not(.foo) .module .module-content {
    display:none;
    padding:5px 10px 10px;
    margin:0;
  }
  .container .aside:not(.foo) .module+.module { margin:10px 0 0 0; }
}
.container #page-menu {
  width:270px;
  font-size:0.86em;
  font-weight:bold;
  padding:0;
  margin:15px 0 10px 0;
  float:left;
  border-right:25px solid #FFF;
  background:#dadada;
}
.container .title-2 {
  color:#212121;
  font-size:2.25rem;
  margin-bottom:15px;
  clear:both;
}
.container .title-3 {
  color:#212121;
  margin-bottom:15px;
}
.container .title-4 {
  color:#58595b;
  margin-bottom:15px;
}
.container .padding { padding:20px 0; }
.container .clear { clear:both; }
.form {
  background-color:#F1F4F6;
  border-radius:8px;
  margin:0 0 20px;
  padding:32px 15px 32px 15px;
  clear:both;
  display:table;
  width:100%;
}
@media screen and (max-width: 768px) {
  .form { padding:10px; }
}
.form.margin-top { margin-top:20px; }
.form.sp-small dl { width:60%; }
@media screen and (max-width: 992px) {
  .form.sp-small dl { width:80%; }
}
@media screen and (max-width: 768px) {
  .form.sp-small dl { width:100%; }
}
@media screen and (max-width: 480px) {
  .form.sp-small dl { width:100%; }
}
.form fieldset {
  padding:0;
  margin:0;
}
.form.sp-left dl dt { width:20%; }
.form h2 { margin-top:0; }
.form .col-sm-6 dl { margin:0; }
.form .col-sm-6 dl dd { width:60%; }
@media screen and (max-width: 992px) {
  .form .col-sm-6 dl dt { width:38%; }
  .form .col-sm-6 dl dd { width:58%; }
}
@media screen and (max-width: 768px) {
  .form .col-sm-6 dl dt {
    width:100%;
    margin:0;
  }
  .form .col-sm-6 dl dd {
    width:100%;
    margin:0;
  }
}
@media screen and (max-width: 768px) {
  .form .col-sm-12 { float:none; }
}
.form dl {
  width:100%;
  display:inline-block;
  margin:0 0 10px;
}
.form dl dt, .form dl dd {
  float:left;
  margin-bottom:7px;
}
.form dl dt {
  width:40%;
  clear:left;
  text-align:right;
}
.form dl dd {
  width:60%;
  text-align:left;
  box-sizing:border-box;
}
@media screen and (max-width: 480px) {
  .form dl dd {
    width:61%;
    margin-left:0;
  }
}
.form dl dd .checkbox { display:inline-block; }
.form dl dd .criteria {
  top:2px;
  margin:0;
  height:48px;
  width:48px;
}
@media screen and (max-width: 768px) {
  .form dl dd .criteria {
    position:relative;
    left:0;
  }
}
.form dl .label-form {
  padding:9px 7px 0 0;
  width:100%;
  color:#585858;
  font-weight:bold;
  margin:0;
}
@media screen and (max-width: 768px) {
  .form dl .label-form { margin:0 0 7px 0; }
}
.form dl input:not([type='checkbox']), .form dl textarea {
  width:80%;
  border:1px solid #d4d4d4;
}
.form dl input:not([type='checkbox']).input-form, .form dl input:not([type='checkbox']).field_margin, .form dl textarea.input-form, .form dl textarea.field_margin { margin:0; }
@media screen and (max-width: 480px) {
  .form dl input:not([type='checkbox']), .form dl textarea { width:100%; }
}
.form dl input:not([type='checkbox']).remove-form, .form dl textarea.remove-form {
  border:none;
  width:20px;
  margin-left:5px;
  top:3px;
  position:relative;
}
.form dl .error-form {
  margin:5px 0 10px;
  color:#aa0000;
  font-size:12px;
  font-style:italic;
  padding:0px;
  font-weight:bold;
}
.form dl .radio_buttons-group .radio-group {
  display:inline-block;
  margin-top:7px;
}
.form dl .radio_buttons-group .radio-group .radio {
  display:inline-block;
  width:25px;
}
.form dl .radio_buttons-group .radio-group label { display:inline-block; }
.form dl .radio_buttons-group .radio-group label.radio-intention { padding:0; }
.form dl .composed-field .textarea-form, .form dl .field-form { border:1px solid #d4d4d4; }
.form dl .field-help {
  font-size:0.8em;
  font-style:italic;
  margin:5px 0 10px;
  color:#58595b;
}
.form dl .fixedWidth { width:80%; }
@media screen and (max-width: 768px) {
  .form dl .fixedWidth { width:100%; }
}
.form dl .sp-date .fixedWidth { width:40%; }
@media screen and (max-width: 768px) {
  .form dl .sp-date .fixedWidth { width:45%; }
}
.form .sp-big dl textarea {
  width:100%;
  min-height:150px;
}
@media screen and (max-width: 768px) {
  .form dl dt, .form dl dd {
    float:none;
    width:100%;
    text-align:left;
    margin:0;
  }
}
.form hr {
  clear:both;
  border-color:#fff;
}
@media screen and (max-width: 768px) {
  .form hr { margin-bottom:10px; }
}
.form .wrapper_select_doc input {
  display:block;
  margin-top:10px;
}
.form .wrapper_select_doc label { display:none; }
.form .wrapper_select_doc #uniform-account-face, .form .wrapper_select_doc #uniform-account-CV {
  display:inline-block;
  width:290px;
  margin-top:10px;
}
.form .wrapper_select_doc #desc-file {
  width:290px;
  display:inline;
}
@media screen and (max-width: 768px) {
  .form .wrapper_select_doc #desc-file { display:block; }
}
@media screen and (max-width: 992px) {
  .form .wrapper_select_doc #uniform-account-CV { margin-top:10px; }
}
.form .wrapper_select_doc #uniform-account-person_doc_type {
  display:inline-block;
  width:200px;
  margin-left:10px;
  margin-top:0;
}
@media screen and (max-width: 992px) {
  .form .wrapper_select_doc #uniform-account-person_doc_type {
    margin-left:0;
    margin-top:10px;
    width:290px;
  }
}
.form .wrapper_select_doc select+ select, .form .wrapper_select_doc select+ input, .form .wrapper_select_doc select+ .select, .form .wrapper_select_doc input+ select, .form .wrapper_select_doc input+ input, .form .wrapper_select_doc input+ .select { margin-top:10px; }
.form .button { margin:0 15px 20px 0; }
.form .block-nolegend+.block-nolegend {
  margin-top:10px;
  border-top:2px solid #fff;
  padding-top:25px;
}
.form#activity-filter, .form#patronageFilter { padding:20px 20px 0; }
label {
  margin-right:10px;
  font-weight:normal;
}
label.sp-checkbox, label.sp-checkboxstaff {
  line-height:18px;
  display:inline-block;
  margin:5px 0;
}
label.sp-checkbox .checkbox, label.sp-checkboxstaff .checkbox {
  top:0;
  display:inline;
  width:17px;
}
label.sp-checkbox .checkbox input[type=checkbox], label.sp-checkboxstaff .checkbox input[type=checkbox] {
  top:0;
  left:-2px;
}
label.sp-checkbox.sp-block, label.sp-checkboxstaff.sp-block { display:block; }
label .select2-container { margin:0; }
input[type=text], input[type=password], input[type=search], span.select2-selection {
  background:#fff;
  border:1px solid #D5DADD;
  border-radius:72px;
  color:#7F888F;
  font-size:16px;
  min-height:48px;
  line-height:48px;
  overflow:hidden;
  padding:0 20px;
  width:100%;
}
span.select2-selection .select2-selection__rendered { line-height:20px; }
.select2-container .select2-selection--multiple { display:inline-flex; }
input[read-only], input[disabled] { background-color:#dadada; }
input:read-only:not([type=button]):not([type=submit]), input:disabled:not([type=button]):not([type=submit]) { background-color:#dadada; }
label + input[type=text], input[type=password], input[type=search], .select2-selection { margin-top:5px; }
.fieldset_content { padding-top:2.8rem; }
.wrapper_radio {
  display:inline-block;
  position:relative;
  top:7px;
  margin-bottom:8px;
  margin-left:3px;
}
.wrapper_radio label {
  padding-left:3px;
  text-transform:capitalize;
}
.wrapper_radio .radio, .wrapper_radio input[type="radio"] {
  position:relative;
  margin:0;
  display:inline;
  width:12px;
}
.block_display {
  text-align:right;
  padding:15px 15px 0 0;
  background-color:#F1F4F6;
}
@media screen and (max-width: 480px) {
  .block_display { font-size:12px; }
}
.block_display label { width:auto; }
.block_display .block_display-list { display:inline-block; }
@media screen and (max-width: 480px) {
  .block_display .block_display-list { display:block; }
}
.block_display .block_display-list .fixedWidth { width:115px; }
.criteria {
  border:1px solid #D5DADD;
  border-radius:8px;
  cursor:pointer;
  color:#414042;
  font-weight:normal;
  display:inline-block;
  vertical-align:top;
  padding:10px 30px 10px 10px;
  position:relative;
  margin:10px 10px 0px 0;
}
.criteria > .delete {
  vertical-align:top;
  display:inline-block;
}
.criteria > .delete:after {
  content:"close";
  color:#D5DADD;
  font-family:'Material Icons Sharp';
  font-feature-settings:'liga' 1;
  font-weight:400;
  position:absolute;
  right:8px;
  top:9px;
  font-size:18px;
}
.block_tabs .tab-exam {
  margin:0;
  border:none;
  position:relative;
}
.block_tabs .tab-exam .tabItem, .block_tabs .tab-exam .tabItemFocus {
  margin:0;
  border-right:1px solid #fff;
}
.block_tabs .tab-exam .tab-style {
  border:none;
  width:auto;
  text-align:center;
  height:50px;
  line-height:17px;
  margin:0 !important;
  text-decoration:none;
  cursor:pointer;
  padding:5px 20px !important;
  background-color:#F1F4F6;
  color:#212121;
  box-sizing:border-box;
  display:table-cell;
  font-size:1.6rem;
  vertical-align:middle;
  font-weight:600;
}
@media screen and (max-width: 992px) {
  .block_tabs .tab-exam .tab-style { font-size:1.4rem; }
}
@media screen and (max-width: 480px) {
  .block_tabs .tab-exam .tab-style {
    padding:0 5px;
    height:40px;
    font-size:12px;
    line-height:12px;
    border-top:3px solid #c7c7c7;
    font-size:1rem;
  }
}
.block_tabs .tab-exam .tabItemFocus .tab-style {
  background-color:#F1F4F6;
  cursor:default;
  font-weight:600;
  border-top:4px solid #115A9E;
}
@media screen and (max-width: 480px) {
  .block_tabs .tab-exam .tabItemFocus .tab-style { border-top:3px solid #0077D4; }
}
@media screen and (max-width: 768px) {
  .tab-list-display {
    top:-50px;
    zoom:0.8;
  }
}
.content_tabs {
  padding:20px;
  background-color:#F1F4F6;
}
.list_years {
  text-align:left;
  padding:3.4rem 2rem;
  background-color:#F1F4F6;
}
@media screen and (max-width: 480px) {
  .list_years { font-size:12px; }
}
#map_canvas { background-color:#ebebeb; }
#map_canvas embed { width:100%; }
.list_news {
  margin:0px 0px 30px 0;
  display:block;
  clear:both;
}
.list_news +.list_news {
  margin-top:20px;
  padding-top:30px;
  border-top:1px solid #ccc;
}
.list_news +.list_news figure { float:left; }
.list_news .col-sm-3 { padding-right:0; }
@media screen and (max-width: 480px) {
  .list_news .col-sm-3 .picture.thumb { margin:0 auto; }
}
.staff {
  background-color:#ffcccc !important;
  border:1px solid #ffaaaa !important;
  padding:5px;
}
.staff:before, .cadenas:before {
  background:transparent url('../design-img/icons/cadenas.png') no-repeat right center;
  height:11px;
  width:11px;
  content:"\00A0\00A0\00A0\00A0\00A0\00A0";
}
.cd-top {
  display:inline-block;
  height:40px;
  width:40px;
  position:fixed;
  bottom:40px;
  right:10px;
  box-shadow:0 0 10px rgba(0,0,0,0.05);
  overflow:hidden;
  text-indent:100%;
  white-space:nowrap;
  background:rgba(0,119,212,0.8) url('../design-img/icons/top-arrow.png') no-repeat center 50%;
  visibility:hidden;
  opacity:0;
  -webkit-transition:opacity .3s 0s, visibility 0s .3s;
  -moz-transition:opacity .3s 0s, visibility 0s .3s;
  transition:opacity .3s 0s, visibility 0s .3s;
  -webkit-border-radius:10px;
  -moz-border-radius:10px;
  border-radius:10px;
}
.cd-top.cd-is-visible, .cd-top.cd-fade-out, .no-touch .cd-top:hover {
  -webkit-transition:opacity .3s 0s, visibility 0s 0s;
  -moz-transition:opacity .3s 0s, visibility 0s 0s;
  transition:opacity .3s 0s, visibility 0s 0s;
}
.cd-top.cd-is-visible {
  visibility:visible;
  opacity:1;
}
.cd-top.cd-fade-out { opacity:.5; }
.no-touch .cd-top:hover {
  background-color:#0077D4;
  opacity:1;
}
@media only screen and (min-width: 768px) {
  .cd-top {
    right:20px;
    bottom:20px;
  }
}
@media only screen and (min-width: 1024px) {
  .cd-top {
    height:50px;
    width:50px;
    right:30px;
    bottom:30px;
  }
}
.elem_list {
  margin:0 0 73px 0;
  overflow:hidden;
}
.elem_list figure .picture {
  border-radius:8px;
  margin-bottom:22px;
}
.elem_list figure .picture img { height:auto; }
.elem_list h3 { margin-top:0; }
.table-actions { float:right; }
.table-actions a { float:none; }
.table-actions .csv-export { font-size:1.3em; }
.element-dive {
  float:right;
  margin-top:10px;
}
.element-audio { margin-top:20px; }
.element-audio audio { width:100%; }
body #st-2 { text-align:left; }
#page-main .share-btns { margin-right:0; }
.home-widgets .share-btns {
  float:none;
  margin-top:0;
  margin-bottom:10px;
}
.link-all {
  border-top:1px solid #dadada;
  font-size:0.93em;
  display:block;
  line-height:1;
  margin:0;
  padding:15px 0 0;
  position:relative;
  text-align:right;
  color:#212121;
}
.link-list.pd-lft { font-size:0.95em; }
.link-list.pd-lft .link { color:#212121; }
.link-list.pd-lft2 { font-size:0.95em; }
.link-list.pd-lft2 .link { color:#212121; }
.link-list {
  display:inline-block;
  padding:10px 0px;
  width:100%;
  font-weight:bold;
  color:#212121;
}
.link-list:hover { text-decoration:underline; }
.link-list .link {
  display:block;
  padding:0 20px;
  text-decoration:none;
  color:#212121;
}
.link-list .arrow, .link-all .arrow {
  margin-left:10px;
  float:right;
}
.link.text { font-weight:bold; }
.link, .link-doc, .link-mailto, .link-unesdoc {
  display:inline-block;
  color:#0077D4;
  font-weight:bold;
}
.element-country .link {
  border:1px solid #D5DADD;
  border-radius:8px;
  color:#212121;
  display:inline-block;
  font-size:16px;
  font-weight:normal;
  margin-bottom:10px;
  padding:10px;
}
.link.secondary { font-weight:normal; }
.table-responsive > .table > thead > tr > th, .table-responsive > .table > tbody > tr > th, .table-responsive > .table > tfoot > tr > th, .table-responsive > .table > thead > tr > td, .table-responsive > .table > tbody > tr > td, .table-responsive > .table > tfoot > tr > td { white-space:normal; }
.activities .description {
  position:relative;
  margin:15px 0;
  padding:10px;
  background-color:#FFF;
}
.activities .description:after {
  clear:both;
  content:"";
  display:block;
}
.activities .description .friendly_url {
  display:block;
  text-align:right;
  margin:5px 0px 10px;
}
.title { font-weight:bold; }
.title.big {
  font-size:2.85714em;
  font-weight:bold;
  line-height:1.1;
  margin:0px 0px 25px;
}
.title.country {
  display:inline-block;
  text-transform:uppercase;
  background:none repeat scroll 0% 0% #0872CB;
  color:#E7F1FA;
  font-size:1em;
  margin:0px;
  padding:25px 15px;
  min-width:150px;
  text-align:center;
  top:-20px;
  position:relative;
}
.title.focus {
  line-height:1.1em;
  font-size:2.14em;
  margin:0;
}
.title.home {
  font-size:1.3em;
  color:#115A9E;
  margin:0 0 15px;
}
.title.news {
  line-height:1.1em;
  font-size:1.14em;
  margin:0;
}
.title.page {
  font-size:2.14em;
  color:#212121;
  margin:45px 0 15px;
}
.title.script {
  font-size:2.5em;
  color:#115A9E;
  padding:0 0 25px;
  margin:10px 0 0 0;
  background-color:#ebebeb;
}
.block_result {
  font-size:2.2em;
  font-weight:normal;
  margin-bottom:10px;
  margin:20px 0;
}
.block_result .elem_search { color:#115A9E; }
.block_result-list {
  background:#f4f4f4;
  padding:20px;
}
@media screen and (max-width: 768px) {
  .block_result-list { padding:10px 15px; }
}
.block_result-list .block_result-elem {
  clear:both;
  padding:20px 0;
}
.block_result-list .block_result-elem+.block_result-elem { border-top:1px solid #fff; }
.block_result-list .block_result-elem .result-elem-title {
  font-size:18px;
  text-transform:capitalize;
  display:block;
  margin-bottom:10px;
}
.block_result-list .block_result-elem .result-elem-desc {
  line-height:18px;
  font-size:small;
  text-align:left;
  color:#545454;
  display:inline;
}
.block_result-list .block_result-elem .copyright-search {
  font-size:0.928571em;
  color:#8A8A8B;
  font-style:italic;
  margin:5px 0;
  display:block;
}
.block_result-list #hdtbSum {
  border-bottom:2px solid #fff;
  height:70px;
}
@media screen and (max-width: 768px) {
  .block_result-list #hdtbSum {
    height:36px;
    display:block;
    clear:both;
    overflow:hidden;
  }
  .block_result-list #hdtbSum.open { height:190px; }
}
.block_result-list #hdtbSum #hdtb-msb .hdtb-mitem { display:inline-block; }
@media screen and (max-width: 768px) {
  .block_result-list #hdtbSum #hdtb-msb .hdtb-mitem {
    height:22px;
    line-height:24px;
    display:none;
  }
  .block_result-list #hdtbSum #hdtb-msb .hdtb-mitem.open { display:block; }
  .block_result-list #hdtbSum #hdtb-msb .hdtb-mitem a {
    margin:0;
    padding:0 20px 0 10px;
    font-size:14px;
  }
}
@media screen and (max-width: 768px) {
  .block_result-list #hdtbSum #hdtb-msb .hdtb-mitem.hdtb-msel {
    display:block;
    border:0;
    padding:0;
    margin:0;
    height:22px;
    line-heiht:24px;
  }
  .block_result-list #hdtbSum #hdtb-msb .hdtb-mitem.hdtb-msel a {
    color:#115A9E;
    position:relative;
  }
  .block_result-list #hdtbSum #hdtb-msb .hdtb-mitem.hdtb-msel a:after {
    content:"+";
    position:absolute;
    right:0;
    font-size:15px;
    top:0;
  }
}
.block_result-list #hdtb {
  background:#fff;
  color:#666;
  font-size:12px;
  outline-width:0;
  outline:none;
  position:relative;
  z-index:102;
}
.block_result-list .wrapper_country { display:inline; }
.search .title-search {
  min-width:100%;
  margin-top:15px;
}
@media screen and (max-width: 768px) {
  .search .title-search {
    top:-10px;
    padding:15px;
    margin-bottom:10px;
  }
}
.search .wrap_search {
  display:inline-block;
  position:relative;
  vertical-align:top;
}
.search .wrap_search input {
  width:100%;
  text-indent:10px;
  padding:0;
  margin-left:0;
}
.search .wrap_search .search-loupe {
  position:absolute;
  right:30px;
  top:20px;
  background:url('/design-img/icons/loupe-16.png') no-repeat;
  margin:0;
  padding:0 10px;
  text-align:center;
  cursor:pointer;
  border:0;
  height:16px;
  width:16px;
}
.search .mentions_search {
  font-size:11px;
  color:#777;
}
@media screen and (max-width: 992px) {
  .search #hdtb-msb .hdtb-mitem.hdtb-msel-pre { padding:0 2px; }
}
.button-link {
  background:#0872cb;
  display:inline-block;
  border:0;
  color:#e7f1fa;
  font-size:1.2em;
  margin:15px 0;
  padding:15px 40px;
  text-align:center;
  cursor:pointer;
}
.button-link.special { background:#01B2B1; }
.button-link:hover {
  text-decoration:none;
  color:#e7f1fa;
}
.home.container { padding:0; }
.home .article_box {
  padding:15px;
  background-color:#fff;
}
.home .actualite_box article {
  background-color:#fff;
  border-radius:10px;
  margin-bottom:30px;
}
.home .actualite_box .article_box-pic .picture {
  max-width:50%;
  margin-right:20px;
}
.home .actualite_box .article_box-pic .picture #news-focus-img {
  width:100%;
  display:block;
}
.home .actualite_box .article_box-pic .picture .copyright { display:none; }
@media screen and (max-width: 768px) {
  .home .actualite_box .article_box-pic .picture {
    margin-right:0;
    max-height:310px;
    text-align:center;
  }
  .home .actualite_box .article_box-pic .picture #news-focus-img {
    margin:0 auto;
    float:none;
    margin-bottom:15px;
  }
  .home .actualite_box .article_box-content { clear:both; }
}
.home .actualite_box .article_box-contrib {
  border-collapse:collapse;
  width:100%;
  font-size:0.8em;
}
.home .actualite_box .article_box-contrib td {
  border-top:#8A8A8B solid 1px;
  padding:5px;
}
@media screen and (max-width: 768px) {
  .home .event_highlight { margin-top:15px; }
}
.home #home-activities { margin-bottom:20px; }
.home #news-last-thumb { padding:0; }
.home #news-last-thumb .news-thumb {
  width:100%;
  background:none;
  padding:0;
}
.home #news-last-thumb .news-thumb h2 { margin:10px 0; }
.home #news-last-thumb .news-thumb ._ellipsis {
  margin:0 0 5px;
  display:block;
}
@media screen and (max-width: 992px) {
  .home #news-last-thumb { padding-bottom:0; }
  .home #news-last-thumb .news_box {
    width:100%;
    display:block;
  }
  .home #news-last-thumb .news_box .news-thumb {
    width:100%;
    margin:0;
    padding-bottom:10px;
    border-top:1px solid #dadada;
  }
  .home #news-last-thumb .news_box .news-thumb .crop, .home #news-last-thumb .news_box .news-thumb .newspaper {
    max-height:220px;
    height:auto;
  }
  .home #news-last-thumb .news_box .news-thumb .country { display:inline; }
}
@media screen and (max-width: 992px) {
  .home .events_box .infos_activity {
    border-top:1px solid #dadada;
    padding-top:10px;
  }
  .home .events_box .infos_activity:first-child {
    border-top:none;
    padding-top:0;
  }
}
.importante-news {
  display:flex;
  flex-wrap:wrap;
  overflow:hidden;
}
.importante-news .text, .importante-news .picture {
  border-radius:10px;
  flex:0 0 auto;
  position:relative;
  width:50%;
}
.importante-news .picture { border-radius:0 0.625rem 0.625rem 0; }
.importante-news .text {
  color:#fff;
  background-color:#0077D4;
  border-radius:0.625rem 0 0 0.625rem;
  padding:58px 66px;
}
.importante-news .text .date { margin:2.3rem 0; }
.importante-news .text .wiki-text { color:#fff; }
.importante-news .text .caption { color:#B2D6F2; }
.importante-news .text a { color:#fff; }
.importante-news .picture { background-size:cover; }
@media screen and (max-width: 992px) {
  .importante-news .text {
    border-radius:10px 10px 0 0;
    width:100%;
  }
  .importante-news .picture {
    border-radius:0 0 10px 10px;
    height:300px;
    width:100%;
  }
}
.banner-cta figure {
  display:flex;
  flex-wrap:wrap;
  margin-top:64px;
  overflow:hidden;
}
.banner-cta figure:first-child { margin-top:0; }
.banner-cta figure figcaption {
  flex:0 0 auto;
  position:relative;
  width:60%;
  z-index:100;
}
.banner-cta figure .img {
  flex:0 0 auto;
  overflow:hidden;
  transition:transform 2s;
  width:40%;
}
.banner-cta figure .img:hover { transform:scale(1.1); }
.banner-cta figure figcaption {
  background-color:#0077D4;
  padding:48px 0 48px 0;
}
.banner-cta figure figcaption .texte {
  background-color:rgba(0,119,212,0.3);
  border:1px solid #FFF;
  color:#FFF;
  left:48px;
  padding:60px 120px 60px 60px;
  position:relative;
}
.banner-cta figure:nth-child(2n-2)  figcaption { order:2; }
.banner-cta figure:nth-child(2n-2) .img { order:1; }
.banner-cta figure:nth-child(2n-2) figcaption .texte {
  left:auto;
  right:48px;
  padding:60px 60px 60px 120px;
  position:relative;
}
@media screen and (max-width: 768px) {
  .banner-cta figure figcaption {
    padding:0 48px;
    width:100%;
  }
  .banner-cta figure .img {
    height:300px;
    width:100%;
  }
  .banner-cta figure figcaption .texte, .banner-cta figure:nth-child(2n-2) figcaption .texte {
    right:auto;
    left:auto;
    top:48px;
    padding:60px 60px 120px 60px;
  }
  .banner-cta figure:nth-child(2n-2)  figcaption { order:1; }
  .banner-cta figure:nth-child(2n-2) .img { order:2; }
}
.banner-cta figure {
  display:flex;
  flex-wrap:wrap;
  margin-top:64px;
  overflow:hidden;
}
.banner-cta figure:first-child { margin-top:0; }
.banner-cta figure figcaption {
  flex:0 0 auto;
  position:relative;
  width:60%;
  z-index:100;
}
.banner-cta figure .img {
  flex:0 0 auto;
  overflow:hidden;
  transition:transform 2s;
  width:40%;
}
.banner-cta figure .img:hover { transform:scale(1.1); }
.banner-cta figure figcaption {
  background-color:#0077D4;
  padding:48px 0 48px 0;
}
.banner-cta figure figcaption .texte {
  background-color:rgba(0,119,212,0.3);
  border:1px solid #FFF;
  color:#FFF;
  left:48px;
  padding:60px 120px 60px 60px;
  position:relative;
}
.banner-cta figure:nth-child(2n-2)  figcaption { order:2; }
.banner-cta figure:nth-child(2n-2) .img { order:1; }
.banner-cta figure:nth-child(2n-2) figcaption .texte {
  left:auto;
  right:48px;
  padding:60px 60px 60px 120px;
  position:relative;
}
@media screen and (max-width: 768px) {
  .banner-cta figure figcaption {
    padding:0 48px;
    width:100%;
  }
  .banner-cta figure .img {
    height:300px;
    width:100%;
  }
  .banner-cta figure figcaption .texte, .banner-cta figure:nth-child(2n-2) figcaption .texte {
    right:auto;
    left:auto;
    top:48px;
    padding:60px 60px 120px 60px;
  }
  .banner-cta figure:nth-child(2n-2)  figcaption { order:1; }
  .banner-cta figure:nth-child(2n-2) .img { order:2; }
}
#home-saveguarding .container, #home-dive .container { padding-bottom:0; }
#home-saveguarding .emblem-big, #home-dive .emblem-big {
  padding-top:25px;
  padding-bottom:65px;
}
@media screen and (max-width: 768px) {
  #home-saveguarding .emblem-big, #home-dive .emblem-big { padding:20px; }
  #home-saveguarding .emblem-big .col-xs-12+.col-xs-12, #home-dive .emblem-big .col-xs-12+.col-xs-12 { margin-top:15px; }
  #home-saveguarding .emblem-big .element_videos, #home-dive .emblem-big .element_videos { display:none; }
}
@media screen and (max-width: 1200px) {
  #home-forward .sp-interviews .video-thumb { width:109px; }
}
.sp-select-home .fixedWidth { width:100%; }
.sp-select-home .fixedWidth .arrow { width:32px; }
.elem-article-inline {
  float:right;
  width:300px;
  text-align:center;
  clear:right;
  margin-top:-30px;
}
@media screen and (max-width: 768px) {
  .elem-article-inline {
    float:none;
    width:auto;
    margin:20px 0;
    background-color:#f4f4f4;
    padding:10px 0;
  }
}
.link-rss { float:right; }
.news_item-activity {
  margin:20px 0;
  min-height:45px;
}
.news_item-activity .item-activity {
  text-decoration:none;
  text-align:left;
  display:block;
  width:45%;
}
.news_item-activity .item-activity.sp-left { float:left; }
.news_item-activity .item-activity.sp-right { float:right; }
.news_item-activity .item-activity .item-arrow {
  display:inline-block;
  vertical-align:top;
  height:24px;
  width:24px;
}
.news_item-activity .item-activity .item-link {
  display:inline-block;
  line-height:1.05em;
  width:84%;
}
@media screen and (max-width: 768px) {
  .news_item-activity .item-activity { width:47%; }
  .news_item-activity .item-activity .item-link {
    width:80%;
    font-size:13px;
  }
}
.news_item-plus { clear:both; }
.wrapper_dropdwon { border-bottom:1px solid #ccc; }
.wrapper_dropdwon .dropdown-content {
  padding:20px;
  background-color:#eee;
  margin-bottom:20px;
}
.wrapper_dropdwon .dropdown-content.sp-white { background-color:#fff; }
dl dd.full { width:100%; }
dl dd.full #uniform-registration-v2-cbdoc-accept_research, dl dd.full #uniform-registration-v2-cbdoc-accept_CBDOC { margin:0px 15px 60px -22px !important; }
dl dd.full .label-form {
  padding:0;
  font-weight:normal;
  font-size:14px;
}
.wrapper_captcha { margin:0 0 20px; }
.wrapper_captcha .captcha-img { margin:0 0 15px; }
.wrapper_captcha .captcha-img img { width:180px; }
.wrapper_captcha input { width:180px !important; }
.sp-list .col-xs-12 .col-xs-12 {
  margin:0;
  padding:0;
}
.sp-list .input-2, .sp-list .select.fixedWidth {
  width:67%;
  margin:10px 0;
}
.sp-list label.form_label {
  text-align:right;
  width:auto;
}
@media screen and (max-width: 992px) {
  .sp-list label.form_label { text-align:left; }
}
@media screen and (max-width: 768px) {
  .sp-list label.form_label { width:25%; }
}
#map { height:400px; }
.photo-subjects {
  background-color:#ccc;
  padding:10px;
}
.photo-results .photo-div {
  color:#000;
  height:250px;
  margin-bottom:10px;
  padding:10px 0;
  text-align:center;
}
.photo-results .photo-div dl {
  padding-top:5px;
  color:#868686;
  font-weight:normal;
  font-size:0.85em;
}
.photo-results .photo-div img {
  max-height:150px;
  border-radius:15px;
}
.photo-results dt {
  font-size:1.15em;
  color:#000;
}
.photo-nav { width:100%; }
#photo-zone { margin:0; }
#photo-zone .photo { margin:0; }
#photo-zone .photo .photo-div {
  max-height:400px;
  overflow-y:auto;
}
#photo-zone .photo-legend-slideshow { min-height:25px; }
#photo-zone .photo-legend-slideshow, #photo-zone .photo-legend { padding:1.4rem; }
.photoBlock-legend-action {
  bottom:82px;
  position:absolute;
  right:62px;
}
.photoBlock-legend-action .popupFancyBox:after {
  content:"";
  background:transparent url("../design-img/infos.svg") no-repeat;
  height:50px;
  position:absolute;
  width:50px;
}
.aside .photoBlock-legend-action { display:none; }
.section-content .photoBlock-legend-action {
  top:10px;
  bottom:auto;
}
body .photoBlock-photo { display:inline; }
body .photoBlock-photo img {
  border-radius:8px;
  width:100%;
  height:auto;
}
.photo-legend-slideshow .wiki-text {
  color:#FFF;
  margin:0;
}
.csv-export { float:right; }
#dive-constellation iframe, #dive-biome iframe, #dive-domain iframe, #dive-threat iframe, #dive-sdg iframe {
  border:none;
  height:1200px;
  width:100%;
}
#dive-constellation iframe.dive_with_border, #dive-biome iframe.dive_with_border, #dive-domain iframe.dive_with_border, #dive-threat iframe.dive_with_border, #dive-sdg iframe.dive_with_border { border:1px solid rgba(0,0,0,0.2); }
.home_dive_container { position:relative; }
.dive_modal_link {
  display:block;
  float:right;
  font-size:1.5em;
  position:absolute;
  top:20px;
  right:20px;
  color:rgba(0,0,0,0.5);
}
.dive_link {
  display:inline-block;
  width:60px;
  height:60px;
  background:transparent url(/design-img/icon/icon_dive_32.png) no-repeat right center;
  background-size:100% auto;
}
.dive_link.constellation { background-image:url(https://ich.unesco.org/img/photo/thumb/13107-THB.jpg); }
.dive_link.sdg { background-image:url(https://ich.unesco.org/img/photo/thumb/15178-THB.png); }
.dive_link.biome { background-image:url(https://ich.unesco.org/img/photo/thumb/13108-THB.jpg); }
.dive_link.threat { background-image:url(https://ich.unesco.org/img/photo/thumb/13109-THB.jpg); }
.dive-browser-msg {
  margin:1.5rem 0;
  color:red;
  display:none;
}
#dive_modal {
  height:1200px;
  padding:10px 15px;
}
#st-1 { z-index:1 !important; }
.select2-results__option { padding:1px 5px !important; }
.select2-results__options--nested { margin-left:15px; }
.translation-convention .toc {
  margin-top:10%;
  width:100%;
}
.translation-convention .section-content dd { margin-left:40px; }
.translation-convention .section-content dd.title-convention-ar { text-align:right; }
.module-content-pandemic .select2-selection { margin-bottom:15px; }
.module-content-pandemic .select2-container .select2-selection--multiple .select2-selection__rendered { white-space:normal !important; }
.module-content-pandemic input.select2-search__field { text-indent:5px; }
span.select2-dropdown.select2-dropdown--below { width:365px !important; }
.navbar-collapse .menu-dropdown { right:0; }
.menu-dropdown {
  background:#ffffff none repeat scroll 0% 0%;
  border:1px solid #D5DADD;
  border-radius:10px;
  color:#212121;
  display:block;
  min-width:120px;
  padding:15px;
  position:absolute;
  z-index:100;
}
.menu-dropdown li {
  margin:0 0 15px 0 !important;
  text-align:left;
  width:100%;
}
.menu-dropdown li:last-child { margin-bottom:0 !important; }
.menu-dropdown li a { color:#7F888F; }
.menu-dropdown li a:hover, .menu-dropdown li a.langue-active { color:#212121; }
.container-1200 {
  margin:0 auto;
  max-width:1200px;
  padding:0 15px;
}
.container-768 {
  margin:0 auto;
  max-width:768px;
}
.text-dark { color:#212121 !important; }
.bg-light { background-color:#F1F4F6; }
.background-ngo-01 { background-color:#ffffff; }
.background-ngo-02 {
  background-color:#326DED;
  color:#fff;
}
.background-ngo-03 {
  background-color:#7d0aa1;
  color:#fff;
}
.background-ngo-04 { background-color:#fabf3d; }
.background-ngo-05 { background-color:#f39200; }
.background-ngo-06 { background-color:#00a75d; }
.background-ngo-07 {
  background-color:#ed3d1a;
  color:#fff;
}
.h2 { font-size:2.25rem; }
.font-weight-bold { font-weight:700 !important; }
.position-relative { position:relative; }
.position-absolute { position:absolute !important; }
.right-0 { right:0; }
.top-5 { top:5px; }
.top-20 { top:20px; }
.justify-end { justify-content:flex-end; }
.justify-center { justify-content:center; }
.d-flex { display:flex; }
.align-center { align-items:center; }
.d-block { display:block; }
.d-inline-block { display:inline-block; }
.rounded { border-radius:0.625rem !important; }
.nav-link a {
  font-size:.85em;
  display:block;
  color:#212121;
  padding:.4rem 1rem;
}
a { cursor:pointer; }
.mt-0 { margin-top:0 !important; }
.m-0 { margin:0; }
.m-1 { margin:1rem; }
.m-2 { margin:2rem; }
.m-25 { margin:2.5rem; }
.m-3 { margin:3rem; }
.m-4 { margin:4rem; }
.p-4 { padding:1.5rem !important; }
.p-25 { padding:2.5rem; }
.p-21 { padding:2.1rem !important; }
.mb-4, .my-4 { margin-bottom:1.5rem !important; }
.mb-5, .my-5 { margin-bottom:3rem !important; }
.mb-20 { margin-bottom:2rem !important; }
.mb-32, .my-32 { margin-bottom:3.2rem !important; }
.mb-moins-30 { margin-bottom:-3rem !important; }
.mt-3, .my-3 { margin-top:1rem !important; }
.mt-4, .my-4 { margin-top:1.5rem !important; }
.mt-5, .my-5 { margin-top:3rem !important; }
.mt-13 { margin-top:1.3rem; }
.mt-20 { margin-top:2rem !important; }
.mt-32, .my-32 { margin-top:3.2rem !important; }
.mt-40, .my-40 { margin-top:4rem !important; }
.mt-45, .my-45 { margin-top:4.5rem !important; }
.mt-50, .my-50 { margin-top:5rem !important; }
.mt-64, .my-64 { margin-top:6.4rem !important; }
.mt-94, .my-94 { margin-top:9.4rem !important; }
.mb-64, .my-64 { margin-bottom:6.4rem !important; }
.mr-10 { margin-right:1rem !important; }
.pr-50 { padding-right:5rem !important; }
.pt-24, .py-24 { padding-top:2.4rem !important; }
.mt-2, .my-2 { margin-top:.5rem !important; }
.mb-2 { margin-bottom:.5rem !important; }
.px-2 {
  padding-left:15px !important;
  padding-right:15px !important;
}
.pt-3, .py-3 { padding-top:1rem !important; }
.pt-4, .py-4 { padding-top:1.5rem !important; }
.mt-24, .my-24 { margin-top:2.4rem !important; }
.pt-5, .py-5 { padding-top:3rem !important; }
@media screen and (max-width: 992px) {
  .mt-md-32 { margin-top:3.2rem !important; }
}
.border-top { border-top:1px solid #D5DADD !important; }
.h-100 { height:100% !important; }
.min-width-100 { min-width:100px; }
.min-width-200 { min-width:200px; }
.min-width-300 { min-width:300px; }
.max-width-100 { max-width:100px; }
.max-width-200 { max-width:200px; }
.max-width-300 { max-width:300px; }
.width-100 { width:100px; }
.width-200 { width:200px; }
.width-300 { width:300px; }
.width-25p { width:25%; }
.width-33p { width:33%; }
.width-50p { width:50%; }
.width-75p { width:75%; }
.width-100p { width:100%; }
.bg-light-blue { background-color:#B2D6F2; }
.bg-light-gray { background:#F1F4F6 !important; }
.related-theme {
  display:inline-block;
  border:1px solid #D5DADD;
  border-radius:8px;
  margin-right:10px;
  padding:10px;
}
.card-link-wrapper {
  text-decoration:none;
  color:#212121;
}
.card {
  background-color:#F1F4F6;
  background-clip:border-box;
  border:0 solid rgba(33,33,33,0.125);
  border-radius:.5rem;
  min-width:0;
  overflow:hidden;
  position:relative;
  word-wrap:break-word;
}
.card a {
  display:block;
  height:100%;
  position:relative;
  width:100%;
}
.card a .arrow {
  background:url(../design-img/round-arrow.svg);
  height:24px;
  transition:transform 0.5s ease;
  position:absolute;
  right:15px;
  top:10px;
  width:24px;
}
.card:hover .arrow { transform:translate(8px,0); }
.card .text-3-lines {
  overflow:hidden;
  text-overflow:ellipsis;
  display:-webkit-box;
  -webkit-line-clamp:3;
  line-clamp:3;
  -webkit-box-orient:vertical;
}
.card-body {
  min-height:1px;
  padding:1.5rem;
}
.card-body h2 { font-size:1.5rem; }
.card-body a { color:#212121; }
.card-body a.btn { color:#fff; }
.card-title {
  font-weight:600;
  line-height:1.3;
  margin-bottom:1.5rem;
  width:calc(98%);
}
.card-img-top {
  -o-object-fit:cover;
  object-fit:cover;
  width:100%;
  height:100%;
  max-height:15rem;
}
body .documents, body .periodic-reporting { padding-top:101px !important; }
body .documents:before, body .periodic-reporting:before {
  content:"";
  height:72px;
  position:absolute;
  top:21px;
  width:72px;
}
body .documents:before { background:transparent url(../design-img/doc.svg) no-repeat; }
body .periodic-reporting:before { background:transparent url(../design-img/periodic-reporting.svg) no-repeat; }
.button:disabled { background:#82add0; }
.button:hover {
  background:#115A9E;
  transition:0.5s;
}
.btn {
  border:0;
  border-radius:24px;
  font-size:14px;
  height:4.8rem;
  line-height:4.8rem;
  margin:0;
  padding:0 25px;
  text-align:center;
  transition:color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}
.btn-outline-secondary {
  color:#7F888F;
  cursor:pointer;
  border:1px solid #7F888F;
}
.btn-outline-secondary:hover {
  color:#4C5054;
  border-color:#4C5054;
}
.btn-primary {
  background:#0077D4;
  color:#fff;
}
.btn-primary:hover, .btn-primary:focus {
  color:#fff;
  background-color:#115A9E;
}
.btn-gray4 {
  color:#fff;
  background-color:#7F888F;
  border-color:#7F888F;
}
.btn-gray4:hover {
  background-color:#4C5054;
  border-color:#4C5054;
  color:#fff;
}
.btn-secondary {
  background-color:#115A9E;
  color:#fff;
}
.btn-secondary:hover {
  color:#fff;
  background-color:#0E4280;
}
.btn-infos {
  background:#7F888F;
  border-radius:50%;
  padding:14px 23px;
  width:50px;
  height:50px;
  display:block;
  font-size:16px;
  font-weight:800;
  color:#FFF;
}
.btn-action {
  display:flex;
  width:100%;
  height:85px;
}
.btn-action a {
  color:#fff;
  padding-right:50px;
  padding-left:10px;
}
.btn-action .btn-text {
  display:flex;
  align-items:center;
  justify-content:center;
}
.btn-action .btn-icon {
  vertical-align:middle;
  font-size:1.4em;
  border-radius:50%;
  width:40px;
  height:40px;
  text-align:center;
  position:absolute;
  left:auto !important;
  right:30px;
  top:23px;
}
.popin-hover {
  background:#fff;
  border:1px solid #D5DADD;
  border-radius:1rem;
  padding:1.5rem;
}
.popin-hover p:last-child { margin-bottom:0; }
.ui-widget-header {
  border:0px !important;
  background:none !important;
}
.ui-widget-content { max-width:600px; }
#user-prof-div ul { padding:0; }
#user-prof-div ul li {
  border-bottom:1px solid #D5DADD;
  padding:3.8rem 0;
}
#user-prof-div ul li:first-child { padding-top:0; }
#list-display label + select { margin-top:0; }
.row-flex {
  --bs-gutter-x:1.875rem;
  --bs-gutter-y:0;
  display:-webkit-box;
  display:-ms-flexbox;
  display:flex;
  -ms-flex-wrap:wrap;
  flex-wrap:wrap;
  margin-top:calc(-1 * var(--bs-gutter-y));
  margin-right:-15px;
  margin-left:-15px;
}
.row-flex > * {
  flex-shrink:0;
  width:100%;
  max-width:100%;
  padding-right:15px;
  padding-left:15px;
  margin-top:var(--bs-gutter-y);
}
.row-flex .col-4 {
  -webkit-box-flex:0;
  -ms-flex:0 0 auto;
  flex:0 0 auto;
  width:25%;
}
@media (max-width: 1200px) {
  .row-flex .col-4 {
    -webkit-box-flex:0;
    -ms-flex:0 0 auto;
    flex:0 0 auto;
    width:33.33333333%;
  }
}
@media (max-width: 768px) {
  .row-flex .col-4 {
    -webkit-box-flex:0;
    -ms-flex:0 0 auto;
    flex:0 0 auto;
    width:50%;
  }
}
@media (max-width: 576px) {
  .row-flex .col-4 {
    -webkit-box-flex:0;
    -ms-flex:0 0 auto;
    flex:0 0 auto;
    width:100%;
  }
}
.row-flex .col-3 {
  -webkit-box-flex:0;
  -ms-flex:0 0 auto;
  flex:0 0 auto;
  width:33.33333333%;
}
@media (max-width: 768px) {
  .row-flex .col-3 {
    -webkit-box-flex:0;
    -ms-flex:0 0 auto;
    flex:0 0 auto;
    width:50%;
  }
}
@media (max-width: 576px) {
  .row-flex .col-3 {
    -webkit-box-flex:0;
    -ms-flex:0 0 auto;
    flex:0 0 auto;
    width:100%;
  }
}
.title-12 {
  font-size:1.2rem;
  font-weight:600;
  margin:0 0 2rem 0;
  text-transform:uppercase;
}
@media screen and (max-width: 992px) {
  .d-lg-none { display:none !important; }
}
.img-responsive { max-width:100%; }
.logo-grid .logo { padding:15px 10px; }
.logo-grid .logo .image-style-logo-grid {
  max-width:100%;
  max-height:185px;
}
.logo-grid .inverse { background-color:#919191; }
#exam-title {
  margin:15px 15px 10px;
  font-size:1.5em;
  font-weight:normal;
  text-align:center;
}
#exam-log {
  background:#999 !important;
  color:#FFF !important;
}
#exam-guide {
  position:absolute;
  top:5px;
  left:10px;
}
#exam-data { width:100%; }
#exam-data td {
  vertical-align:top;
  border:1px solid #CCC;
  padding:5px;
}
#exam-submit { line-height:2em; }
.exam-item {
  padding:10px;
  background:#F2F6F7;
  border:1px solid #C4CFD8;
  width:780px;
  margin-top:-10px;
  margin-bottom:10px;
}
.label_radio { display:block; }
.exam-item textarea {
  width:100%;
  height:150px;
}
.exam-info {
  max-height:100px;
  overflow:auto;
  margin:0 0 10px;
  padding:5px;
  background:#F2F6F7;
  border:1px solid #888;
  font-size:.9em;
  color:#4C4C4C;
}
.exam-sum {
  position:relative;
  display:block;
  float:left;
  padding-right:20px;
  margin-top:10px;
  margin-bottom:10px;
}
.em_1 { font-weight:bold; }
#undefined, #satisfied, #unsatisfied { font-style:italic; }
.exam-legend {
  display:block;
  left:0;
  height:30px;
  line-height:30px;
  padding:0 20px;
  overflow:hidden;
}
.exam-tabLabel {
  position:relative;
  padding:0;
  background:#025A9C;
  color:#FFF;
  font-weight:bold;
  cursor:pointer;
}
.exam-tabLabel:hover {
  background:#025A9C;
  color:#FFF;
  opacity:.5;
}
.exam-tabLabelSelected {
  position:relative;
  padding:0;
  background:#D1DDEF;
  border:1px solid #CCC;
  border-bottom:none;
  font-weight:bold;
}
.exam-tabSelected {
  clear:both;
  position:absolute;
  top:40px;
  left:0;
}
.exam-tabItem, .exam-tabItemFocus {
  display:inline-block;
  vertical-align:bottom;
  margin:0 5px 0 0;
  padding:0;
  border:none;
  overflow:hidden;
  white-space:normal;
}
.tab-exam { border-bottom:1px solid #0872cb; }
.tab-label a {
  color:white;
  font-weight:bold;
  height:100%;
  width:100%;
}
.button-3 {
  text-align:center;
  min-width:160px;
  line-height:2.2em;
  font-size:1em;
  height:35px;
  display:inline-block;
}
.arrow.gray {
  display:inline-block;
  height:15px;
  width:15px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -185px -247px;
}
.arrow.blue {
  display:inline-block;
  height:15px;
  width:15px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -185px -262px;
}
.arrow.white.selected {
  display:inline-block;
  height:15px;
  width:15px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -185px -277px;
}
.calendar {
  display:inline-block;
  height:18px;
  width:17px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -168px -281px;
  margin:0 10px 0 0;
  vertical-align:middle;
}
.calendar_w {
  display:inline-block;
  height:21px;
  width:21px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -200px -404px;
}
.financial_w {
  display:inline-block;
  height:21px;
  width:21px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -200px -383px;
}
.paper_clip {
  display:inline-block;
  height:16px;
  width:17px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -168px -359px;
  margin:0 13px 0 -27px;
}
.no_paper_clip {
  display:inline-block;
  height:16px;
  width:17px;
  margin:0 13px 0 -27px;
}
.no_paper_clip img { margin-bottom:8px; }
.play {
  display:inline-block;
  width:24px;
  height:23px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -168px -504px;
}
.pointer {
  display:inline-block;
  height:18px;
  width:17px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -168px -299px;
  margin:0 10px 0 0;
  vertical-align:middle;
}
.pointer_w {
  display:inline-block;
  height:23px;
  width:17px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -168px -336px;
}
.spanfeed_rss14x14 {
  display:inline-block;
  height:14px;
  width:14px;
  margin-right:5px;
  background:url('../design-img/feed-icon-14x14.png') no-repeat;
  position:relative;
  top:2px;
}
.spanfeed_rss28x28 {
  display:inline-block;
  height:28px;
  width:28px;
  background:url('../design-img/feed-icon-28x28.png') no-repeat;
  position:relative;
  top:2px;
}
.afeed14x14 {
  margin-left:3px;
  padding:0 0 0 19px;
  background:url('../design-img/feed-icon-14x14.png') no-repeat 0 50%;
}
.afeed28x28 {
  margin-left:3px;
  padding:0 0 0 19px;
  background:url('../design-img/feed-icon-28x28.png') no-repeat 0 50%;
}
.btn-info {
  background-color:#F6DC12 !important;
  border-color:#F6DC12 !important;
}
#actions_bar {
  display:table;
  width:100%;
  clear:both;
  overflow:hidden;
  padding:4px 0;
}
.action {
  display:table-cell;
  border-right:1px solid #c7c7c7;
  width:44px;
  height:38px;
}
.anchor {
  display:block;
  position:relative;
  top:-10px;
}
.blue_0 { color:#115A9E !important; }
ul.bullet {
  list-style:disc outside none;
  padding-left:20px;
}
ol.bullet {
  list-style:decimal outside none;
  padding-left:20px;
}
#breadcrumbs {
  font-size:0.86em;
  padding:7px;
}
.caption {
  font-size:1.4rem;
  color:#7F888F;
}
.countries {
  color:#06a1b5;
  padding:5px 0 0;
  display:inline-block;
}
.country {
  text-transform:capitalize;
  color:#0077D4;
  white-space:initial;
  font-weight:normal;
}
.country + .separator:last-child { display:none; }
.date {
  color:#4FB293;
  font-weight:normal;
}
.dates {
  color:#4FB293;
  font-weight:normal;
  padding:5px 0 0;
  white-space:nowrap;
  display:inline-block;
}
.debug {
  position:absolute;
  top:40%;
  left:50%;
  background-color:#EEE;
  color:#000;
  padding:10px;
  width:50%;
  margin:0 -25%;
  z-index:100;
  box-shadow:1px 1px 12px #555;
}
.download {
  display:inline-block;
  color:#0077D4;
  font-weight:bold;
  text-decoration:underline;
}
.ellipsis {
  display:block;
  overflow:hidden;
  overflow-wrap:break-word;
  position:relative;
  z-index:1;
}
.ellipsis:before {
  content:'\00a0\00a0…';
  position:absolute;
  bottom:0;
  right:0;
  z-index:10;
  font-weight:bold;
  background:#EBEBEB;
  padding:0 0 0 5px;
  opacity:.7;
}
.ellipsis.white:before { background:#FFF; }
.ellipsis.white:before { background:#FFF; }
.ellipsis-1 {
  height:1.25em;
  overflow:hidden;
  display:inline-block;
  position:relative;
}
.ellipsis-1:before {
  content:'…';
  position:absolute;
  bottom:0;
  right:0;
  z-index:10;
  font-weight:bold;
  background:#EBEBEB;
  padding:0 5px;
}
.ellipsis-3 {
  max-height:4.28571em;
  overflow:hidden;
  overflow-wrap:break-word;
  position:relative;
  z-index:1;
}
.ellipsis-3:before {
  content:'…';
  position:absolute;
  bottom:0;
  right:0;
  z-index:10;
  font-weight:bold;
  background:#EBEBEB;
  padding:0 5px;
}
.ellipsis-6 {
  max-height:8.37em;
  overflow:hidden;
  overflow-wrap:break-word;
  position:relative;
}
.ellipsis-6:before {
  content:'…';
  position:absolute;
  bottom:0;
  right:0;
  z-index:10;
  font-weight:bold;
  background:#EBEBEB;
  padding:0 5px;
}
.emblem-big { background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -221px -625px; }
#event-tabs {
  display:table;
  width:100%;
}
#event-tabs .tab:not(.selected) { cursor:pointer; }
.filter .label {
  font-size:0.857143em;
  display:inline-block;
  font-weight:bold;
  margin:0 10px 0 0;
}
.filter.brown { background-color:#E9E1C9; }
#footer { height:100%; }
.forward-header {
  padding-bottom:35px;
  max-height:75px;
}
.forward-div { min-height:345px; }
#goto-home {
  height:134px;
  display:inline-block;
}
.gray_2 { color:#212121 !important; }
#header-actions {
  display:table-cell;
  vertical-align:top;
  width:328px;
  border-spacing:5px 0;
}
.hide-default > option:first-child { display:none; }
.highlight {
  background:#ebebeb;
  padding:20px;
}
.highlight.center { text-align:center; }
.highlight:nth-child(odd) { background:#dadada; }
.input_text {
  background:transparent;
  display:inline-block;
  padding:5px 10px;
  border:1px solid #fff;
  font-size:12px;
  color:#fff;
}
.sm-row + .sm-row {
  border-top:1px solid #d4d4d4;
  padding:10px 0;
}
.sm-row:first-child { padding-top:10px; }
.sm-row:last-child { padding-bottom:0; }
#home-forward { margin-top:20px; }
#home-lists {
  max-width:295px;
  padding:0 0 0 25px;
}
#home-projects {
  max-width:295px;
  padding:0 25px;
}
#home-saveguarding, #home-dive {
  background-color:#dadada;
  margin-top:20px;
}
#home-videos {
  max-width:295px;
  padding:0 25px 0 0;
}
.input.element {
  display:inline-block;
  padding:10px 9px;
  border:1px solid #D4D4D4;
}
.intro {
  font-weight:bold;
  font-size:1.14em;
}
li.newspaper { margin:0 0 5px 0; }
#news-focus {
  display:inline-block;
  margin-left:60px;
  background-color:#fff;
  padding:20px;
  padding-bottom:0px;
  margin:0px 0px 35px;
}
#news-focus-img {
  float:left;
  max-height:100%;
}
#news-last { max-width:660px; }
#news-last-thumb { padding-bottom:20px; }
.news-thumb {
  width:100%;
  overflow:hidden;
  text-align:left;
  display:inline-block;
  vertical-align:top;
}
.padding-with-menu { padding:20px 20px 20px 275px; }
#pager {
  border-top:1px solid #D4D4D4;
  font-size:0.93em;
  clear:both;
  margin:20px 0 0;
  padding:10px 0 0;
  text-align:center;
}
#pager .list, #pager .list-item {
  list-style:none outside none;
  margin:0 15px;
  padding:0;
  display:inline-block;
  vertical-align:middle;
}
#pager .list .item, #pager .list-item .item {
  color:#0872CB;
  font-weight:bold;
}
#pager .list .selected, #pager .list-item .selected { color:#414042; }
#pager .step {
  display:inline-block;
  vertical-align:middle;
  color:transparent;
  height:22px;
  overflow:hidden;
  white-space:nowrap;
  width:21px;
  text-indent:42px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat scroll -200px -447px transparent;
}
#pager .step.disabled {
  opacity:0.5;
  cursor:default;
}
#pager .first {
  height:21px;
  background-position:-200px -491px;
}
#pager .next { background-position:-200px -469px; }
#pager .last {
  height:21px;
  background-position:-200px -512px;
}
#page-main {
  background:#fff;
  height:100%;
  position:relative;
  margin-top:3.8rem;
}
#page-title {
  display:table-cell;
  vertical-align:top;
  padding:20px 20px 20px 20px;
}
#page-menu ~ #page-title {
  padding-left:0px;
  width:765px;
}
.picture, .picture_home { margin:0; }
.picture.thumb, .picture_home.thumb { margin:0 20px 0 0; }
.print {
  display:block;
  width:24px;
  height:20px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -197px -100px;
  margin:9px 10px;
}
#projects {
  background-color:#115A9E;
  color:#fff;
  padding:20px;
}
.projects-block {
  border:1px solid #fff;
  font-size:0.928571em;
  padding:10px 5px 10px 40px !important;
  position:relative;
  margin-top:15px;
}
#saveguarding-article {
  display:inline-block;
  max-width:380px;
  position:relative;
  margin:0 20px;
  vertical-align:top;
}
#saveguarding-image {
  display:inline-block;
  width:260px;
  height:170px;
  vertical-align:top;
}
#saveguarding-links {
  display:inline-block;
  vertical-align:bottom;
  max-width:295px;
}
.select-styled {
  overflow:hidden;
  position:relative;
}
.select-styled.inline { display:inline-block; }
.select-styled .element {
  display:inline-block;
  border:1px solid #dadada;
  padding:8px 30px 8px 10px;
  background-color:#fff;
  margin:0;
}
.select-styled .element.hide-first  option:first-child { display:none; }
.select-styled .drop {
  display:inline-block;
  height:35px;
  width:32px;
  position:absolute;
  right:1px;
  top:1px;
  pointer-events:none;
}
.select-styled .drop.blue { background:#3087d1; }
.select-styled .drop.gray { background:#212121; }
.select-styled .drop .down {
  display:block;
  position:absolute;
  margin:-4px 0 0 -4px;
  top:50%;
  left:50%;
  width:8px;
  height:8px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -189px -221px;
}
.select-styled .big {
  font-size:2em;
  color:#414042;
  width:100%;
  height:80px;
}
.select-styled .large {
  width:100%;
  height:50%;
  margin-bottom:8px;
}
.send {
  display:block;
  width:24px;
  height:20px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -197px -40px;
  margin:9px 10px;
}
.share {
  display:block;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -197px 0px;
  width:24px;
  height:20px;
  margin:9px 10px;
}
#sitemap-secondary {
  padding:15px 20px 0;
  text-align:center;
  color:#fff;
}
#sitemap-secondary .link { color:#fff; }
.size {
  display:table-cell;
  border-right:1px solid #c7c7c7;
  width:auto;
  text-align:right;
}
.sizedown {
  display:inline-block;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -197px -80px;
  width:24px;
  height:20px;
  margin:9px 10px;
}
.sizedown.disabled {
  opacity:0.5;
  cursor:default;
}
.sizeup {
  display:inline-block;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -197px -60px;
  width:24px;
  height:20px;
  margin:9px 10px;
}
.sizeup.disabled {
  opacity:0.5;
  cursor:default;
}
.skip {
  background:#fff;
  color:#115A9E;
  display:inline-block;
  padding:5px;
  position:absolute;
  left:-999px;
  white-space:nowrap;
}
.skip:focus {
  left:0;
  outline:thin dotted;
}
.tab {
  background:#b6b6b7;
  color:#58595b;
  text-align:center;
  display:table-cell;
  padding:10px;
  font-size:.9em;
  margin:0;
  line-height:1;
  border-right:1px solid #FFF;
}
.tab.selected {
  border-top:3px solid #0872cb;
  border-right:none;
  background:#fff;
  cursor:default;
  color:#0872cb;
}
.tab-content { margin:0 0 35px 0; }
.table {
  font-size:14px;
  line-height:1.2;
  width:99%;
  border-collapse:collapse;
  border-spacing:0px;
}
.table .thead {
  background:none repeat scroll 0% 0% #58595b;
  color:#FFF;
  padding:15px;
}
.table .thead  + .thead { border-left:1px solid #FFF; }
.table .row:nth-child(even) { background:none repeat scroll 0% 0% #D4D4D4; }
.table .row:nth-child(odd) { background:none repeat scroll 0% 0% #EBEBEB; }
.table .cell { padding:15px; }
.table .cell  + .cell { border-left:1px solid #BEBEC0; }
.tableOfContent {
  border:1px solid #212121;
  padding:10px 20px;
  margin:0 0 10px;
  list-style:none;
}
.tableOfContent-1 { margin-left:30px; }
.tableOfContent-1:hover { list-style:circle outside none; }
.tableOfContent-0 { list-style:square outside none; }
.tableOfContent-0 .link { color:#212121; }
#title {
  display:inline-block;
  vertical-align:top;
  height:85px;
  width:136px;
  margin:32px 45px 0;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat;
  overflow:hidden;
  color:transparent;
}
#title[lang=en] { background-position:0 -193px; }
#title[lang=fr] { background-position:0 -278px; }
#title[lang=es] { background-position:0 -363px; }
#title[lang=ar] { background-position:0 -448px; }
.title-thin {
  font-size:0.92em;
  font-weight:normal;
  color:#212121;
  margin:0;
}
.title-normal {
  line-height:1.1em;
  font-size:1em;
  font-weight:bold;
  color:#212121;
  margin:0;
}
.menu-title {
  margin:0;
  color:#fff;
}
.menu-title .link { color:#fff; }
#title-page {
  font-size:2.85em;
  font-weight:bold;
  line-height:1.1em;
  color:#212121;
  margin:0;
}
#UNESCO-portal-link {
  position:fixed;
  top:0;
  left:0;
  width:100%;
  height:27px;
  padding:4px;
  background-color:#115A9E;
  z-index:100;
  text-align:center;
  color:#fff;
  font-size:.9em;
}
.url {
  display:block;
  width:24px;
  height:20px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -197px -20px;
  margin:9px 10px;
}
.video-infos {
  display:inline-block;
  vertical-align:top;
  width:160px;
  margin:0 10px;
}
.video-thumb {
  display:inline-block;
  position:relative;
  width:130px;
  height:80px;
  margin-bottom:35px;
  object-fit:cover;
}
.video-thumb > .play {
  position:absolute;
  right:10px;
  bottom:10px;
}
.homeVideos .video-infos { width:300px; }
.homeVideos .video-thumb {
  width:426px;
  height:240px;
}
.abs {
  position:relative;
  left:-25px;
  float:left;
  top:5px;
}
.colspan {
  column-span:all;
  -webkit-column-span:all;
  -moz-column-span:all;
  -ms-column-span:all;
  -o-column-span:all;
}
.device { background:no-repeat 10px 10px; }
.gray2 { color:#212121; }
.image { width:100%; }
.module-header {
  background:none;
  color:#212121;
  font-weight:600;
  margin-bottom:19px;
  line-height:15px;
  position:relative;
}
.module-header h2 { margin:0; }
.module-header .title {
  font-size:18px;
  font-weight:600;
}
.module-content {
  padding:20px;
  margin-bottom:40px;
  font-size:0.93em;
}
.module > .module-content { padding:0; }
.module-article {
  border-top:1px solid #CECECE;
  padding-top:10px;
}
.module-article:first-child {
  border-top:none;
  padding-top:0;
}
#news-last-thumb .grid-1 { width:180px; }
#news-last-thumb .grid-1:nth-child(2) {
  width:auto;
  text-align:center;
}
.text-normal {
  line-height:1.1em;
  color:#212121;
  line-height:1.4em;
}
.text-small { font-size:0.93em; }
ol, ul { list-style:none; }
#timeline-history {
  width:300px;
  height:815px;
  overflow:hidden;
  position:relative;
  background:url('/design-img/timeline_history/dot.gif') left 45px repeat-x;
}
.ui-progressbar {
  height:2em;
  text-align:left;
  overflow:hidden;
}
.ui-progressbar .ui-progressbar-value {
  margin:-1px;
  height:100%;
}
.sociales {
  text-align:center;
  margin-bottom:20px;
}
#dates-history {
  width:600px;
  height:60px;
  overflow:hidden;
}
#dates-history li {
  list-style:none;
  float:left;
  width:100px;
  height:50px;
  font-size:16px;
  text-align:center;
  background:url('/design-img/timeline_history/biggerdot.png') center bottom no-repeat;
}
#dates-history a {
  line-height:38px;
  padding-bottom:10px;
  color:#3F95C3;
}
#dates-history .selected-history { font-size:24px; }
#issues {
  width:600px;
  height:815px;
  overflow:hidden;
}
#issues li {
  width:300px;
  height:auto;
  list-style:none;
  float:left;
}
#issues li .timeline-content { padding-right:50px; }
#issues li img {
  scale:1.2;
  width:300px;
  float:left;
  margin:0px 30px 15px 0px;
  background:transparent;
  -ms-filter:"progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF,endColorstr=#00FFFFFF)";
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF,endColorstr=#00FFFFFF);
  zoom:1;
  -webkit-transition:all 2s ease-in-out;
  -moz-transition:all 2s ease-in-out;
  -o-transition:all 2s ease-in-out;
  -ms-transition:all 2s ease-in-out;
  transition:all 2s ease-in-out;
}
#issues li h1 {
  color:#fc0;
  font-size:32px;
  margin:20px 0;
}
#issues li p {
  font-weight:normal;
  line-height:18px;
}
#grad_left, #grad_right {
  width:100px;
  height:200px;
  position:absolute;
  top:0;
}
#grad_left {
  left:0;
  background:url('/design-img/timeline_history/grad_left.png') repeat-y;
}
#grad_right {
  right:0;
  background:url('/design-img/timeline_history/grad_right.png') repeat-y;
}
#next, #prev {
  position:absolute;
  top:0;
  font-size:70px;
  top:120px;
  width:24px;
  height:24px;
  background-repeat:no-repeat;
  text-indent:-9999px;
  overflow:hidden;
}
#next, #next_activity {
  right:0;
  background:url("/design-img/timeline_history/timeline.png?v4.3") no-repeat -184px 0;
  margin-right:4px;
}
#prev, #prev_activity {
  left:0;
  background:url("/design-img/timeline_history/timeline.png?v4.3") no-repeat -160px 0;
  margin-left:4px;
}
#timeline-history p.wiki-text { width:auto; }
#timeline-history h2 {
  margin:0 0 0.4em 0;
  width:auto;
  line-height:1.1;
}
.galleria {
  list-style:none;
  width:200px;
}
.galleria li {
  display:block;
  width:80px;
  height:80px;
  overflow:hidden;
  float:left;
  margin:0 10px 10px 0;
}
.galleria li a { display:none; }
.galleria li div {
  position:absolute;
  display:none;
  top:0;
  left:180px;
}
.galleria li div img { cursor:pointer; }
.galleria li.active div img, .galleria li.active div { display:block; }
.galleria li img.thumb {
  cursor:pointer;
  top:auto;
  left:auto;
  display:block;
  width:auto;
  height:auto;
}
.galleria li .caption {
  display:block;
  padding-top:.5em;
}
* html .galleria li div span { width:400px; }
.infoBox {
  background:url("/css/icon/map/tipbox.gif") no-repeat scroll 7px 0 transparent;
  height:200px;
  opacity:1;
  width:360px;
}
.infoBoxmap-celeb-home {
  background:url("/css/icon/map/tipbox.gif") no-repeat scroll 7px 0 transparent;
  opacity:1;
  width:270px;
}
.box-desc {
  border:1px solid black;
  height:200px;
  overflow:auto;
  margin-top:8px;
  background:white;
  padding:5px;
}
.box-descmap-celeb-home {
  border:1px solid black;
  margin-top:8px;
  background:white;
  padding:5px;
}
.tab-exam {
  margin:15px 0 10px;
  padding:0;
  border-bottom:1px solid #005CA1;
}
.tabItem, .tabItemFocus {
  display:inline-block;
  position:relative;
  vertical-align:bottom;
  margin:0 5px 0 0;
  padding:0;
  border:none;
  white-space:normal;
}
.tab-label-current {
  display:inline-block;
  border-top:solid 1px #0872cb;
  border-left:solid 1px #0872cb;
  border-right:solid 1px #0872cb;
  border-bottom:solid 1px white;
  background-color:white;
  text-transform:none;
  color:#0872cb;
  cursor:default;
}
.tab-label {
  display:inline-block;
  width:auto !important;
  background-color:#82add0;
  margin:0 0 0 5px !important;
  padding:5px 16px 5px 11px !important;
  color:#FFF;
  text-transform:none;
  text-align:center;
  cursor:pointer;
}
span.emblem-calendar, .infos_activity span {
  display:block;
  margin:5px 0 0;
  padding:0 0 0 25px;
  position:relative;
}
span.emblem-calendar:before, .infos_activity span:before, .infos_activity span > ieb {
  background:url('../design-img/sprites/sprite_global.png') -99px 0 no-repeat;
  height:17px;
  width:17px;
  content:'';
  display:inline-block;
  position:absolute;
  top:0;
  right:auto;
  bottom:auto;
  left:0;
}
section.emblem-report-table table.wiki-table th:nth-of-type(1) { width:30%; }
section.emblem-report-table table.wiki-table th:nth-of-type(3) { width:25%; }
.marg-top-low { margin-top:0.38em; }
.marg-left-1 span, a.marg-left-1 {
  display:inline-block;
  margin-left:1em;
}
span.countries.emblem-calendar:before, .infos_activity .countries:before, .infos_activity .countries > ieb {
  background-position:-116px 0;
  height:18px;
  width:14px;
  left:2px;
}
.lst_dtl_toggle_all:after, .lst_dtl_toggle_all > iea, .lst_dtl_item .btn_toggle:after, .lst_dtl_item .btn_toggle > iea {
  background:url('../design-img/sprites/sprite_global.png') -76px -201px no-repeat;
  height:24px;
  width:18px;
  content:'';
  display:inline-block;
  background-color:#58595b;
  position:absolute;
  top:0;
  right:0;
  bottom:auto;
  left:auto;
}
.js .lst_dtl_item .btn_toggle { display:block; }
.no-link {
  text-decoration:none;
  color:#333;
  outline:none;
}
.no-link:hover { text-decoration:none; }
.rating {
  display:inline-block;
  font-size:16px;
  line-height:16px;
}
header.header {
  position:fixed;
  z-index:90;
  width:100%;
}
@media (min-width: 992px) {
  header.header { position:absolute; }
}
@media (max-width: 992px) {
  header.header { background:none; }
}
header.header:hover {
  background:#0077D4;
  transition:0.5s;
}
@media (max-width: 992px) {
  header.header:hover { background:none; }
}
@media (max-width: 992px) {
  header.header.is-scroll, header.header-page.is-scroll { background:#0077D4; }
}
header.header.is-border .wrapper_header, header.header-page.is-border .wrapper_header {
  border-bottom:0.0625rem solid #fff;
  padding:2rem 15px;
  border-radius:0px;
}
@media (max-width: 992px) {
  header.header.is-border .wrapper_header, header.header-page.is-border .wrapper_header { height:93px; }
}
header.header-page {
  padding-bottom:7.8rem;
  background:#0077D4;
}
header.header-page.header-bg {
  padding-bottom:28rem;
  background-size:cover;
}
@media screen and (max-width: 992px) {
  header.header-page.header-bg { padding-bottom:2rem; }
}
#wrapper_navbar:hover {
  background:#0077D4;
  transition:0.5s;
}
.wrapper_header {
  margin:0 auto;
  position:initial;
}
.wrapper_header .col-xs-6, .wrapper_header .col-sm-6, .wrapper_header .col-md-8 { position:initial; }
.wrapper_header#header-mobile-remplacement .navbar-toggle { right:17px; }
.wrapper_header .navbar-toggle {
  background:#115A9E;
  position:absolute;
  border-radius:50%;
  margin-right:0;
  padding:0px 11px;
  right:15px;
  width:40px;
  height:40px;
  z-index:90;
  cursor:pointer;
}
.wrapper_header .navbar-toggle.collapsed { padding:0px 8px; }
.wrapper_header .navbar-toggle .icon-bar { background-color:#fff; }
.wrapper_header .navbar_logo a { display:block; }
.wrapper_header .navbar_logo a.navbar_baseline {
  display:block;
  margin-top:.5rem;
  font-size:1.3rem;
  color:#fff;
  font-weight:800;
}
@media (min-width: 992px) {
  .wrapper_header .navbar_logo a.navbar_baseline { font-size:1.75rem; }
}
.wrapper_header .navbar_logo a img { width:180px; }
@media (min-width: 992px) {
  .wrapper_header .navbar_logo a img { width:250px; }
}
.wrapper_header .header_connect, .wrapper_header #login { position:relative; }
.wrapper_header .header_connect .bk-connexion, .wrapper_header #login .bk-connexion {
  position:absolute;
  background:#fff;
  border:1px solid #D5DADD;
  border-radius:8px;
  z-index:21;
  margin-left:-6px;
  padding:15px;
  right:0;
  top:30px;
  min-height:100px;
}
@media screen and (max-width: 992px) {
  .wrapper_header .header_connect .bk-connexion, .wrapper_header #login .bk-connexion { right:-15px; }
}
.wrapper_header .header_connect .bk-connexion label, .wrapper_header #login .bk-connexion label {
  display:inline-block;
  color:#212121;
  font-size:0.9em;
  width:90px;
}
.wrapper_header .header_connect .bk-connexion button, .wrapper_header #login .bk-connexion button {
  background-color:#004d79;
  margin:0 10px 10px;
  min-width:0;
}
.wrapper_header .header_connect .create_account, .wrapper_header #login .create_account {
  color:white;
  display:block;
  margin:0 0 2px 0;
  text-align:right;
  clear:both;
  font-size:12px;
}
.wrapper_header .header_connect .sso_account, .wrapper_header #login .sso_account { margin:0 0 5px 0; }
.wrapper_header .header_actions {
  position:relative;
  z-index:2;
}
.wrapper_header .header_actions .header_langue {
  display:inline-block;
  margin:0;
  padding:0;
  text-align:right;
}
@media screen and (max-width: 992px) {
  .wrapper_header .header_actions .header_langue { text-align:center; }
}
@media screen and (max-width: 768px) {
  .wrapper_header .header_actions .header_langue {
    text-align:right;
    padding-top:30px;
  }
}
@media screen and (max-width: 768px) {
  .wrapper_header .header_actions .header_langue #languages {
    margin:0 auto;
    text-align:center;
  }
}
.wrapper_header .header_actions .header_langue .language {
  display:inline-block;
  height:32px;
  width:32px;
  margin:8px 5px 0 0;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat;
  overflow:hidden;
  text-indent:32px;
  vertical-align:bottom;
}
.wrapper_header .header_actions .header_langue .language:focus { outline:1px dotted #fff; }
.wrapper_header .header_actions .header_langue .language[lang=en] { background-position:-136px -149px; }
.wrapper_header .header_actions .header_langue .language[lang=fr] { background-position:-136px -245px; }
.wrapper_header .header_actions .header_langue .language[lang=es] { background-position:-136px -341px; }
.wrapper_header .header_actions .header_langue .language[lang=ar] { background-position:-136px -437px; }
.wrapper_header .header_actions .header_langue .language[lang=en].is-disable {
  background-position:-136px -181px;
  cursor:not-allowed;
}
.wrapper_header .header_actions .header_langue .language[lang=fr].is-disable {
  background-position:-136px -277px;
  cursor:not-allowed;
}
.wrapper_header .header_actions .header_langue .language[lang=es].is-disable {
  background-position:-136px -373px;
  cursor:not-allowed;
}
.wrapper_header .header_actions .header_langue .language[lang=ar].is-disable {
  background-position:-136px -469px;
  cursor:not-allowed;
}
.wrapper_header .header_actions .header_langue .language[lang=en].is-select, .wrapper_header .header_actions .header_langue .language[lang=en]:active { background-position:-136px -213px; }
.wrapper_header .header_actions .header_langue .language[lang=fr].is-select, .wrapper_header .header_actions .header_langue .language[lang=fr]:active { background-position:-136px -309px; }
.wrapper_header .header_actions .header_langue .language[lang=es].is-select, .wrapper_header .header_actions .header_langue .language[lang=es]:active { background-position:-136px -405px; }
.wrapper_header .header_actions .header_langue .language[lang=ar].is_select, .wrapper_header .header_actions .header_langue .language[lang=ar]:active { background-position:-136px -501px; }
.wrapper_header .header_actions .header_search {
  display:inline-block;
  position:relative;
  margin:20px 0 0 0;
  width:100%;
}
@media screen and (max-width: 992px) {
  .wrapper_header .header_actions .header_search { margin:20px 0; }
}
@media screen and (max-width: 768px) {
  .wrapper_header .header_actions .header_search { margin:12px 0 0; }
}
.wrapper_header .header_actions .header_search .sp-search {
  background:transparent;
  display:block;
  margin:0;
  padding:0 31px 0 9px;
  width:100%;
  border:1px solid #fff;
  font-size:12px;
  color:#fff;
  height:35px;
  line-height:35px;
}
.wrapper_header .header_actions .header_search .sp-search::placeholder { color:#dadada; }
.wrapper_header .header_actions .header_search .search-button {
  width:17px;
  height:15px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -168px -266px;
  border:none;
  position:absolute;
  bottom:11px;
  right:21px;
}
#login-menu.login-menu {
  background:#FFF;
  border:1px solid #D5DADD;
  border-radius:8px;
  display:none;
  margin-left:-15px;
  min-width:232px;
  padding:15px;
  position:absolute;
  right:0;
  text-align:center;
  top:30px !important;
  width:100%;
  z-index:100;
}
#login-menu.login-menu ul { padding:0; }
#login-menu.login-menu ul > .item {
  list-style:none;
  text-align:left;
  float:none !important;
  margin-right:0;
}
#login-menu.login-menu ul > .item:last-child .link { border:none; }
#login-menu.login-menu ul > .item > .link {
  border-bottom:1px solid #D5DADD;
  color:#7F888F;
  display:inline-block;
  font-weight:normal;
  padding:5px 10px;
  transition:0.5s;
  width:100%;
  cursor:pointer;
}
#login-menu.login-menu ul > .item > .link:hover {
  color:#212121;
  transition:0.5s;
}
.login-link, .header_connect-link {
  display:inline-block;
  vertical-align:top;
  font-size:1.2rem;
  line-height:15px;
  color:#fff;
  cursor:pointer;
}
@media screen and (max-width: 768px) {
  .login-link, .header_connect-link { font-size:1.2em; }
}
.login-link > .drop, .header_connect-link > .drop {
  display:inline-block;
  vertical-align:bottom;
  width:26px;
  height:22px;
  top:1px;
  left:-4px;
  -moz-transform:scale(0.35);
  -webkit-transform:scale(0.35);
  -o-transform:scale(0.35);
  transform:scale(0.35);
  position:relative;
  background-image:url('../design-img/icons/bottom-arrow.png');
}
.wrapper_navigation {
  height:55px;
  font-size:14px;
  position:inherit !important;
}
@media screen and (max-width: 992px) {
  .wrapper_navigation {
    height:85vh;
    font-size:12px;
    position:absolute !important;
    top:92px;
  }
}
.wrapper_navigation .navbar {
  padding:0;
  margin:0 auto;
  position:initial;
  border:none;
}
.wrapper_navigation .navbar .container-fluid {
  margin:0;
  padding:0;
}
.wrapper_navigation .navbar .container-fluid .col-sm-9, .wrapper_navigation .navbar .container-fluid .col-xs-12 {
  margin:0;
  position:inherit;
}
.wrapper_navigation .navbar .container-fluid .navbar-toggle {
  float:left;
  margin-left:15px;
}
.wrapper_navigation .navbar .container-fluid .navbar-header {
  margin:0;
  padding:0;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect {
  display:none;
  margin:0 20px 0 0;
}
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect {
    display:block;
    float:right;
  }
  .wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect .nav_connect-link {
    display:inline-block;
    width:24px;
    height:24px;
    color:#fff;
  }
  .wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect .nav_connect-link:before {
    display:inline-block;
    content:"";
    vertical-align:bottom;
    width:53px;
    height:50px;
    margin:0 5px;
    zoom:0.4;
    background-image:url('../design-img/content/user.png');
  }
}
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_search {
  display:none;
  margin:0 20px 0 0;
}
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .container-fluid .navbar-header .nav_search {
    display:block;
    float:right;
  }
  .wrapper_navigation .navbar .container-fluid .navbar-header .nav_search .nav_search-link {
    display:inline-block;
    width:20px;
    height:20px;
    color:#fff;
  }
  .wrapper_navigation .navbar .container-fluid .navbar-header .nav_search .nav_search-link:before {
    display:inline-block;
    content:"";
    vertical-align:bottom;
    width:17px;
    height:15px;
    background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat -168px -266px;
  }
}
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect-subblock {
  clear:both;
  padding:1px;
  min-height:1px;
  display:none;
  width:100%;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect-subblock button {
  min-width:auto;
  padding:0 20px;
  font-weight:bold;
  background:#004d79;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect-subblock label {
  font-size:100%;
  line-height:2;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_connect-subblock .forgot_pwd { margin:0 10px 10px; }
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_search-subblock {
  clear:both;
  padding:1px;
  min-height:1px;
  display:none;
  width:100%;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .nav_search-subblock button {
  min-width:auto;
  padding:0 20px;
  font-weight:bold;
  background:#004d79;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .dropdown-menu-user {
  z-index:0;
  margin-left:0;
  top:1px;
  width:auto;
  text-align:center;
  display:none;
  position:absolute;
  background:#fff;
  clear:both;
  right:0;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .dropdown-menu-user ul {
  margin:0;
  padding:0;
}
.wrapper_navigation .navbar .container-fluid .navbar-header .dropdown-menu-user .link {
  padding:10px;
  font-size:14px;
}
.wrapper_navigation .navbar .navbar-collapse {
  padding:0;
  margin:0;
  border:none;
}
@media screen and (max-width: 992px) {
  .wrapper_navigation .navbar .navbar-collapse.collapse.in { height:100vh; }
}
.wrapper_navigation .navbar .navbar-collapse .navbar-nav {
  width:100%;
  margin:0;
  text-align:center;
}
@media screen and (max-width: 992px) {
  .wrapper_navigation .navbar .navbar-collapse .navbar-nav {
    background:#0077D4;
    height:86vh;
    left:-1px;
    overflow:auto;
    z-index:99;
    padding:2rem 15px;
    position:absolute;
    display:flex;
    flex-direction:column;
  }
}
.wrapper_navigation .navbar .navbar-collapse .navbar-nav > li { float:right; }
@media screen and (max-width: 992px) {
  .wrapper_navigation .navbar .navbar-collapse .navbar-nav li:first-child { order:2; }
}
.wrapper_navigation .navbar .navbar-collapse .navbar-nav ul { padding:0; }
@media (min-width: 768px) {
  .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-md-1, .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-sm-1, .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-lg-1 {
    width:100%;
    *width:100%;
  }
}
@media (min-width: 992px) {
  .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-md-1, .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-sm-1, .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-lg-1 {
    width:14.285714285714285714285714285714%;
    *width:14.285714285714285714285714285714%;
  }
}
@media (min-width: 1200px) {
  .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-md-1, .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-sm-1, .wrapper_navigation .navbar .navbar-collapse .navbar-nav .seven-cols .col-lg-1 {
    width:14.285714285714285714285714285714%;
    *width:14.285714285714285714285714285714%;
  }
}
.wrapper_navigation .navbar .navbar-collapse.collapse { display:none; }
.wrapper_navigation .navbar .navbar-collapse.collapse.in {
  display:block;
  overflow-y:inherit;
}
.wrapper_navigation .navbar .dl-menu li { margin-right:20px; }
.wrapper_navigation .navbar .dl-menu li:last-child { margin-right:0; }
@media screen and (max-width: 992px) {
  .wrapper_navigation .navbar .dl-menu li {
    text-align:left;
    width:100%;
  }
}
.wrapper_navigation .navbar .nav .open > a, .wrapper_navigation .navbar .nav .open > a:hover, .wrapper_navigation .navbar .nav .open > a:focus { background:none; }
.wrapper_navigation .navbar li {
  padding:0;
  margin:0;
  position:initial;
  float:left;
}
.wrapper_navigation .navbar li.icon-header {
  margin-right:3px;
  width:24px;
}
.wrapper_navigation .navbar li.icon-header .menu-label { background:none; }
.wrapper_navigation .navbar li.second-menu-mobile .menu-label:not(.icon-header) { font-size:12px; }
@media screen and (max-width: 992px) {
  .wrapper_navigation .navbar li.second-menu-mobile .menu-label:not(.icon-header) { font-size:2rem; }
}
.wrapper_navigation .navbar li.second-menu-mobile .menu-label:not(.icon-header):before { display:none; }
.wrapper_navigation .navbar li.second-menu-mobile .anniversary img, .wrapper_navigation .navbar li.second-menu-mobile .explore-unesco img {
  height:auto;
  margin-left:8px;
  margin-top:-3px;
}
.wrapper_navigation .navbar li.second-menu-mobile .anniversary {
  color:#eecf93;
  font-size:12px;
  vertical-align:top;
}
.wrapper_navigation .navbar li.second-menu-mobile .anniversary img { width:10px; }
.wrapper_navigation .navbar li.second-menu-mobile .explore-unesco {
  color:#fff;
  font-size:12px;
  vertical-align:top;
}
.wrapper_navigation .navbar li.second-menu-mobile .explore-unesco img { width:14px; }
.wrapper_navigation .navbar li .icon-header {
  font-size:16px;
  margin:0;
  color:#fff;
  display:block;
  padding:0 20px 10px 0;
}
.wrapper_navigation .navbar li .menu-label, .wrapper_navigation .navbar li .active-menu:not(.icon-header) {
  font-size:16px;
  margin:0;
  color:#fff;
  width:100%;
  display:block;
  position:relative;
  padding:0 20px 10px 0;
}
.wrapper_navigation .navbar li .menu-label patch.arrow-down, .wrapper_navigation .navbar li .active-menu:not(.icon-header) patch.arrow-down { fill:#fff; }
.wrapper_navigation .navbar li .menu-label:hover:before, .wrapper_navigation .navbar li .active-menu:not(.icon-header):hover:before {
  left:0;
  width:100%;
}
.wrapper_navigation .navbar li .menu-label.is-expanded:hover:before, .wrapper_navigation .navbar li .active-menu:not(.icon-header).is-expanded:hover:before { width:calc(98.5%); }
.wrapper_navigation .navbar li .menu-label:before, .wrapper_navigation .navbar li .active-menu:not(.icon-header):before {
  content:'';
  position:absolute;
  bottom:.5rem;
  right:0;
  width:0;
  height:1px;
  background-color:#fff;
  transition:.3s ease-in-out;
}
.wrapper_navigation .navbar li .menu-label:hover, .wrapper_navigation .navbar li .menu-label:focus, .wrapper_navigation .navbar li .active-menu:not(.icon-header):hover, .wrapper_navigation .navbar li .active-menu:not(.icon-header):focus { text-decoration:none; }
.wrapper_navigation .navbar li .menu-label:hover.arrow-menu:after, .wrapper_navigation .navbar li .active-menu:not(.icon-header):hover.arrow-menu:after {
  transform:rotate(180deg);
  transition:0.5s;
}
.wrapper_navigation .navbar li .menu-label:hover .active-menu:after, .wrapper_navigation .navbar li .active-menu:not(.icon-header):hover .active-menu:after {
  transform:rotate(180deg);
  transition:0.5s;
}
@media screen and (max-width: 992px) {
  .wrapper_navigation .navbar li .menu-label, .wrapper_navigation .navbar li .active-menu:not(.icon-header) {
    font-size:2.4rem;
    font-weight:400;
    padding:0 0 2.4rem 0;
  }
}
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar li .menu-label, .wrapper_navigation .navbar li .active-menu:not(.icon-header) { text-align:left; }
  .wrapper_navigation .navbar li .menu-label.bg-color-blue_2, .wrapper_navigation .navbar li .active-menu:not(.icon-header).bg-color-blue_2 { background-color:#004d79; }
}
.wrapper_navigation .navbar li .active-menu:not(.icon-header):before {
  left:0;
  width:100%;
}
.wrapper_navigation .navbar li .arrow-menu:after, .wrapper_navigation .navbar li .active-menu:not(.icon-header):after {
  margin-left:.5rem;
  content:"expand_more";
  font-family:'Material Icons Sharp';
  font-feature-settings:'liga' 1;
  font-weight:400;
  position:absolute;
  transition:0.5s;
}
.wrapper_navigation .navbar li .active-menu:not(.icon-header):after {
  transform:rotate(180deg);
  transition:0.5s;
}
.wrapper_navigation .navbar li ul.menu-sub li { float:none; }
.wrapper_navigation .navbar .nav_country a {
  color:#eaf4fc;
  text-decoration:none;
}
.wrapper_navigation .navbar .nav_country a:before { display:none; }
.wrapper_navigation .navbar .nav_country .sub-menu-countries {
  background-color:#F1F4F6;
  padding:0;
  text-transform:capitalize;
  line-height:normal;
  text-align:left;
  padding:20px;
  width:100%;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries .block_country-select {
  border-top:1px solid #fff;
  margin:20px 0 0 0;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries label {
  display:block;
  margin-top:10px;
  text-transform:uppercase;
  font-size:11px;
  font-weight:normal;
  padding-top:5px;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries .country-search::after {
  content:'';
  display:block;
  width:17px;
  height:15px;
  background:url(../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png) no-repeat -168px -266px;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries select, .wrapper_navigation .navbar .nav_country .sub-menu-countries .fixedWidth, .wrapper_navigation .navbar .nav_country .sub-menu-countries input {
  margin:1.6rem 0;
  width:100%;
  font-weight:normal;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries .select .arrow {
  height:25px;
  width:25px;
  background-position:-150px -45px;
}
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .nav_country .sub-menu-countries { width:100%; }
  .wrapper_navigation .navbar .nav_country .sub-menu-countries.menu {
    left:0;
    padding:0 20px 20px;
  }
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries .sub-menu-region {
  padding:20px;
  border-bottom:1px solid #1c71a1;
  width:30%;
  font-size:1em;
  font-weight:bold;
  color:#e7f1fa;
  float:right;
  clear:both;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries .sub-menu-list {
  background-color:#06a1b5;
  padding:0;
  position:absolute;
  top:0;
  left:0;
  display:none;
  width:70%;
  padding:20px;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries .sub-menu-list .child {
  display:inline-block;
  padding:7px;
}
.wrapper_navigation .navbar .nav_country .sub-menu-countries .sub-menu-list .child .country {
  font-size:0.93em;
  color:#e7f1fa;
}
.wrapper_navigation .navbar .sub-menu-countries hr { margin:1.5rem 0; }
.wrapper_navigation .navbar .sub-menu-countries .block_country-select .select { margin-top:15px; }
.wrapper_navigation .navbar .nav_country-subblock {
  clear:both;
  padding:1px;
  min-height:1px;
  display:none;
  width:100%;
  padding:10px 20px;
  z-index:1;
  position:relative;
  background-color:#06a1b5;
}
.wrapper_navigation .navbar .nav_country-subblock input {
  margin:O auto;
  width:100%;
  height:30px;
  line-height:30px;
}
.wrapper_navigation .navbar .menu {
  display:table;
  visibility:hidden;
  position:absolute;
  left:0;
  width:100%;
  z-index:99;
  background-color:#0077D4;
  padding:30px 20px 50px 20px;
  text-align:left;
  top:125px;
}
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .menu.menu-col-0 { padding-bottom:30px !important; }
  .wrapper_navigation .navbar .menu.menu-col-0 .menu-row {
    height:auto;
    overflow:hidden;
  }
  .wrapper_navigation .navbar .menu.menu-col-0 .menu-row .first, .wrapper_navigation .navbar .menu.menu-col-0 .menu-row .dropdown-toggle { display:none; }
  .wrapper_navigation .navbar .menu.menu-col-0 .menu-row .dropdown-menu { display:block !important; }
}
.wrapper_navigation .navbar .menu.menu-col-0 .menu-row { width:100%; }
.wrapper_navigation .navbar .menu.menu-col-0 .menu-row .menu-sub {
  width:100%;
  margin-left:-5px;
}
.wrapper_navigation .navbar .menu.menu-col-0 .menu-row .menu-sub li {
  width:113px;
  display:inline-block;
  margin-left:3px;
  overflow:hidden;
  height:113px;
  margin-bottom:3px;
}
.wrapper_navigation .navbar .menu.menu-col-0 .menu-row .menu-sub li .item_menu-news img { height:160px !important; }
.wrapper_navigation .navbar .menu.menu-col-0 .menu-row .menu-sub li .link-all-news {
  position:absolute;
  bottom:0;
  right:10px;
}
@media screen and (max-width: 992px) {
  .wrapper_navigation .navbar .menu.menu-col-0 .menu-row .menu-sub li {
    height:auto;
    width:auto;
  }
  .wrapper_navigation .navbar .menu.menu-col-0 .menu-row .menu-sub li .item_menu-news { display:none; }
  .wrapper_navigation .navbar .menu.menu-col-0 .menu-row .menu-sub li .link-all-news { left:0px; }
}
.wrapper_navigation .navbar .menu.menu-col-1 .menu-row { width:85%; }
.wrapper_navigation .navbar .menu.menu-col-2 .menu-row { width:40%; }
.wrapper_navigation .navbar .menu.menu-col-3 .menu-row { width:30%; }
.wrapper_navigation .navbar .menu.menu-col-4 .menu-row { width:21%; }
.wrapper_navigation .navbar .menu.menu-col-5 .menu-row { width:16%; }
.wrapper_navigation .navbar .menu.menu-col-6 .menu-row { width:14%; }
.wrapper_navigation .navbar .menu .no-bullet, .wrapper_navigation .navbar .menu .menu-row {
  display:inline-block;
  padding:0 10px 0 10px;
  position:relative;
  vertical-align:top;
}
.wrapper_navigation .navbar .menu .no-bullet > .first, .wrapper_navigation .navbar .menu .menu-row > .first {
  padding:0 0 5px;
  margin:20px 0 10px 0;
  color:#fff;
  font-size:1em;
  font-weight:bold;
  display:block;
}
.wrapper_navigation .navbar .menu .no-bullet .js-submennu-action, .wrapper_navigation .navbar .menu .menu-row .js-submennu-action {
  text-align:right;
  background:red;
  height:22px;
  width:58px;
  display:block;
  position:absolute;
  z-index:9999;
  right:-20px;
  top:0;
}
.wrapper_navigation .navbar .menu .no-bullet .dropdown-toggle, .wrapper_navigation .navbar .menu .menu-row .dropdown-toggle { display:none; }
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .menu .no-bullet .dropdown-menu, .wrapper_navigation .navbar .menu .menu-row .dropdown-menu { display:none; }
  .wrapper_navigation .navbar .menu .no-bullet .dropdown-toggle, .wrapper_navigation .navbar .menu .menu-row .dropdown-toggle {
    display:block;
    position:absolute;
    top:0;
    right:0;
    width:30px;
    height:28px;
    padding:0px 30px;
  }
  .wrapper_navigation .navbar .menu .no-bullet .dropdown-toggle:before, .wrapper_navigation .navbar .menu .menu-row .dropdown-toggle:before {
    content:'';
    display:inline-block;
    vertical-align:bottom;
    width:27px;
    height:22px;
    top:-12px;
    left:0px;
    zoom:0.35;
    position:relative;
    background-image:url(../design-img/icons/bottom-arrow.png);
  }
}
.wrapper_navigation .navbar .menu .menu-row  + .menu-row { margin:0 10px 0 20px; }
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .menu .menu-row  + .menu-row { margin:0; }
}
.wrapper_navigation .navbar .menu .menu-sub {
  display:inline-block;
  padding:0 10px 0 10px;
  position:relative;
  vertical-align:top;
  margin:0;
  padding:0;
  white-space:normal;
}
.wrapper_navigation .navbar .menu .menu-sub > .first {
  padding:0 0 5px;
  margin:20px 0 10px 0;
  color:#fff;
  font-size:1em;
  font-weight:bold;
  display:block;
}
.wrapper_navigation .navbar .menu .menu-sub .js-submennu-action {
  text-align:right;
  background:red;
  height:22px;
  width:58px;
  display:block;
  position:absolute;
  z-index:9999;
  right:-20px;
  top:0;
}
.wrapper_navigation .navbar .menu .menu-sub .dropdown-toggle { display:none; }
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .menu .menu-sub .dropdown-menu { display:none; }
  .wrapper_navigation .navbar .menu .menu-sub .dropdown-toggle {
    display:block;
    position:absolute;
    top:0;
    right:0;
    width:30px;
    height:28px;
    padding:0px 30px;
  }
  .wrapper_navigation .navbar .menu .menu-sub .dropdown-toggle:before {
    content:'';
    display:inline-block;
    vertical-align:bottom;
    width:27px;
    height:22px;
    top:-12px;
    left:0px;
    zoom:0.35;
    position:relative;
    background-image:url(../design-img/icons/bottom-arrow.png);
  }
}
.wrapper_navigation .navbar .menu .menu-sub .sub {
  display:block;
  font-size:0.93em;
  margin-bottom:7px;
  color:#fff;
}
@media screen and (max-width: 768px) {
  .wrapper_navigation .navbar .menu.visible {
    position:relative;
    padding:0 20px;
  }
  .wrapper_navigation .navbar .menu.visible ul {
    padding:0;
    margin:0;
  }
  .wrapper_navigation .navbar .menu .menu-row {
    display:block;
    width:100% !important;
  }
  .wrapper_navigation .navbar .menu .menu-row .menu-sub.hidden-navs { display:none; }
}
.wrapper_navigation .navbar .menu ul { padding:0; }
.wrapper_navigation .bk-connexion {
  position:absolute;
  background:#115A9E;
  z-index:10;
  margin-left:-6px;
  padding:10px;
  right:0;
  top:30px;
  min-height:100px;
  width:330px;
}
.wrapper_navigation .bk-connexion label {
  display:inline-block;
  width:90px;
  font-size:0.9em;
}
.wrapper_navigation .bk-connexion button {
  background-color:#004d79;
  min-width:0;
}
#form-login { text-align:left; }
#form-login a {
  display:block;
  margin:0 0 10px 10px;
  text-align:left;
  color:#7F888F;
  font-size:12px;
  transition:0.5s;
}
#form-login a:hover, #form-login a:active {
  color:#212121;
  transition:0.5s;
}
.navbar-toggle .icon-bar {
  width:22px;
  transition:all 0.2s;
  -webkit-transition:all 0.2s;
}
.navbar-toggle .top-bar {
  transform:rotate(45deg);
  -webkit-transform:rotate(45deg);
  transform-origin:10% 10%;
  -webkit-transform-origin:10% 10%;
}
.navbar-toggle .middle-bar { opacity:0; }
.navbar-toggle .bottom-bar {
  transform:rotate(-45deg);
  transform-origin:10% 90%;
  -webkit-transform:rotate(-45deg);
  -webkit-transform-origin:10% 90%;
}
.navbar-toggle.collapsed .top-bar {
  transform:rotate(0);
  -webkit-transform:rotate(0);
  margin-left:0px;
}
.navbar-toggle.collapsed .middle-bar { opacity:1; }
.navbar-toggle.collapsed .bottom-bar {
  transform:rotate(0);
  -webkit-transform:rotate(0);
  margin-left:0px;
}
.navbar-default .navbar-toggle {
  position:relative;
  padding-top:10px;
  z-index:100;
}
.page-00001 .block_breadcrumbs { display:none; }
.block_breadcrumbs {
  font-size:0.875em;
  padding:0;
}
.block_breadcrumbs ul {
  margin:0;
  padding:0 0 30px 0;
}
.block_breadcrumbs ul li {
  color:#7F888F;
  float:left;
  font-weight:600;
  margin-right:5px;
  padding-right:25px;
  position:relative;
}
.block_breadcrumbs ul li a {
  color:#212121;
  font-weight:400;
  display:inline-block;
  vertical-align:middle;
}
.block_breadcrumbs ul li:after {
  content:"chevron_right";
  color:#7F888F;
  font-family:'Material Icons Sharp';
  font-feature-settings:'liga' 1;
  font-weight:400;
  position:absolute;
  transition:0.5s;
  font-size:20px;
  right:0;
  top:-4px;
}
.block_breadcrumbs ul li:last-child:after { content:""; }
.block_breadcrumbs a.block_breadcrumbs-link { color:#212121; }
.block_breadcrumbs span.block_breadcrumbs-link { color:#2980b9; }
#header .title-form-header {
  color:#212121;
  font-size:24px;
  font-weight:600;
}
#header h1 {
  font-size:6rem;
  font-weight:800;
  line-height:7.2rem;
}
@media screen and (max-width: 992px) {
  #header h1 {
    font-size:2.2em;
    line-height:4.2rem;
  }
}
#header .block_breadcrumbs { padding:0 15px; }
#header .block_breadcrumbs li { margin-bottom:0; }
#header .block_breadcrumbs li a { color:#B2D6F2; }
#header .block_breadcrumbs li:after { color:#B2D6F2; }
#header .block_breadcrumbs span.block_breadcrumbs-link {
  color:#B2D6F2;
  font-weight:400;
}
#footer .block_breadcrumbs { padding:0 15px; }
#footer .block_breadcrumbs li { margin-bottom:0; }
#footer .block_breadcrumbs li a { color:#0077D4; }
#footer .block_breadcrumbs span.block_breadcrumbs-link {
  color:#7F888F;
  font-weight:400;
}
.modal-backdrop {
  background-color:#101820;
  position:fixed;
  top:0;
  left:0;
  bottom:0;
  right:0;
  z-index:1040;
  background-color:#000;
}
.modal {
  position:fixed;
  width:100%;
  height:100%;
  z-index:9999;
  background:#004d79;
  top:0;
}
.modal .close {
  font-size:2em;
  font-weight:normal;
  position:absolute;
  top:20px;
  right:20px;
  height:46px;
  width:50px;
  border:0;
  background-color:transparent;
  color:#fff;
  border:none;
  opacity:0.9;
  zoom:0.7;
  background-image:url('../design-img/icons/delete-icon.png');
}
.modal .modal-dialog {
  display:block;
  padding:20px;
  min-height:100%;
}
.modal .modal-dialog .modal-content {
  position:relative;
  width:300px;
  margin:10% auto 0;
}
.modal .modal-dialog .modal-content .title {
  font-size:25px;
  text-align:center;
  margin:0 0 30px;
}
.modal .modal-dialog .modal-content input {
  width:100%;
  margin-bottom:20px;
  margin-left:0;
  height:50px;
  line-height:50px;
}
.modal .modal-dialog .modal-content input::placeholder { font-style:italic; }
.modal .modal-dialog .modal-content button { margin-bottom:20px; }
.wrapper_alert {
  margin:0 auto;
  background:#fff6bf;
  padding:0;
}
.wrapper_alert .block_alert {
  padding:5px 20px;
  color:black;
  text-align:center;
}
.menu-theme { background-color:#005780 !important; }
#bloclang, #form-login { visibility:hidden; }
.submenu_card {
  flex-direction:row;
  flex-wrap:wrap;
  display:flex;
  margin:0 -0.75rem -1.5rem;
  width:calc(101.5%);
  margin:0 -0.5rem -1rem;
  padding:0;
}
.submenu_card li.img-menu {
  transition:all .3s ease-in-out;
  margin-bottom:1.5rem;
  margin-right:0px !important;
  position:relative !important;
  padding:0 0.75rem;
  max-width:33%;
  display:flex;
}
@media screen and (min-width: 994px) {
  .submenu_card li.img-menu { max-width:25%; }
}
.submenu_card li.img-menu:hover { transform:scale(1.05); }
.submenu_card li.img-menu a.card {
  position:relative;
  background-size:cover;
  background-position:center center;
  overflow:hidden;
  flex-direction:column;
  min-width:0;
  word-wrap:break-word;
  background-color:#F1F4F6;
  background-clip:border-box;
  border:0 solid rgba(33,33,33,0.125);
  border-radius:0.5rem;
  color:#fff;
  font-weight:600;
  font-size:18px;
  text-align:center;
  z-index:100;
  width:100%;
}
.submenu_card li.img-menu a.card::before {
  content:'';
  position:absolute;
  width:100%;
  height:100%;
  background-color:#212121;
  opacity:.4;
  z-index:2;
  left:0;
}
.submenu_card li.img-menu a.card img { scale:1.4; }
.submenu_card li.img-menu a.card span {
  position:absolute;
  top:50%;
  left:50%;
  transform:translateX(-50%) translateY(-50%);
  z-index:3;
  padding:0 0.5rem;
  font-size:18px;
  font-weight:600;
  color:#fff;
}
#header-mobile-remplacement {
  visibility:hidden;
  background:#0077D4;
}
#mobile-header {
  position:absolute;
  width:100%;
}
@media screen and (max-width: 992px) {
  .menu-open, .menu-open boby { overflow-y:hidden; }
  #divMenu {
    display:none;
    background:#0077D4;
    height:90vh;
    overflow:auto;
    padding:4rem 15px;
    position:absolute;
    right:0;
    z-index:99;
    top:93px;
    width:100%;
  }
  #divMenu a { color:#fff; }
  #divMenu button.btn-primary {
    background-color:#115A9E;
    color:#fff;
  }
  #divMenu .container {
    padding:0;
    width:100%;
  }
  #divMenu .title-form-header {
    color:#fff;
    margin-left:15px;
    margin-bottom:10px;
  }
  #divMenu ul { padding:0; }
  #divMenu ul li a {
    color:#fff;
    display:block;
    font-size:0.93em;
    margin-bottom:7px;
  }
  #divMenu ul li a.first {
    padding:0 0 5px;
    margin:20px 0 10px 0;
    font-size:1em;
    font-weight:bold;
    display:block;
  }
}
.submenu-header { cursor:pointer; }
.submenu-header .submenu-title { vertical-align:top; }
.m-fadeOut {
  visibility:hidden !important;
  opacity:0;
  transition:visibility 0s linear 300ms, opacity 500ms;
}
.m-fadeIn {
  visibility:visible !important;
  opacity:1;
  transition:visibility 0s linear 0ms, opacity 500ms;
}
#wrapper {
  width:calc(-20%);
  height:60px;
  background-color:#dbdbdb;
  padding:0 60px;
  margin-top:60px;
  font-family:helvetica;
  overflow:hidden;
  position:relative;
  z-index:99;
}
.arrowb {
  background-color:#0077D4;
  border:none;
  border-radius:50%;
  cursor:pointer;
  color:#fff;
  display:inline-block;
  font-size:1.625rem;
  position:absolute;
  top:10px;
  padding:0;
  line-height:0;
  outline:none;
  height:4rem;
  min-width:4rem;
  width:2.5rem;
  z-index:98;
}
.arrowb:first-of-type { left:0; }
.arrowb:nth-of-type(2) { right:0; }
.arrowl:before, .arrowr:before {
  color:#fff;
  font-feature-settings:'liga' 1;
  font-weight:400;
  position:absolute;
  transition:0.5s;
  font-size:30px;
  top:50%;
  left:50%;
  transform:translateY(-50%);
  transform:translateX(-50%);
}
.arrowl:before {
  content:"chevron_left";
  font-family:'Material Icons Sharp';
}
.arrowr:before {
  content:"chevron_right";
  font-family:'Material Icons Sharp';
}
.menu-onglet {
  width:100%;
  height:60px;
  list-style-type:none;
  margin:0;
  overflow:hidden;
  transition:2.0s;
  white-space:nowrap;
}
.menu-onglet li {
  display:inline-block;
  height:60px;
  line-height:60px;
  font-size:13px;
  padding:0 18px;
  transition:0.5s;
}
.menu-onglet li.selected, .menu-onglet li:hover {
  border-bottom:0.325rem solid #0077D4;
  font-weight:600;
  transition:0.5s;
}
.menu-onglet li a { color:#212121; }
.ltr_R, .rtl_L {
  float:right;
  margin:0 0 0 20px;
}
.ltr_L, .rtl_R {
  float:left;
  margin:0 20px 0 0;
}
.myGallery dl img {
  width:80px;
  height:auto;
}
.myScreen img { height:auto; }
.myGallery dl {
  float:left;
  margin:5px;
}
.myGallery { display:inline-block; }
.copyright { display:none; }
.myScreen {
  width:100%;
  height:auto;
}
.js-slider-full {
  position:relative;
  width:100%;
  height:39rem;
}
.js-slider-full .wrapper-image {
  position:relative;
  height:100%;
}
.js-slider-full .wrapper-image img {
  width:100%;
  height:100%;
  -o-object-fit:cover;
  object-fit:cover;
}
.js-slider-full .wrapper-image::after {
  content:'';
  position:absolute;
  z-index:1;
  background:linear-gradient(180deg,rgba(0,0,0,0.55) 0%,rgba(0,0,0,0.25) 100%);
  width:100%;
  height:100%;
  top:0;
  left:0;
}
.js-slider-full .wrapper-infos {
  position:absolute;
  width:100%;
  padding:0 1.5rem;
  bottom:11rem;
  left:50%;
  transform:translateX(-50%);
  z-index:3;
}
@media screen and (max-width: 992px) {
  .js-slider-full .wrapper-infos { bottom:3rem; }
}
.js-slider-full .wrapper-infos .field--name-title, .js-slider-full .wrapper-infos .slide-title {
  font-size:4rem;
  font-weight:800;
  color:#fff;
  margin-bottom:2.25rem;
}
@media screen and (max-width: 992px) {
  .js-slider-full .wrapper-infos .field--name-title, .js-slider-full .wrapper-infos .slide-title { font-size:3rem; }
}
.js-slider-full .wrapper-infos .slide-text a { color:#fff; }
.js-slider-full .wrapper-infos .slide, .js-slider-full .wrapper-infos .wrapper-image {
  height:100%;
  position:relative;
}
.js-slider-full .wrapper-infos .wrapper-image img {
  width:100%;
  height:100%;
  -o-object-fit:cover;
  object-fit:cover;
}
.js-slider-full .wrapper-infos .slide-link .btn { margin-right:10px; }
.js-slider-full .wrapper-infos .photoBlock-legend-action {
  bottom:0;
  right:14rem;
}
.js-slider-full .wrapper-infos .content-copyright {
  position:absolute;
  right:3.5rem;
  bottom:-49px;
}
.js-slider-full .wrapper-infos .content-copyright:hover .popin-hover {
  display:block;
  bottom:60px;
  opacity:70%;
}
.js-slider-full .wrapper-infos .content-copyright .popin-hover {
  display:none;
  bottom:60px;
  opacity:0;
  position:absolute;
  width:305px;
  left:-300px;
  transition:opacity 0.5s;
  background-color:black;
  color:#fff;
  border:0;
}
@media (min-width: 992px) {
  .js-slider-full { height:75rem; }
}
.title-1 {
  color:#3087d1;
  font-size:2.15em;
  font-weight:normal;
  margin-bottom:10px;
}
.wiki-text {
  margin:0 0 10px 0;
  font-family:"Inter", sans-serif;
  font-size:1em;
  font-weight:normal;
  line-height:1.4em;
  color:#000;
}
.toc {
  margin:0 0 64px 0;
  padding:0;
  position:relative;
}
.toc:before {
  background-color:#F1F4F6;
  border-radius:5px;
  content:'';
  height:86%;
  left:0;
  position:absolute;
  top:14px;
  width:1px;
}
.toc li {
  margin-bottom:23px;
  padding:0 0 0px 18px;
  position:relative;
}
.toc li:last-child { margin-bottom:0; }
.toc li a { color:#212121; }
.toc li a:before {
  background-color:#B1BABE;
  border-radius:50%;
  content:"";
  height:5px;
  position:absolute;
  left:-2px;
  top:8px;
  width:5px;
}
.toc li a br { display:none; }
.toc_rules {
  width:200px;
  float:right;
  margin:0 0 10px 10px;
  padding:2px 10px 0 10px;
  background-color:#EDEDEE;
  border:1px solid #C4CFD8;
}
.toc_all {
  margin:0 0 10px 0;
  padding:2px 10px 0 10px;
  background-color:#EDEDEE;
  border:1px solid #C4CFD8;
}
.toc_all li, .toc_rules li {
  list-style:none;
  background:none !important;
  padding:2px 0 2px 10px !important;
}
.toc_level_1 {
  font-size:0.95em;
  margin-left:20px;
}
.toc_level_2 {
  font-size:0.9em;
  margin-left:40px;
}
.emphasy {
  font-weight:bold;
  background:#FFF6BF;
}
.lang-shortcut:after { content:" - "; }
.lang-shortcut:last-child:after { content:""; }
.pd-lft { padding-left:15px; }
.pd-lft2 { padding-left:30px; }
.block_title {
  font-size:1.2857142857143em;
  color:#0872cb;
  font-weight:bold;
  line-height:1.15;
  margin:0 0 15px;
}
.element-title { padding-left:0 !important; }
.element-country { font-size:1.5em; }
.participant-list h3 { margin-bottom:0; }
p.part-list-total {
  margin-top:20px;
  margin-bottom:15px;
  font-weight:bold;
}
span.part-list-org-type { font-weight:bold; }
span.part-list-org-detail { color:grey; }
.participant-list a[href='#top'] img {
  width:20px;
  position:relative;
  top:-20px;
}
#cases-studies #div-cases-studies .div-case-study article, #cases-studies #div-cases-studies .div-case-study article .case-study-img { width:100%; }
#cases-studies #div-cases-studies .div-case-study article {
  margin:30px 0 50px;
  overflow-x:hidden;
  overflow-y:auto;
}
#cases-studies-map .leaflet-popup-content {
  max-height:250px;
  overflow-x:hidden;
  overflow-y:auto;
}
#cases-studies-map .leaflet-popup-content hr { margin:5px 0; }
#cases-studies-map .leaflet-popup-content a { font-size:larger; }
#cases-studies-search-form dl dt, #cases-studies-search-form dl dd {
  width:auto;
  clear:none;
}
#cases-studies-search-form dl dd div, #cases-studies-search-form dl dd input { width:100%; }
#cases-studies-search-form dl dd #uniform-case_study_sdg_ids span { width:270px; }
#cases-studies-search-form dl dd #uniform-case_study_country_ids span { width:260px; }
#cases-studies-search-form dl dd #uniform-case_study_author_ids span { width:160px; }
#cases-studies-search-form  dl label { padding:9px 7px 0; }
.case-study #link-page-search { margin:10px 0; }
.case-study .title, .case-study p.countries, .case-study .sdgs {
  overflow:hidden;
  overflow-y:auto;
  margin:0;
  padding:0;
  margin-bottom:5px;
  width:100%;
}
.case-study div.image { height:165px; }
.case-study .title {
  height:31px;
  margin:5px 0;
}
.case-study p.countries {
  height:40px;
  margin-bottom:0;
}
.case-study .sdgs {
  height:60px;
  margin-top:-15px;
}
.case-study li.countries { width:100%; }
.table { border-collapse:separate; }
.table thead {
  background-color:#004d79;
  color:#fff;
}
.table, .table th, .table td { border:1px solid #c7c7c7; }
.table th, .table td { padding:5px 10px; }
.table tbody th { background-color:#dadada; }
.table-subhead { text-align:left; }
.table tbody td { background-color:#0077D4; }
.table-item { text-align:center; }
.align-left { text-align:left; }
.align-right { text-align:right; }
.align-center { text-align:center; }
.block_table {
  margin:40px 0 50px 0;
  clear:both;
}
.block_table .block_table-header .block_table-title {
  font-size:3rem;
  font-weight:600;
}
.block_table .block_table-filters {
  background:#dadada;
  padding:20px;
}
.block_table .block_table-filters.sp-right { text-align:right; }
.block_table form+form { margin-top:-20px; }
.sp-nolabel .fixedWidth { width:95%; }
.block_tabs + .block.activities .block_table { margin:0 0 20px; }
.block_tabs + .block.activities .block_table-header { margin:0 0 20px; }
.table_design {
  width:100%;
  border:none;
}
.table_design > thead > tr > th, .table_design > thead > tr > td, .table_design > tbody > tr > th, .table_design > tbody > tr > td, .table_design > tfoot > tr > th, .table_design > tfoot > tr > td {
  vertical-align:top;
  border-top:1px solid #D5DADD !important;
  border-left:1px solid #D5DADD !important;
  border-bottom:none;
  border-right:none;
}
.table_design > thead > tr > th:last-child, .table_design > thead > tr > td:last-child, .table_design > tbody > tr > th:last-child, .table_design > tbody > tr > td:last-child, .table_design > tfoot > tr > th:last-child, .table_design > tfoot > tr > td:last-child { border-right:1px solid #D5DADD !important; }
.table_design > thead > tr > th p, .table_design > thead > tr > td p, .table_design > tbody > tr > th p, .table_design > tbody > tr > td p, .table_design > tfoot > tr > th p, .table_design > tfoot > tr > td p { margin:0 0 0 0; }
.table_design > thead > tr > th .small-item, .table_design > thead > tr > td .small-item, .table_design > tbody > tr > th .small-item, .table_design > tbody > tr > td .small-item, .table_design > tfoot > tr > th .small-item, .table_design > tfoot > tr > td .small-item { font-size:0.8em; }
.table_design tbody tr:last-child th:last-child {
  border-radius:0 8px 0 0;
  border-right:1px solid #D5DADD !important;
}
.table_design tbody tr:first-child th:first-child { border-radius:0; }
.table_design thead tr td, .table_design thead tr th {
  background-color:#0077D4;
  color:#fff;
  font-size:1.2em;
  height:30px;
  padding:10px;
  font-weight:normal;
}
.table_design thead tr td h2, .table_design thead tr th h2 { color:#fff; }
.table_design tr { border:none; }
.table_design tr th, .table_design tr td { padding:5px 10px; }
.table_design tr th {
  background-color:#F1F4F6;
  color:#212121;
  font-weight:600;
  padding:12px !important;
}
.table_design tr >td {
  background-color:#fff;
  padding:10px !important;
}
.table_design tr >td:last-child { border-right:1px solid #D5DADD !important; }
.table_design tr >td.sp-highlight { background-color:#62D162; }
.table_design tr >td .wiki-text {
  max-height:160px;
  overflow-y:scroll;
  background:#fefefe;
  padding:5px;
}
.table_design tr:last-child td { border-bottom:1px solid #D5DADD !important; }
.table_design h2 { margin:0; }
@media screen and (max-width: 767px) {
  .table_design {
    width:100%;
    overflow-x:scroll;
    display:block;
  }
  .table_design.table { margin-bottom:0; }
}
.listing .article_item {
  overflow:hidden;
  padding:20px 0;
}
.listing .picture {
  display:block;
  width:auto;
}
.pi_left {
  margin-right:20px;
  float:left;
}
.pi_left, .pi_right {
  display:table;
  margin-bottom:10px;
}
.listing_videos .picture a { position:relative; }
.picture a, .picture img, .picture_home a { display:block; }
.pi_left img, .pi_right img { max-width:none; }
.listing_videos .duration {
  border:1px solid #fff;
  font-size:0.92857142857143em;
  position:absolute;
  top:auto;
  right:40px;
  bottom:7px;
  left:auto;
  color:#fff;
  font-weight:normal;
  padding:2px 5px;
}
.listing_videos .article_title { margin-top:0; }
.listing .article_title { margin:.5em 0; }
.countries a { font-weight:normal; }
.countries, .countries a { color:#06a1b5; }
.listing .article_intro {
  margin-top:0;
  margin-left:240px;
}
.listing_videos .picture a:after, .listing_videos .picture a > iea {
  background:url('../design-img/sprites/sprite_global.png') -79px -114px no-repeat;
  height:23px;
  width:24px;
  content:'';
  display:inline-block;
  position:absolute;
  top:auto;
  right:8px;
  bottom:8px;
  left:auto;
}
.element_videos .picture a, .element_videos .picture_home a { position:relative; }
.element_videos .article_title { margin-top:0; }
.element_videos .picture a:after, .element_videos .picture a > iea {
  background:url('../design-img/sprites/sprite_global.png') -79px -114px no-repeat;
  height:23px;
  width:24px;
  content:'';
  display:inline-block;
  position:absolute;
  top:auto;
  right:166px;
  bottom:100px;
  left:auto;
}
.element_videos .picture2 a { position:relative; }
.element_videos .picture2 a:after {
  background:url('../design-img/sprites/sprite_global.png') -79px -114px no-repeat;
  height:23px;
  width:24px;
  content:'';
  display:inline-block;
  position:relative;
  top:auto;
  right:-55px;
  bottom:60px;
  left:auto;
}
.element_videos .picture_home a:after {
  background:url('../design-img/sprites/sprite_global.png') -79px -114px no-repeat;
  height:23px;
  width:24px;
  content:'';
  display:inline-block;
  position:absolute;
  top:auto;
  right:120px;
  bottom:75px;
  left:auto;
}
.play_container {
  position:relative;
  display:inline-block;
  margin:0 0 0 10px;
  border:1px solid #dadada;
}
.play {
  display:inline-block;
  width:24px;
  height:23px;
  position:absolute;
  top:50%;
  left:50%;
  margin:-12px 0 0 -12px;
  opacity:.7;
}
.play_lang {
  display:block;
  width:100%;
  background:#FFF;
  text-align:center;
}
.play_lang a:after {
  content:"|";
  color:#212121;
}
.play_lang a:last-child:after { content:""; }
.shortcut:after { content:" - "; }
.shortcut:last-child:after { content:""; }
#recording-container, #film-container {
  overflow:hidden;
  background:#fff none repeat scroll 0 0;
  color:#000000;
  font-size:0.8em;
  text-align:center;
}
#recording-container audio { width:100%; }
#recording-title, #film-title {
  font-size:1.5em;
  margin:0 auto 10px;
  text-align:center;
  width:500px;
  font-weight:normal;
}
.link-external {
  background:rgba(0,0,0,0) url("/design-img/background/bg_external_link.gif") no-repeat scroll right center;
  color:#214a87;
  font-weight:bold;
  margin:0 1px 0 0;
  padding:0 10px 0 0;
}
.videoBlock {
  text-align:center;
  background-color:#000000;
}
.videoBlock iframe, .videoBlock video { margin-bottom:0; }
.videoBlock-right {
  float:right;
  margin:0 0 10px 20px;
}
.videoBlock-left {
  float:left;
  margin:0 20px 10px 0;
}
.videoBlock-center { margin:10px auto; }
.videoBlock-legend {
  margin-top:0;
  color:#ffffff;
  padding:5px;
  padding-top:0;
  display:flex;
}
.videoBlock-legend-action {
  display:flex;
  margin-left:auto;
  margin-right:5px;
  align-items:center;
}
.videoBlock-legend-action a { color:#ffffff; }
.videoBlock-legend-content {
  font-size:0.8em;
  display:flex;
  align-items:center;
  justify-content:center;
  flex-grow:1;
}
.label-form {
  display:inline-block;
  width:180px;
  font-size:0.9em;
  vertical-align:top;
}
.input-form {
  background:transparent;
  margin:0 10px 10px;
  padding:5px 10px;
  border:1px solid #58595b;
  width:310px;
  font-size:12px;
  color:#58595b;
}
.combobox-form {
  background:transparent;
  display:inline-block;
  vertical-align:top;
  margin:0 10px 10px 0;
  padding:5px 10px;
  border:1px solid #58595b;
  font-size:12px;
  width:310px;
  color:#58595b;
}
.composed-field .textarea-form, .field-form {
  display:inline-block;
  padding:5px 10px;
  border:1px solid #58595b;
  width:310px;
  font-size:12px;
  color:#58595b;
}
.field-form { padding:0; }
.face {
  height:128px;
  width:auto;
}
.alert-text {
  background-color:#ffeaea;
  color:#400;
  padding:10px;
  margin-bottom:10px;
}
fieldset { margin-bottom:10px; }
.error-form {
  color:#aa0000;
  font-size:12px;
  font-style:italic;
  padding:0px;
}
.div-form {
  display:inline-block;
  vertical-align:top;
  width:180px;
  font-size:0.9em;
}
#desc-file { vertical-align:top; }
.trainer-form .select.fixedWidth { width:310px; }
.comboboxMulti-form {
  height:100px;
  width:100%;
  border:none;
}
aside fieldset, fieldset fieldset { border:none; }
.WAI {
  position:absolute;
  top:-9999px;
  left:-9999px;
}
.selectForm { padding:10px 0px; }
.remove-form {
  color:transparent;
  border:none;
  width:25px;
  height:25px;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat scroll -203px -296px transparent;
}
.gdgd {
  color:transparent;
  background:url("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgBBgkIBwgKCgk…V2U24wDcRo9/H+pRA09AO+HMpwDc6qCqxuSVuWIZR+0uNoRl1tLbaQhCAEpSkWAA6DBKo1//Z") no-repeat;
  border:none;
  background:url('../design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat scroll -203px -296px transparent;
  height:18px;
  width:18px;
  margin:0px 0px 0px 5px;
  vertical-align:top;
  display:inline-block;
}
.radio-intention {
  padding-right:25px;
  text-transform:capitalize;
}
#uniform-facilitator-registration-sexID2, #uniform-facilitator-registration-sexID1, #uniform-facilitator-registration-sexID0 { float:left; }
.radio-label-facilitator { float:left; }
.generated-form .gen-form-inline, .generated-form .gen-form-inline * { display:inline-block; }
.generated-form .gen-form-column .row * { display:inline-block; }
.subform label {
  display:inline-block;
  min-width:170px;
  max-width:170px;
  margin-right:20px;
}
.subform .radio-row { display:inline-block; }
.subform .radio-row span label {
  min-width:55px;
  margin-right:5px;
}
.subform .checkbox-row {
  display:inline-block;
  vertical-align:top;
}
.subform-table .sf-row label, .subform-table .sf-row .input, .subform-table .sf-row .edit, .subform-table .sf-row .remove { display:inline-block; }
.subform-table .button { min-width:35px; }
#reset-box, .formPage { display:none; }
.form-fields .checkbox {
  display:inline-block;
  margin-left:5px;
  width:325px;
}
.checkbox, .radio {
  margin-top:0px;
  margin-bottom:0px;
}
.formPage .checkbox input[type="checkbox"], .formPage .radio input[type="radio"] {
  position:relative;
  margin:0px;
}
.radio input[type="radio"].radio-label {
  position:relative;
  margin-left:0;
}
.gen-form-column > div.row {
  margin:0;
  display:block;
}
.gen-form-column > div { display:inline-block; }
.squaredOne {
  width:25px;
  height:25px;
  position:relative;
  background:#fcfff4;
  border:solid 2px #414042;
  box-sizing:content-box;
}
.squaredOne label {
  width:25px;
  height:25px;
  position:absolute;
  top:0px;
  left:0px;
  cursor:pointer;
  background:#dadada;
}
.squaredOne label:after {
  content:'';
  width:25px;
  height:25px;
  position:absolute;
  top:0px;
  left:0px;
  background:#0872cb;
  opacity:0;
}
.squaredOne label:hover::after { opacity:0.3; }
.squaredOne input[type=checkbox] { visibility:hidden; }
.squaredOne input:checked + label:after { opacity:1; }
.composed-field {
  overflow:auto;
  border:1px solid #0872cb;
  padding:10px;
  margin-bottom:15px;
  margin-left:30px;
  margin-right:30px;
  background-color:#e6ecf3;
}
.composed-field .cf_head {
  font-weight:bold;
  background-color:#0872cb;
  color:#FFFFFF;
  padding:5px;
  margin-bottom:10px;
}
.composed-field .ctn fieldset, .composed-field .ctn .composed-field > div {
  display:inline-block;
  margin-bottom:0;
  padding-bottom:0;
}
.composed-field .add-cf { margin-right:20px; }
.composed-field .block-nolegend:not(.cfSubBlock) legend {
  padding-bottom:10px;
  margin-bottom:10px;
  width:100%;
  font-size:1.3em;
  border-bottom:1px solid #CCCCCC;
}
.composed-field .block-nolegend {
  margin-top:15px;
  margin-bottom:20px;
}
.composed-field label { font-size:0.9em; }
.generated-form .composed-field .tableaux label { font-size:0.9em; }
.generated-form .textCheckComment {
  width:100%;
  height:100px;
}
.generated-form .depth1 { padding:0; }
.generated-form .depth1:not(.fld_question)>legend {
  color:#3f95c3;
  text-align:left;
  font-size:22px;
  width:100%;
  margin-bottom:20px;
  padding-bottom:20px;
}
.generated-form .depth2, .generated-form .depth3, .generated-form .depth4 { padding:0; }
.generated-form .fld_question {
  border-radius:5px;
  background-color:#F1F4F6;
  border:1px solid #dadada;
  padding:20px;
  margin-bottom:30px;
}
.generated-form .fld_question>legend {
  margin-bottom:0px;
  font-size:1.3em;
}
.generated-form .fld_question .intro {
  border-bottom:#dadada 1px solid;
  padding-bottom:10px;
  font-weight:normal;
  font-size:1.3em;
  margin-bottom:10px;
}
.generated-form .fld_question>legend {
  margin-left:20px;
  background-color:#F1F4F6;
  border:1px solid #dadada;
  border-radius:10px;
  min-height:30px;
  padding:5px 10px;
}
.generated-form .depth3 { padding:0; }
.generated-form legend {
  font-weight:normal;
  margin-bottom:0px;
  padding-bottom:0px;
  font-size:1em;
  display:table;
  max-width:100%;
}
.generated-form input[type="text"], .generated-form input[type="email"], .generated-form select {
  height:25px;
  line-height:25px;
  margin:0 5px;
  vertical-align:middle;
}
.generated-form label, .generated-form textarea { margin:5px; }
.generated-form .tableaux label {
  font-size:1em;
  width:auto;
}
.generated-form input[type="radio"].form-err, input[type="checkbox"].form-err { outline:2px solid red; }
.generated-form input[type="text"].form-err { border:2px solid red; }
.generated-form textarea.form-err { border:2px solid red; }
.generated-form select.form-err { border:2px solid red; }
.cf-onglet {
  margin-left:30px;
  margin-right:30px;
}
.fld-onglet .composed-field { display:none; }
.cf-onglet-item {
  display:inline-block;
  padding:5px;
  border:1px solid #808080;
  background-color:#CCCCCC;
  margin-right:3px;
  padding-left:12px;
  padding-right:12px;
  cursor:pointer;
  position:relative;
}
.cf-onglet-item.active {
  background-color:#EDEDED;
  border:1px solid #0872cb;
  border-top:4px solid #0872cb;
  font-weight:bold;
}
.cf-onglet-add {
  display:inline-block;
  padding:5px;
  margin-right:3px;
  cursor:pointer;
  font-size:0.9em;
  font-style:italic;
}
.cf-onglet-remove {
  position:absolute;
  top:-3px;
  right:-3px;
  height:16px;
  width:16px;
  background:url('/design-img/icon/picto_remove_blue_16x16.png') no-repeat;
}
.generated-form .pbar-lexical {
  position:relative;
  padding-top:5px;
  padding-bottom:15px;
}
.generated-form .indicator {
  text-align:center;
  position:absolute;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  font-size:12px;
}
.generated-form .indicator.indicatorresume {
  overflow:unset;
  position:relative;
  padding:2px;
}
.generated-form .indicator.not_satisfied {
  background-color:#E2E0E0;
  border:1px dotted #4a3f3f;
}
.irs-bar--single.irs-bar-not_satisfied {
  border:1px solid #E2E0E0 !important;
  background:linear-gradient(to bottom,#ffffff 0%,#7b7873 30%,#7b7873 100%) !important;
}
.generated-form .indicator.minimally {
  background-color:#8FD4F2;
  border:1px dotted #4a3f3f;
}
.irs-bar--single.irs-bar-minimally {
  border:1px solid #8FD4F2 !important;
  background:linear-gradient(to bottom,#ffffff 0%,#8FD4F2 30%,#8FD4F2 100%) !important;
}
.generated-form .indicator.partially {
  background-color:#5dc3f0;
  border:1px dotted #4a3f3f;
}
.irs-bar--single.irs-bar-partially {
  border:1px solid #5dc3f0 !important;
  background:linear-gradient(to bottom,#ffffff 0%,#5dc3f0 30%,#5dc3f0 100%) !important;
}
.generated-form .indicator.largely {
  background-color:#38afe3;
  border:1px dotted #4a3f3f;
}
.irs-bar--single.irs-bar-largely {
  border:1px solid #38afe3 !important;
  background:linear-gradient(to bottom,#ffffff 0%,#38afe3 30%,#38afe3 100%) !important;
}
.generated-form .indicator.satisfied {
  background-color:#1795fa;
  border:1px dotted #4a3f3f;
}
.irs-bar--single.irs-bar-satisfied {
  border:1px solid #1795fa !important;
  background:linear-gradient(to bottom,#ffffff 0%,#1795fa 30%,#1795fa 100%) !important;
}
.generated-form .irs--big .irs .irs-single, .generated-form .irs--big .irs .irs-max, .generated-form .irs--big .irs .irs-min, .generated-form .irs--big .irs .irs-to, .generated-form .irs--big .irs .irs-from { display:none; }
.generated-form .irs--big .irs .irs-line { top:5px; }
.generated-form .irs--big .irs-bar { top:5px; }
.generated-form .irs--big .irs-handle { top:-4px; }
.generated-form .irs--big { height:30px; }
.generated-form .summary .progbar {
  height:12px;
  box-shadow:inset 0 0 1px 1px rgba(255,255,255,0.5);
  position:relative !important;
}
.generated-form .click_intro a { background:url('/design-img/icons/tree.png') no-repeat 50% 50%; }
.generated-form .cfSubBlock {
  border:1px dotted #808080;
  padding:10px;
  margin-bottom:10px;
}
.generated-form .cfSubBlock>label { font-weight:bold; }
.generated-form .cfSubBlock legend {
  font-weight:bold;
  font-size:0.9em;
}
.generated-form .fld_question .separator {
  background:none;
  border-right:1px solid #a4c1de;
  height:16px !important;
  width:2px;
  margin:5px 5px;
  padding:5px 0;
  float:right;
}
.generated-form .fld_relatif { position:relative; }
.generated-form .factor-note {
  display:none;
  float:right;
  padding-right:5px;
  color:#a1a7ae;
  font-size:0.9em;
}
.generated-form .factor-note div { display:inline-block; }
.generated-form .summary {
  font-size:0.9em;
  border-collapse:collapse;
}
.generated-form .summary tr td {
  padding:15px;
  border:1px solid #CCCCCC;
}
.generated-form .summary tbody tr:not(.separator).sum2 { background:#e9eff4; }
.generated-form .summary tbody tr:not(.separator).sum2.impaire { background:#ebf4fb; }
.generated-form .summary tr:not(.separator).sum1 {
  background-color:#f4f5f5;
  transition:background .2s ease-in;
}
.generated-form .summary tr:not(.separator):hover { background:#fff !important; }
.generated-form .summary tr td.thematic {
  font-size:1.2em;
  color:#CCCCCC;
}
.generated-form .summaryOK {
  background:url('/design-img/icon/ok_responded.png') no-repeat;
  width:21px;
  height:22px;
}
.generated-form .summaryNone {
  background:url('/design-img/icon/none_responded.png') no-repeat;
  width:21px;
  height:22px;
}
.generated-form .summaryNoneNotResponded {
  background:url('/design-img/icon/none_not_responded.png') no-repeat;
  width:21px;
  height:22px;
}
.generated-form .summaryKO {
  background:url('/design-img/icon/ko_responded.png') no-repeat;
  width:21px;
  height:21px;
}
.generated-form .summaryKONotResponded {
  background:url('/design-img/icon/ko_not_responded.png') no-repeat;
  width:21px;
  height:21px;
}
.generated-form .summaryGOTO {
  background:url('/design-img/©_UNESCO_-_ICH_-_All_right_reserved.png') no-repeat;
  background-position:-184px -262px;
  width:17px;
  height:16px;
  cursor:pointer;
}
.generated-form .summary .summaryentete { background:transparent !important; }
.generated-form .summary .summaryentete td {
  font-weight:bold;
  text-align:center;
  font-size:0.7em;
  padding:10px;
}
.generated-form .summary .summaryniv1 { font-weight:bold; }
.generated-form .summary .summaryniv2 { padding-left:40px; }
.generated-form .summary .summaryniv3 { padding-left:80px; }
#formBtnPrevious {
  display:none;
  background:rgba(196,196,196,0.8) url('/design-img/icons/left-arrow.png') no-repeat center 50%;
  height:40px;
  width:40px;
  border-radius:10px;
  position:absolute;
  left:0;
}
#formBtnNext {
  display:none;
  background:rgba(196,196,196,0.8) url('/design-img/icons/right-arrow.png') no-repeat center 50%;
  height:40px;
  width:40px;
  border-radius:10px;
  position:absolute;
  right:0;
}
#infoFixed {
  position:fixed;
  top:30px;
  right:30px;
}
.generated-form li.onglet_small a.tab-style {
  width:25px !important;
  padding:0px !important;
}
.generated-form li.onglet_element a.tab-style {
  padding:5px 5px !important;
  font-size:13px;
}
.generated-form .activities {
  border:2px solid #ebebeb;
  background-color:#FFFFFF;
}
.generated-form .label-form-one-line {
  width:100%;
  margin-bottom:10px;
  padding-bottom:10px;
  border-bottom:1px dotted #CCCCCC;
}
.generated-form .block_tabs .tab-exam .tabItem { margin-top:5px; }
.generated-form .tableaux {
  width:100%;
  border-spacing:0;
}
.generated-form .tableaux:not(.dyna) tr td:first-child { width:90%; }
.generated-form .tableaux:not(.dyna) tr td.cell {
  width:50%;
  vertical-align:top;
}
.generated-form .tableaux.dyna tr td:first-child { width:75%; }
.generated-form .tableaux.dyna tr td.sys_link { width:5%; }
.generated-form .tableaux.dyna.more tr td:first-child { width:60%; }
.ICH-11 .generated-form .tableaux .select2-container.select2-container--disabled .select2-selection--multiple { max-width:none !important; }
.ICH-11 .generated-form .tableaux span.select2.select2-container.select2-container--default.select2-container--disabled {
  width:auto !important;
  max-width:280px !important;
}
.generated-form .tableaux tr {
  background-color:#eeeeee;
  transition:background .2s ease-in;
}
.generated-form .tableaux tr:hover { background:#fff !important; }
.generated-form .tableaux tbody tr:nth-child(2n+1) { background:#e6e6e6; }
.generated-form .tableaux td, .generated-form .tableaux th { padding:10px; }
.aligned-question { text-align:center; }
fieldset.activity {
  background:rgba(8,114,203,0.06);
  margin:0px;
  border-top:solid 1px #0872cb;
  border-bottom:solid 1px #0872cb;
}
fieldset.block-nolegend.clear.activity:first-of-type { border-left:solid 1px #0872cb; }
fieldset.block-nolegend.clear.activity:last-of-type { border-right:solid 1px #0872cb; }
fieldset.activity select, fieldset.activity input {
  height:2.34em !important;
  background:#ffffff !important;
}
.expense input.textarea-form { width:312px; }
.expense .devise-ctn input.textarea-form { width:125px; }
.composed-field .ctn .composed-field div {
  padding:0 0.625em;
  vertical-align:middle;
}
.composed-field .ctn {
  white-space:nowrap;
  width:100%;
}
.composed-field .ctn input[type=text], .composed-field .ctn select, .composed-field .ctn .select2-container {
  display:block;
  margin-left:0;
}
.composed-field .ctn input[type=text] { margin:0; }
.composed-field .ctn legend {
  white-space:pre-line;
  width:312px;
  min-height:47px;
  display:inline-block;
  margin-bottom:0 !important;
}
span.select2.select2-container.select2-container--default {
  width:100% !important;
  max-width:100% !important;
}
#elementsListForm1 { padding:0 15px 32px 15px; }
#elementsListForm1 .filter-large .select2-container, #elementsListForm1 .select2-container {
  width:100% !important;
  max-width:100% !important;
}
#elementsListForm1 .filter-large .select2-selection, #elementsListForm1 .select2-selection {
  width:100% !important;
  max-width:100% !important;
}
.select2-container--default .select2-selection--multiple .select2-selection__rendered { width:auto; }
#elementsListForm1 .button { margin:0; }
#elementsListForm1 .input-2 { width:100%; }
.activity-fieldset-title {
  border:1px solid #7F888F;
  border-radius:50px;
  color:#7F888F;
  cursor:pointer;
  display:inline-block;
  padding:10px 50px 10px 25px;
  position:relative;
}
.activity-fieldset-title .arrow {
  position:absolute;
  bottom:auto;
  left:auto;
  height:17px;
  width:19px;
}
.activity-fieldset-title .arrow.arrow-up, .activity-fieldset-title .arrow.arrow-down {
  background:url(../design-img/arrow-select.svg) no-repeat;
  right:21px;
  top:9px;
  transition:0.5s;
}
.activity-fieldset-title .arrow.arrow-down {
  right:6px;
  top:15px;
  transform:rotate(180deg);
}
.form-project fieldset {
  border-bottom:1px solid #D5DADD;
  padding:15px 0;
}
.form-project fieldset:first-child { padding-top:0; }
.form-project fieldset:last-child { padding-bottom:0; }
.form-project fieldset .line-add-filter {
  color:#212121;
  cursor:pointer;
  display:block;
  font-weight:600;
  font-size:18px;
  position:relative;
  width:100%;
}
.form-project fieldset .line-add-filter .arrow {
  height:20px;
  width:20px;
}
.form-project fieldset .line-add-filter .arrow::after {
  content:"chevron_right";
  color:#212121;
  font-family:'Material Icons Sharp';
  font-feature-settings:'liga' 1;
  font-weight:400;
  height:20px;
  position:absolute;
  transition:0.5s;
  font-size:20px;
  right:0;
  width:20px;
}
.line-add-filter .arrow.arrow-down:after { transform:rotate(90deg); }
.line-add-filter .arrow.arrow-up:after { transform:rotate(270deg); }
#elementsListForm1 label { margin-right:5px; }
#biblio-div .select2-container {
  max-width:none !important;
  padding:0 !important;
  width:auto !important;
}
#biblio-div .select2-container .selection { padding:0 !important; }
#biblio-div .select2-container .select2-selection--single {
  border:none;
  height:100%;
}
#biblio-div .select2-selection--multiple { width:514px !important; }
#biblio-div .author_labels {
  display:inline-block;
  width:36.5%;
  padding-left:10px;
  font-style:italic;
}
#biblio-div .authors-row { margin-top:10px; }
.ICH-11 span.select2.select2-container.select2-container--default.select2-container--disabled {
  width:600px !important;
  max-width:600px !important;
}
.ICH-11 .select2-container--default.select2-container--disabled .select2-selection--multiple .select2-selection__choice { margin-right:600px !important; }
.ICH-11 .select2-container--disabled li.select2-search.select2-search--inline { display:none; }
.composed-field > .devise-ctn { width:197px; }
.composed-field .ctn fieldset.devise, input.textarea-form.devise { width:125px; }
.composed-field .ctn fieldset.devise legend { width:125px; }
.cf-button {
  display:inline-block;
  margin-bottom:2px;
}
.cf-button button {
  min-width:32px;
  height:32px;
}
.cf-button button:first-child { margin-right:2px; }
.aligned-question .devise-ctn { display:inline-block; }
.aligned-question .devise-ctn * { display:block; }
.form-ngo-desc-top {
  font-size:16px;
  text-transform:uppercase;
  text-align:center;
  font-weight:bold;
}
.form-ngo-desc-bottom {
  text-align:center;
  font-style:italic;
}
.form-ngo-desc-bottom a {
  text-transform:uppercase;
  font-weight:bold;
  text-decoration:underline;
}
body .form hr {
  border-color:#D5DADD;
  margin:3.2rem 0;
  width:100%;
}
.form-ich-ngo .italic { font-style:italic; }
.form-ich-ngo .bold { font-weight:bold; }
.form-ich-ngo hr.ich08-b1 {
  height:2px;
  border:none;
  color:#888;
  background-color:#888;
}
.form-ich-ngo ul {
  margin-top:10px;
  margin-left:-20px;
}
.form-ich-ngo .blt {
  font-weight:bold;
  vertical-align:top;
  display:inline-block;
  width:5%;
}
.form-ich-ngo .with-blt {
  display:inline-block;
  width:93%;
}
.form-ich-ngo .blt-sm {
  font-weight:bold;
  display:inline-block;
  vertical-align:top;
  width:5%;
}
.form-ich-ngo .with-blt-sm {
  display:inline-block;
  width:93%;
}
.form-check.form-check-inline { margin-bottom:3px; }
.form-check.form-check-inline .form-check-input {
  display:inline-block;
  width:auto;
  margin:0 2px;
}
.form-check.form-check-inline a#add-author { color:#4FB293; }
#timeline {
  height:500px;
  position:relative;
  z-index:100;
  clear:both;
}
#timeline-embed {
  padding-top:6px;
  padding-bottom:6px;
}
.cf { zoom:1; }
.cf:before, .cf:after {
  content:"";
  display:table;
  line-height:0;
}
.cf:after { clear:both; }
#footer {
  background-color:#F1F4F6;
  color:#212121;
  font-size:14px;
  font-style:normal;
  padding:25px 0 0 0;
}
#footer .explore-footer {
  background-color:#0077D4;
  margin-top:34px;
  padding:23px 0;
}
#footer .explore-footer a { color:#fff; }
#footer .explore-footer .explore-unesco {
  display:inline-block;
  margin:0 auto;
  padding:0 1.8rem 0 1rem;
  position:relative;
}
#footer .explore-footer .explore-unesco img { margin-top:-2px; }
@media screen and (max-width: 767px) {
  #footer .row .col-xs-5 { margin-left:20px; }
}
#footer .logo-unesco-footer {
  border-top:1px solid #D5DADD;
  height:auto;
  padding:30px 0;
  width:220px;
}
.main_container {
  zoom:1;
  margin:0 auto;
  position:relative;
  width:1024px;
}
.main_container:before, .main_container:after {
  content:"";
  display:table;
  line-height:0;
}
.main_container:after { clear:both; }
.invisible, .anchor, .tabs_ready .tab_title, .no_legend legend {
  clip:rect(1px 1px 1px 1px);
  clip:rect(1px,1px,1px,1px);
  position:absolute !important;
}
#footer .col {
  float:left;
  width:148px;
}
#footer .col-sm-12:last-child .cat_title:after { display:none; }
#footer .cat_title {
  border-top:1px solid #D5DADD;
  font-size:14px;
  font-weight:bold;
  margin:0 0 20px;
  padding-top:1rem;
}
@media screen and (max-width: 992px) {
  #footer .cat_title { padding:30px 0; }
  #footer .cat_title.arrow_up:after {
    transform:rotate(180deg);
    transition:0.5s;
  }
  #footer .cat_title:after {
    content:"expand_more";
    font-family:'Material Icons Sharp';
    font-feature-settings:'liga' 1;
    font-weight:400;
    position:absolute;
    transition:0.5s;
    font-size:30px;
    right:8px;
    top:20px;
  }
}
#footer ul {
  list-style:none;
  border:0;
  margin:0;
  padding:0 0 30px 0;
  vertical-align:baseline;
}
#footer ul li {
  margin-bottom:10px;
  position:relative;
}
#footer ul a, #footer ul .link.block {
  color:#212121;
  display:block;
  transition:0.5s;
}
#footer ul a:hover, #footer ul .link.block:hover {
  color:#0077D4;
  transition:0.5s;
}
#footer ul + .cat_title { margin-top:30px; }
#footer .col + .col { margin:0 0 0 20px; }
#footer .col {
  float:left;
  width:230px;
}
#footer #sitemap-secondary {
  clear:both !important;
  padding:20px 0 0;
  text-align:center;
}
#thanks_SAU, #thanks_SAU ul, #thanks_SAU img { width:100%; }
#thanks_SAU ul { display:table; }
#thanks_SAU li {
  display:table-cell;
  vertical-align:middle;
}
#thanks_SAU li#thanks_SAU_text {
  width:70%;
  padding:10px;
  font-size:69%;
  color:black;
  font-weight:bold;
}
#thanks_SAU li#thanks_SAU_img { width:30%; }
#thanks_SAU span { font-size:1.5em; }
#thanks_SAU img {
  width:100%;
  text-align:right;
}
@media screen and (max-width: 992px) {
  .collapse { display:none; }
  .collapse.in {
    display:block;
    background:#0077D4;
    width:100%;
    left:0;
    height:100%;
    overflow:auto;
  }
}
.copyright-footer {
  color:#0077D4;
  margin-top:10px;
}
.reseaux li a {
  display:inline-block;
  height:33px;
  line-height:33px;
}
.reseaux li a img { margin-right:10px; }
.reseaux li:hover svg { color:#0077D4; }
.wiki-list {
  margin:0;
  padding:0;
}
.wiki-enum { list-style:decimal; }
.wiki-list-item {
  background:transparent url(/design-img/background/bg_bullet_blue.gif) no-repeat scroll 4px 6px;
  padding:0 0 5px 20px;
}
.wiki-enum-item {
  margin:0 0 0 20px;
  padding:0 0 10px 0;
}
.wiki-sublist-item {
  background:transparent url(/design-img/background/bg_bullet_blue.gif) no-repeat scroll 4px 6px;
  padding:0 0 10px 20px;
}
.wiki-sublist { margin:7px 0 0 0; }
.wiki-sublist-item {
  background:transparent url(/design-img/background/bg_bullet_blue.gif) no-repeat scroll 4px 6px;
  padding:0 0 10px 20px;
}
.wiki-text {
  margin:0 0 10px 0;
  font-family:"Inter", sans-serif;
  font-size:1em;
  font-weight:normal;
  line-height:1.4em;
  color:#000;
}
.box-secondary .wiki-text { color:#fff; }
.wiki-table {
  width:99%;
  border:1px solid #CCC;
  margin:0 0 10px 0;
}
.wiki-table-head {
  background:#005CA1;
  color:#FFF;
  padding:5px 10px;
  text-align:left;
}
.wiki-table-item {
  padding:5px 10px;
  text-align:left;
  vertical-align:top;
  background-color:#E3EAF4;
}
.wiki-table-item div.isr_comment {
  padding:5px;
  background-color:white;
  overflow-y:scroll;
  max-height:4.7em;
  min-height:4.7em;
  font-size:0.8em;
}
.wiki-hr {
  margin:0 0 10px 0;
  color:#3F95C3;
}
.wiki-dl { margin:0 0 10px 0; }
.wiki-dl
dd {
  margin:0 0 0 40px;
  word-break:break-word;
}
.wiki-blockquote {
  width:300px;
  float:right;
  border:none;
  background:#F1F4F6;
  border-radius:8px;
  margin:0 0 0 10px;
  padding:20px;
}
.wiki-blockquote li { margin-bottom:1.5rem; }
@media screen and (max-width: 768px) {
  .wiki-blockquote { float:none; }
}
.pager, .pagination {
  font-size:0.92857142857143em;
  clear:both;
  margin:0;
  padding:15px 0;
  text-align:center;
}
.pager li a:hover, .pager .selected, .pagination li a:hover, .pagination .selected {
  background-color:#F1F4F6;
  border:1px solid #F1F4F6;
  transition:0.5s;
}
.pager .step, .pagination .step {
  height:39px;
  width:39px;
}
.pager .next:before, .pager .last:before, .pager .last:after, .pagination .next:before, .pagination .last:before, .pagination .last:after { content:"chevron_right"; }
.pager .previous:before, .pager .first:before, .pager .first:after, .pagination .previous:before, .pagination .first:before, .pagination .first:after { content:"chevron_left"; }
.pager li .last:before, .pagination li .last:before, .pager li .first:before, .pagination li .first:before { left:6px; }
.pager li .last:after, .pagination li .last:after, .pager li .first:after, .pagination li .first:after { left:12px; }
.pager .next:before, .pager .last:before, .pager .last:after, .pagination .next:before, .pagination .last:before, .pagination .last:after, .pager .previous:before, .pager .first:before, .pager .first:after, .pagination .previous:before, .pagination .first:before, .pagination .first:after {
  color:#212121;
  font-family:'Material Icons Sharp';
  font-feature-settings:'liga' 1;
  font-weight:400;
  left:9px;
  position:absolute;
  top:5px;
  transition:0.5s;
  font-size:20px;
}
.pager ol, .pager li, .pagination ol, .pagination li {
  display:inline-block;
  vertical-align:top;
}
.ielt8 .pager ol, .ielt8 .pager li, .ielt8 .pagination ol, .ielt8 .pagination li {
  display:inline;
  zoom:1;
}
.pager ol, .pagination ol {
  list-style:none;
  margin:0 15px;
  padding-left:0;
}
.pager li, .pagination li { margin-right:16px; }
.pager li a, .pager li strong, .pagination li a, .pagination li strong {
  border:1px solid #D5DADD;
  color:#212121;
  display:inline-block;
  border-radius:50%;
  padding:10px 15px;
  position:relative;
  transition:0.5s;
}
.pager a, .pagination a { font-weight:bold; }
.pagination > li:first-child > a, .pagination > li:first-child > span {
  margin-left:0;
  border-bottom-left-radius:50%;
  border-top-left-radius:50%;
}
.pagination > li:last-child > a, .pagination > li:last-child > span {
  border-bottom-right-radius:50%;
  border-top-right-radius:50%;
}
.pagination > li > a, .pagination > li > span { line-height:1.4; }
.photo-text_right {
  display:block;
  float:left;
  margin:0 15px 10px 0 !important;
  max-width:390px;
}
.img_box {
  position:relative;
  line-height:0px;
}
.img_box img { width:auto; }
.img_overlay {
  position:absolute;
  top:0;
  left:0;
  width:100%;
  opacity:.7;
  filter:alpha(opacity=70);
  text-align:right;
}
.photo-legend, .photo-legend-slideshow {
  text-align:left;
  font-size:1.6rem;
  font-style:normal;
  color:#212121;
}
.photo-legend-slideshow { min-height:32px; }
dd {
  margin:0;
  padding:0;
}
.photo-text_left {
  display:block;
  float:right;
  margin:0 0 10px 15px !important;
}
.crop {
  height:145px;
  border-radius:8px;
  overflow:hidden;
  width:100%;
  margin-bottom:12px;
}
.crop img {
  height:auto;
  margin:0px;
  width:100%;
}
.photo-legend .link {
  display:block;
  margin:2px 0 0;
  color:#3087d1;
}
.photo-legend .copyright-caption {
  margin:5px 0;
  display:block;
}
.photoBlock {
  margin-bottom:10px;
  display:table;
  position:relative;
}
.photoBlock-right {
  float:right;
  margin:0 0 10px 20px;
}
.photoBlock-left {
  float:left;
  margin:0 20px 10px 0;
}
.photoBlock-center { text-align:center; }
.photoBlock-photo { display:inline-block; }
.photoBlock-legend {
  margin-top:0;
  padding:5px;
  padding-top:0;
  display:table-caption;
  caption-side:bottom;
  position:relative;
}
.photoBlock-legend-content {
  word-break:break-word;
  font-size:0.8em;
  color:#212121;
}
.photoBlock-legend-content .link { display:block; }
.photoBlock img { border-radius:8px; }
.img-h { height:100%; }
.img-w { width:100%; }
.page_filters {
  zoom:1;
  background:#e9e1c9;
  margin:0 0 20px;
  padding:20px;
}
.page_filters .select, .page_filters select {
  min-width:140px;
  max-width:140px;
}
.page_filters .select {
  font-size:0.85714285714286em;
  border-color:#c6c2b5;
}
.page_filters input[type=text] {
  width:200px;
  height:35px;
}
select, .select, .input_file {
  display:inline-block;
  cursor:pointer;
  overflow:hidden;
  position:relative;
  background:#fff;
  border:1px solid #D5DADD;
  border-radius:72px;
  color:#7F888F;
  font-size:16px;
  height:48px;
  padding:0 20px 0 0;
  vertical-align:middle;
}
#form_org_inst_exp .fixedWidth { margin:4px 0px; }
.filter .fixedWidth, .select.fixedWidth, .block_country-select select { width:100%; }
.block_country-select select.combobox-2 { margin:5px 0; }
.fixedWidth { width:50%; }
select, input[type=file] { height:35px; }
.input_file input, .select select {
  -webkit-opacity:0;
  -khtml-opacity:0;
  -moz-opacity:0;
  opacity:0;
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  border:0;
  cursor:pointer;
  padding:0;
  width:100%;
  z-index:10;
}
#form_upload #uniform-form_upload_document { cursor:pointer; }
#form_upload #uniform-form_upload_document input#form_upload_document { width:100%; }
.select span, .combobox-2, .input_file_name {
  background-color:#fff !important;
  display:block;
  height:48px;
  line-height:48px;
  overflow:hidden;
  padding:0 20px;
  position:relative;
  white-space:nowrap;
}
.select .arrow, .input_file .action {
  position:absolute;
  top:12px;
  right:0;
  bottom:auto;
  left:auto;
  height:35px;
  width:35px;
  background:url('../design-img/arrow-select.svg') no-repeat;
}
#periodicReportsFilter .grid-full { text-align:center; }
#periodicReportsFilter .filter-line {
  text-align:left;
  display:inline-block;
}
#periodicReportsFilter .submit-filter {
  text-align:right;
  width:94%;
}
.page_filters .form_label {
  font-size:0.85714285714286em;
  display:inline-block;
  font-weight:bold;
  margin:0 10px 0 0;
  min-width:100px;
  max-width:100px;
  vertical-align:top;
}
.page_filters .form_field, .page_filters .form_field .radio, .page_filters .form_field .checkbox { display:inline; }
.form_btn {
  background:#0872cb;
  border:0;
  color:#e7f1fa;
  font-size:14px;
  padding:8px 15px;
  text-align:center;
}
.form_result_count { color:#212121; }
.researcher-list form dl input:not([type="checkbox"]), .researcher-list form dl textarea {
  width:94%;
  height:40px;
  border-radius:0;
  margin:0;
}
.researcher-list form input#form_title, .researcher-list form input#form_title+div.checkbox {
  display:inline-block;
  width:47%;
  vertical-align:middle;
}
.researcher-list form input#form_title+div.checkbox, .researcher-list form input#form_title+div.checkbox label.label-form { padding:0; }
.researcher-list .form.filter fieldset { padding:0; }
.researcher-list .form.filter fieldset .activity-fieldset-title { margin:5px 0; }
.researcher-list .form.filter #uniform-form_search_abstract {
  padding-top:0;
  min-height:0;
}
.researcher-list .form.filter #form_title+div.checkbox label { font-weight:normal; }
.researcher-list .form.filter dt { width:30%; }
.researcher-list .form.filter dd { width:70%; }
.researcher-list .form.filter dd .select2.select2-container.select2-container--default.select2-container--focus {
  width:390px !important;
  max-width:390px !important;
}
.researcher-list .form.filter dd .select2-container .select2-selection--multiple { width:372px; }
.researcher-list .form.filter dd .select2-container .select2-search--inline .select2-search__field { margin-top:0; }
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  border:0px;
  background-color:transparent;
}
.arialNarrow { font-family:Arial Narrow, Arial, Helvetica, sans-serif; }
.event_highlight {
  background:#58595b;
  color:#ebebeb;
  padding:20px;
  position:relative;
  min-height:120px;
  margin-bottom:20px;
}
.event_highlight .type { color:#eb5e4c; }
.event_highlight p { padding:0 0 0 80px; }
.event_name {
  background:#eb5e4c;
  color:#fff;
  display:block;
  font-weight:bold;
  line-height:1.1;
  padding:5px 0;
  text-align:center;
}
a.event_name:hover, a.event_name:focus {
  background:#fff;
  color:#eb5e4c;
  text-decoration:none;
}
.event_name span { display:block; }
.event_name span sup { vertical-align:top; }
#page_header .event_name {
  width:140px;
  font-size:2.1428571428571em;
  margin:0 20px 0 0;
  float:left;
}
#page_header .event_name span { font-size:4em; }
#page_header .event_name span sup { font-size:0.28333333333333em; }
.event_highlight .event_name {
  font-size:0.92857142857143em;
  position:absolute;
  top:20px;
  right:auto;
  bottom:auto;
  left:20px;
  width:60px;
}
.event_highlight .event_name span {
  font-family:Arial Narrow, Arial, Helvetica, sans-serif;
  font-size:4em;
}
.event_highlight .event_name span sup { font-size:0.28846153846154em; }
.icon-capacity-building {
  background:url('../design-img/icons/capacity-building-16.png') left top no-repeat;
  width:16px;
  height:16px;
  display:inline-block;
}
.icon-info {
  background:url('../design-img/icons/info-16.png') left top no-repeat;
  width:16px;
  height:16px;
  display:inline-block;
}
.icon-safeguarding {
  background:url('../design-img/icons/safeguarding-16.png') left top no-repeat;
  width:16px;
  height:16px;
  display:inline-block;
}
.lst_dtl_toggle_all, .lst_dtl_item .btn_toggle {
  font-size:0.92857142857143em;
  color:#414042;
  font-weight:bold;
  padding:3px 35px 0 0;
  position:relative;
}
.lst_dtl_toggle_all:after, .lst_dtl_toggle_all > iea, .lst_dtl_item .btn_toggle:after, .lst_dtl_item .btn_toggle > iea {
  background:url('../design-img/sprites/sprite_global.png') -76px -201px no-repeat;
  height:24px;
  width:18px;
  content:'';
  display:inline-block;
  background-color:#58595b;
  position:absolute;
  top:0;
  right:0;
  bottom:auto;
  left:auto;
}
.all_opened .lst_dtl_toggle_all:after, .all_opened .lst_dtl_toggle_all > iea, .lst_dtl_item.opened .btn_toggle:after, .lst_dtl_item.opened .btn_toggle > iea { background-position:-94px -201px; }
.lst_dtl_toggle_all { margin:0 0 15px; }
.listing_detail .lst_dtl_toggle_all { color:#414042; }
.js .lst_dtl_toggle_all { display:inline-block; }
.lst_dtl_item {
  background:#ebebeb;
  padding:20px;
}
.lst_dtl_item .infos_activity { margin:0; }
.lst_dtl_item .lst_dtl_title, .lst_dtl_item .lst_dtl_desc { font-size:0.92857142857143em; }
.lst_dtl_item .lst_dtl_title { color:#414042; }
.lst_dtl_item .lst_dtl_desc, .lst_dtl_item .btn_toggle {
  border-top:1px solid #c7c7c7;
  border-top:1px solid #D5DADD;
  margin:0 20px;
}
.lst_dtl_item .lst_dtl_desc {
  padding:20px 0;
  position:relative;
  overflow:hidden;
  zoom:1;
}
.lst_dtl_item .btn_toggle {
  padding-top:3px;
  text-align:right;
}
.js .lst_dtl_item .btn_toggle { display:block; }
.lst_dtl_item .friendly_url {
  position:absolute;
  top:10px;
  right:0;
  bottom:auto;
  left:auto;
}
.lst_dtl_item.even { background:#dadada; }
.lst_dtl_item.opened .lst_dtl_desc { display:block; }
.td_typeentity { font-weight:bold; }
.decision_display blockquote, .decision_display ul, .decision_display ol, .decision_display dl { text-align:justify; }
.decision_display > ol > li {
  list-style:decimal outside;
  margin-bottom:5px;
}
.decision_display ul li {
  list-style:disc outside;
  margin-bottom:5px;
}
.researcher-list-buttons-wrapper a, .researcher-list-buttons-rev-wrapper a, .researcher-list-buttons-rev-wrapper i { font-size:17px; }
.glyphicon-cloud-download.no_file { color:#f00; }
.biblio_info_popin {
  display:none;
  padding:15px;
  background-color:#115A9E;
  color:#fff;
}
.biblio_info_popin ul { padding:0; }
.biblio_info_popin ul li p { margin:0 0 5px 0; }
.biblio_info_popin ul li .biblio_popin_label { font-weight:bold; }
.biblio_info_popin ul li .biblio_popin_value { padding-left:20px; }
.biblio_info_popin ul li table.bibliographic_data { margin:10px 0 10px 40px; }
.biblio_info_popin ul li table.bibliographic_data td:first-child { width:20%; }
.biblio_info_popin ul li table.bibliographic_data td:nth-child(2) { padding-left:5px; }
.biblio_info_popin a {
  color:#fff;
  text-decoration:underline;
}
.researcher-list #lists-tabs { margin-top:20px; }
@media (max-width: 1200px) {
  .researcher-list #lists-tabs { margin-top:40px; }
}
.researcher-list table.table_design td.history ul {
  margin:0;
  padding:2px;
  max-height:75px;
  overflow-y:auto;
}
.researcher-list table.table_design td.history ul li {
  line-height:15px;
  font-size:0.9em;
}
.biblioForm .message { display:none; }
p.biblio-reviewer-name { margin-left:40%; }
#biblio-list-buttons {
  float:right;
  position:absolute;
  top:0;
  right:0px;
  margin-right:15px;
}
@media (max-width: 1200px) {
  #biblio-list-buttons { top:-10px; }
}
@media (max-width: 994px) {
  #biblio-list-buttons { top:-45px; }
}
@media (max-width: 360px) {
  #biblio-list-buttons {
    float:none;
    position:relative;
    top:0;
    margin-right:0;
    margin-bottom:15px;
  }
  #biblio-list-buttons a.btn { margin:5px; }
}
#slideshow-container {
  position:relative;
  background:#FFF;
  text-align:center;
}
#slideshow-focus a.fancybox img { max-width:100%; }
.tab-focus {
  clear:both;
  position:absolute !important;
  top:26px;
  left:0;
  border:1px solid #C4CFD8;
  background:#F2F6F7;
}
.slideshow-options {
  text-align:right;
  margin:5px;
}
.download {
  display:inline-block;
  height:16px;
  width:0;
  padding:0 0 0 16px;
  background:url(/design-img/icon/picto_download_16x16.png) no-repeat;
  cursor:pointer;
  overflow:hidden;
}
#slide_next {
  display:inline-block;
  background:transparent url(/design-img/background/bg_carousel_next_9x16x3.gif) no-repeat left top;
  width:9px;
  height:16px;
  position:absolute;
  right:0px;
  bottom:30px;
  cursor:pointer;
}
#slide_prev {
  display:inline-block;
  background:transparent url(/design-img/background/bg_carousel_prev_9x16x3.gif) no-repeat left top;
  width:9px;
  height:16px;
  position:absolute;
  left:0px;
  bottom:30px;
  cursor:pointer;
}
#slideshow-clip {
  margin:0 20px;
  height:76px;
  overflow:hidden;
  position:relative;
}
.slideshow-item {
  display:inline-block;
  margin:2px 2px;
  vertical-align:top;
}
.slideshow-box {
  position:relative;
  background:#000;
  overflow:hidden;
}
.photo { text-align:center; }
.slideshow-img {
  height:70px;
  cursor:pointer;
  display:block;
}
.slideshow-hide {
  position:absolute;
  top:-9999px;
  left:-9999px;
}
.display-none { display:none; }
table.emblem-authorities {
  table-layout:fixed;
  width:100%;
}
table.emblem-authorities td { padding:3px 3px; }
table.emblem-authorities td span { display:block; }
table.emblem-authorities td span:empty { display:none; }
table.emblem-authorities td span.emblem-authorities-name { font-weight:bold; }
table.emblem-authorities td.emblem-authorities-label {
  text-align:center;
  font-style:italic;
  font-weight:bold;
  padding-bottom:0.7em;
}
table.emblem-authorities td.emblem-authorities-cell { vertical-align:top; }
table.emblem-authorities td.emblem-left-cell {
  width:49%;
  margin-right:1%;
}
table.emblem-authorities h2 {
  color:#0872cb;
  margin-bottom:0;
}
input#emblem_authorities_search { position:relative; }
label.emblem-search-label {
  position:relative;
  top:-0.38em;
}
#hdtb.notl a, #hdtb.notl div, #hdtb.notl li {
  outline-width:0;
  outline:none;
}
#hdtb-msb {
  display:inline-block;
  float:left;
  position:relative;
  white-space:nowrap;
}
body.vasq #hdtbSum {
  height:59px;
  line-height:54px;
}
#hdtb {
  background:#fff;
  color:#666;
  font-size:12px;
  outline-width:0;
  outline:none;
  position:relative;
  z-index:102;
}
#hdtb-msb {
  display:inline-block;
  float:left;
  position:relative;
  white-space:nowrap;
}
.hdtb-mitem a {
  display:inline-block;
  text-transform:capitalize;
}
.hdtb-mitem a {
  margin:0 8px;
  padding:0 8px;
}
.hdtb-mitem a, #hdtb-more-mn a {
  padding:0 10px;
  color:#777;
  text-decoration:none;
  display:block;
  font-size:12px;
}
#hdtb-msb .hdtb-mitem.hdtb-msel, #hdtb-msb .hdtb-mitem.hdtb-msel-pre {
  border-bottom:3px solid #4285f4;
  color:#4285f4;
  font-weight:bold;
  height:35px;
  margin:2px 8px 0;
  padding:0 8px;
}
@media screen and (max-width: 992px) {
  #hdtb-msb .hdtb-mitem.hdtb-msel, #hdtb-msb .hdtb-mitem.hdtb-msel-pre { padding:0 2px; }
}
#appbar {
  background:white;
  -webkit-box-sizing:border-box;
  width:100%;
}
body.vasq .ab_tnav_wrp { height:43px; }
#hdtb-msb>.hdtb-mitem:first-child, .ab_tnav_wrp, #cnt #center_col, .mw #center_col { margin-left:10px; }
@media screen and (max-width: 992px) {
  #hdtb-msb>.hdtb-mitem:first-child, .ab_tnav_wrp, #cnt #center_col, .mw #center_col { margin-left:0; }
}
#sbfrm_l { visibility:inherit !important; }
body.vasq #resultStats { line-height:43px; }
#resultStats {
  position:absolute;
  top:0;
  -webkit-transition:all 220ms ease-in-out;
  font-size:14px;
}
#resultStats { color:#808080; }
#resultStats {
  padding-left:16px;
  padding-top:0;
  padding-bottom:0;
  padding-right:8px;
}
#resultStats {
  line-height:35px;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}
.search-help {
  font-size:0.8em;
  width:300px;
  display:inline-block;
  vertical-align:top;
  margin-left:40px;
  font-style:italic;
}
.container.sp-country { padding-right:0; }
.container.sp-country h1 { color:#06a1b5; }
.container.sp-country .block_home-country {
  margin-bottom:1.5%;
  position:relative;
  overflow:hidden;
  vertical-align:top;
  padding-bottom:40px;
  max-height:500px;
}
.container.sp-country .block_home-country .title_block-home {
  margin:0;
  padding:10px 0 10px;
  font-weight:bold;
  font-size:1.3em;
  color:#212121;
}
.container.sp-country .block_home-country .link-all {
  position:absolute;
  display:block;
  bottom:0;
  right:0;
  width:100%;
  height:40px;
  padding:9px 9px;
  border:none;
  border-bottom:2px solid #dadada;
}
.container.sp-country .block_home-country .block_wrapper-home-country article {
  border-top:1px solid #ebebeb;
  padding-top:10px;
}
.container.sp-country .block_home-country .block_wrapper-home-country article:first-child {
  border:none;
  padding-top:0;
}
.container.sp-country .block_home-country .block_wrapper-home-country article .picture.thumb { margin:0 20px 15px 0; }
.container.sp-country .block_home-country .block_wrapper-home-country header { margin-bottom:10px; }
.container.sp-country .block_home-country .block_wrapper-home-country header h2.title.news {
  margin:10px 0;
  font-size:1.5rem;
  display:inline;
  font-weight:bold;
}
.container.sp-country .block_home-country .block_wrapper-home-country header h2.title.news a { color:#212121; }
.container.sp-country .block_home-country .block_wrapper-home-country .news-description { display:none; }
.container.sp-country .block_home-country .block_wrapper-home-country .sub-list { margin:0 0 0 10px; }
.container.sp-country .block_home-country .block_wrapper-home-country .sub-list.sp { padding-bottom:7px; }
.container.sp-country .block_home-country .block_wrapper-home-country .sub-list .title-4 {
  display:inline-block;
  margin:0;
  color:#58595b;
  font-weight:normal;
}
.container.sp-country .block_home-country .block_wrapper-home-country .sub-list .sep {
  line-height:3px;
  margin-bottom:10px;
  color:#ebebeb;
  font-size:12px;
}
.container.sp-country .block_home-country .block_wrapper-home-country h3 { margin:0 0 7px 0; }
.container.sp-country .block_home-country .caption {
  padding:0;
  display:inline-block;
}
.container.sp-country .block_home-country .block_wrapper-home-country {
  max-height:400px;
  overflow-x:hidden;
  overflow-y:auto;
  margin:10px;
}
.container.sp-country .block_home-country .block_wrapper-home-country .wrapper-ref { padding:0 10px; }
.container.sp-country .block_home-country .block_wrapper-home-country .wrapper-ref+.wrapper-ref {
  padding-top:10px;
  border-top:1px solid #c7c7c7;
}
.container.sp-country .block_home-country .block_wrapper-home-country .wrapper-ref article {
  padding-bottom:10px;
  border:none;
}
.container.sp-country .block_home-country .block_wrapper-home-country .wrapper-ref .country { font-weight:bold; }
.container.sp-country .block_home-country .block_wrapper-home-country .wrapper-ref .country.caption { font-weight:normal; }
.container.sp-country .block_home-country .block_wrapper-home-country .wrapper-ref h2 {
  display:inline-block;
  font-weight:normal;
  font-size:1.5rem;
}
.container.sp-country .block_home-country .block_wrapper-home-country .wrapper-ref .newspaper { margin:10px 0 0; }
@media screen and (max-width: 768px) {
  .container.sp-country .block_home-country .block_wrapper-home-country {
    margin:0;
    height:auto;
  }
}
.container.sp-country .block_list-home-country {
  padding:15px;
  margin-bottom:1.4%;
}
.container.sp-country .block_list-home-country h2 { margin-top:0; }
.container.sp-country .block_list-home-country .listing .article_item {
  margin:0;
  padding:0;
}
.container.sp-country .block_list-home-country .block_list {
  display:inline-block;
  vertical-align:top;
  min-height:180px;
  margin-bottom:1%;
}
.container.sp-country .block_list-home-country .block_list ul { padding:0; }
.container.sp-country .block_list-home-country .block_list .title.news { color:#58595b; }
.container.sp-country .block_list-home-country .block_list .list-title { font-size:1.14em; }
.container.sp-country .block_list-home-country .block_list h3 {
  font-weight:normal;
  color:#58595b;
  font-size:14px;
  margin-right:12px;
}
.container.sp-country .block_list-home-country .block_list .country-colon-right {
  max-height:165px;
  overflow:-moz-scrollbars-vertical;
  overflow-x:hidden;
  overflow-y:auto;
  background-color:transparent;
  margin-top:15px;
}
.container.sp-country .block_list-home-country .block_list ::-webkit-scrollbar, .container.sp-country .block_list-home-country .block_list ::-moz-scrollbar {
  -webkit-appearance:none;
  width:10px;
}
.container.sp-country .block_list-home-country .block_list ::-webkit-scrollbar-thumb, .container.sp-country .block_list-home-country .block_list ::-moz-scrollbar-thumb {
  border-radius:5px;
  background-color:rgba(0,0,0,0.5);
  -webkit-box-shadow:0 0 1px rgba(255,255,255,0.5);
}
.container.sp-country .block_list-home-country .block_list.sp-list {
  margin-top:-5px;
  font-size:0.8em;
}
.container.sp-country .block_list-home-country .block_list.sp-list a {
  display:inline;
  color:#0872cb;
  font-weight:bold;
}
@media screen and (max-width: 992px) {
  .container.sp-country .block_list-home-country .block_list { margin-right:10px; }
}
@media screen and (max-width: 768px) {
  .container.sp-country .block_list-home-country { width:100%; }
}
@media screen and (max-width: 768px) {
  .container.sp-country { padding-right:20px; }
  .container.sp-country .block_home-country {
    width:100%;
    margin-right:0;
    min-height:100px;
    max-height:240px;
    margin-bottom:25px;
  }
  .container.sp-country .block_home-country .block_wrapper-home-country { height:180px; }
  .container.sp-country .block_list-home-country .sp-video { display:none; }
  .container.sp-country .block_list-home-country .block_list.sp-list {
    width:100%;
    margin-bottom:15px;
    display:block;
    min-height:65px;
  }
  .container.sp-country .block_list-home-country .block_list.sp-list .country-colon-right { max-height:95px; }
}
.country-title {
  text-transform:capitalize;
  text-transform:uppercase;
  padding:10px;
  margin:0;
  color:#212121;
  text-align:center;
  font-size:1.2em;
}
.country-title .link { color:#212121; }
.country-left-bar-item {
  display:inline-block;
  border-top:1px solid #82add0;
  padding:10px 0px;
  width:100%;
  font-weight:bold;
}
.country-colon-right {
  padding:5px;
  background-color:#ebebeb;
  width:auto;
  max-height:110px;
  overflow-x:hidden;
  overflow-y:auto;
  margin-top:3px;
}
#main #page-main .country-left-bar {
  float:right;
  font-size:0.86em;
  font-weight:bold;
  padding:0;
  margin:0 0 10px 0;
  border-right:0px solid #FFF;
  background:#dadada;
  width:275px;
}
body #main .title.ongletcountry {
  font-size:2.85em;
  font-weight:bold;
  line-height:1.1em;
  color:#414042;
  margin:0 0 20px 0;
}
#secondary { display:none; }
#content { background:#FFFFFF; }
#primary {
  margin:0;
  width:746px;
}
.ngo-accredited table.table_design td.ref { width:40%; }
.ngo-accredited table.table_design td.data { width:60%; }
table.org_inst td.ref {
  font-size:0.9em;
  width:35%;
}
table.org_inst td.data {
  font-size:0.85em;
  width:65%;
}
table.org_inst p.detail { margin-top:5px; }
dl.detail, p.detail_accre {
  margin:5px 0 5px 0px;
  border:1px dotted black;
  padding:5px;
  background:#F0F0F0;
}
dl.detail dd { margin:0px 0px 0px 15px; }
selected { background-color:#FFF6BF; }
#page-main > div.country-left-bar > ul > li > ul > h2 {
  font-size:1.08em;
  color:#414042;
  font-weight:bold;
}
.block_chronos {
  padding:15px;
  border-top:2px solid #fff;
  margin-bottom:1.4%;
}
.tabs {
  position:relative;
  margin:0;
  padding:0;
}
.slider-down {
  cursor:pointer;
  background:transparent url(/design-img/icons/plus-15.png) no-repeat right center;
  padding:0 15px 0 0;
}
.slider-up {
  cursor:pointer;
  background:transparent url(/design-img/icons/minus-15.png) no-repeat right center;
  padding:0 15px 0 0;
}
.filter fieldset div.checkbox { display:inline-block; }
.filter fieldset input[type=checkbox] {
  display:inline-block;
  margin-left:0;
  position:relative;
  top:2px;
}
.filter fieldset span#less_filters, .filter fieldset span#more_filters {
  text-decoration:underline;
  margin-right:10px;
  cursor:pointer;
}
.filter fieldset span#less_filters { float:right; }
.filter fieldset input[type=text] { margin:0 5px 0 5px; }
.filter fieldset.activity-funds label {
  cursor:pointer;
  display:block;
  user-select:none;
  -moz-user-select:none;
  -webkit-user-select:none;
  -ms-user-select:none;
}
.filter fieldset.activity-funds .activity-donor-country {
  display:inline-block;
  margin-top:15px;
  margin-right:12px;
}
.filter fieldset .activity-funds-left {
  display:inline-block;
  width:59%;
  margin-right:1%;
}
.filter fieldset .activity-funds-right {
  vertical-align:top;
  display:inline-block;
  width:39%;
}
.filter fieldset .activity-beneficiaries-left {
  display:inline-block;
  width:49%;
  margin-right:1%;
}
.filter fieldset .activity-beneficiaries-left label[for=filter-country] {
  margin-right:12px;
  margin-bottom:30px;
}
.filter fieldset .activity-beneficiaries-left label:not([for=filter-country]) {
  cursor:pointer;
  display:block;
  user-select:none;
  -moz-user-select:none;
  -webkit-user-select:none;
  -ms-user-select:none;
  display:inline-block;
}
.filter fieldset .activity-beneficiaries-right {
  display:inline-block;
  width:49%;
  vertical-align:top;
}
.filter fieldset .activity-beneficiaries-right label {
  cursor:pointer;
  display:block;
  user-select:none;
  -moz-user-select:none;
  -webkit-user-select:none;
  -ms-user-select:none;
}
.filter fieldset.activity-period .activity-period-left {
  display:inline-block;
  width:72%;
  margin-right:0%;
}
.filter fieldset.activity-period .activity-period-right {
  display:inline-block;
  width:27%;
}
.filter fieldset.activity-period .activity-period-after {
  display:inline-block;
  width:24%;
}
.filter fieldset.activity-period .activity-period-before { margin-right:10px; }
.filter fieldset.activity-period .activity-date-helper {
  display:inline-block;
  margin:0px 0px 10px 0px;
  font-style:italic;
}
.filter fieldset.activity-period #activity_now, .filter fieldset.activity-period #activity_this_year, .filter fieldset.activity-period #activity_this_biennium { cursor:pointer; }
.activity-global-results {
  font-style:italic;
  display:block;
  margin:auto;
  text-align:center;
}
.form .activity-form-buttons-wrapper {
  text-align:center;
  margin:25px auto 0 auto;
}
.form button[type=submit] { margin-right:10px; }
.fc {
  direction:ltr;
  text-align:left;
}
.fc table {
  border-collapse:collapse;
  border-spacing:0;
}
html .fc, .fc table { font-size:1em; }
.fc td, .fc th {
  padding:0;
  vertical-align:top;
}
.fc-header td { white-space:nowrap; }
.fc-header-left {
  width:25%;
  text-align:left;
}
.fc-header-center { text-align:center; }
.fc-header-right {
  width:25%;
  text-align:right;
}
.fc-header-title {
  display:inline-block;
  vertical-align:top;
}
.fc-header-title h2 {
  margin-top:0;
  white-space:nowrap;
}
.fc .fc-header-space { padding-left:10px; }
.fc-header .fc-button {
  margin-bottom:1em;
  vertical-align:top;
}
.fc-header .fc-button { margin-right:-1px; }
.fc-header .fc-corner-right, .fc-header .ui-corner-right { margin-right:0; }
.fc-header .fc-state-hover, .fc-header .ui-state-hover { z-index:2; }
.fc-header .fc-state-down { z-index:3; }
.fc-header .fc-state-active, .fc-header .ui-state-active { z-index:4; }
.fc-content { clear:both; }
.fc-view {
  width:100%;
  overflow:hidden;
}
.fc-widget-header, .fc-widget-content { border:1px solid #ddd; }
.fc-state-highlight { background:#fcf8e3; }
.fc-cell-overlay {
  background:#bce8f1;
  opacity:.3;
  filter:alpha(opacity=30);
}
.fc-button {
  position:relative;
  display:inline-block;
  padding:0 .6em;
  overflow:hidden;
  height:1.9em;
  line-height:1.9em;
  white-space:nowrap;
  cursor:pointer;
}
.fc-state-default { border:1px solid; }
.fc-state-default.fc-corner-left {
  border-top-left-radius:4px;
  border-bottom-left-radius:4px;
}
.fc-state-default.fc-corner-right {
  border-top-right-radius:4px;
  border-bottom-right-radius:4px;
}
.fc-text-arrow {
  margin:0 .1em;
  font-size:2em;
  font-family:"Courier New", Courier, monospace;
  vertical-align:baseline;
}
.fc-button-prev .fc-text-arrow, .fc-button-next .fc-text-arrow { font-weight:bold; }
.fc-button .fc-icon-wrap {
  position:relative;
  float:left;
  top:50%;
}
.fc-button .ui-icon {
  position:relative;
  float:left;
  margin-top:-50%;
  *margin-top:0;
  *top:-50%;
}
.fc-state-default {
  background-color:#f5f5f5;
  background-image:-moz-linear-gradient(top,#ffffff,#e6e6e6);
  background-image:-webkit-gradient(linear,0 0,0 100%,from(#ffffff),to(#e6e6e6));
  background-image:-webkit-linear-gradient(top,#ffffff,#e6e6e6);
  background-image:-o-linear-gradient(top,#ffffff,#e6e6e6);
  background-image:linear-gradient(to bottom,#ffffff,#e6e6e6);
  background-repeat:repeat-x;
  border-color:#e6e6e6 #e6e6e6 #bfbfbf;
  border-color:rgba(0,0,0,0.1) rgba(0,0,0,0.1) rgba(0,0,0,0.25);
  color:#333;
  text-shadow:0 1px 1px rgba(255,255,255,0.75);
  box-shadow:inset 0 1px 0 rgba(255,255,255,0.2), 0 1px 2px rgba(0,0,0,0.05);
}
.fc-state-hover, .fc-state-down, .fc-state-active, .fc-state-disabled {
  color:#333333;
  background-color:#e6e6e6;
}
.fc-state-hover {
  color:#333333;
  text-decoration:none;
  background-position:0 -15px;
  -webkit-transition:background-position 0.1s linear;
  -moz-transition:background-position 0.1s linear;
  -o-transition:background-position 0.1s linear;
  transition:background-position 0.1s linear;
}
.fc-state-down, .fc-state-active {
  background-color:#cccccc;
  background-image:none;
  outline:0;
  box-shadow:inset 0 2px 4px rgba(0,0,0,0.15), 0 1px 2px rgba(0,0,0,0.05);
}
.fc-state-disabled {
  cursor:default;
  background-image:none;
  opacity:0.65;
  filter:alpha(opacity=65);
  box-shadow:none;
}
.fc-event {
  border:1px solid #3a87ad;
  background-color:#3a87ad;
  color:#fff;
  font-size:.85em;
  cursor:default;
}
a.fc-event { text-decoration:none; }
a.fc-event, .fc-event-draggable { cursor:pointer; }
.fc-rtl .fc-event { text-align:right; }
.fc-event-inner {
  width:100%;
  height:100%;
  overflow:hidden;
}
.fc-event-time, .fc-event-title { padding:0 1px; }
.fc .ui-resizable-handle {
  display:block;
  position:absolute;
  z-index:99999;
  overflow:hidden;
  font-size:300%;
  line-height:50%;
}
.fc-event-hori {
  border-width:1px 0;
  margin-bottom:1px;
}
.fc-ltr .fc-event-hori.fc-event-start, .fc-rtl .fc-event-hori.fc-event-end {
  border-left-width:1px;
  border-top-left-radius:3px;
  border-bottom-left-radius:3px;
}
.fc-ltr .fc-event-hori.fc-event-end, .fc-rtl .fc-event-hori.fc-event-start {
  border-right-width:1px;
  border-top-right-radius:3px;
  border-bottom-right-radius:3px;
}
.fc-event-hori .ui-resizable-e {
  top:0 !important;
  right:-3px !important;
  width:7px !important;
  height:100% !important;
  cursor:e-resize;
}
.fc-event-hori .ui-resizable-w {
  top:0 !important;
  left:-3px !important;
  width:7px !important;
  height:100% !important;
  cursor:w-resize;
}
.fc-event-hori .ui-resizable-handle { _padding-bottom:14px; }
table.fc-border-separate { border-collapse:separate; }
.fc-border-separate th, .fc-border-separate td { border-width:1px 0 0 1px; }
.fc-border-separate th.fc-last, .fc-border-separate td.fc-last { border-right-width:1px; }
.fc-border-separate tr.fc-last th, .fc-border-separate tr.fc-last td { border-bottom-width:1px; }
.fc-border-separate tbody tr.fc-first td, .fc-border-separate tbody tr.fc-first th { border-top-width:0; }
.fc-grid th { text-align:center; }
.fc .fc-week-number {
  width:22px;
  text-align:center;
}
.fc .fc-week-number div { padding:0 2px; }
.fc-grid .fc-day-number {
  float:right;
  padding:0 2px;
}
.fc-grid .fc-other-month .fc-day-number {
  opacity:0.3;
  filter:alpha(opacity=30);
}
.fc-grid .fc-day-content {
  clear:both;
  padding:2px 2px 1px;
}
.fc-grid .fc-event-time { font-weight:bold; }
.fc-rtl .fc-grid .fc-day-number { float:left; }
.fc-rtl .fc-grid .fc-event-time { float:right; }
.fc-agenda table { border-collapse:separate; }
.fc-agenda-days th { text-align:center; }
.fc-agenda .fc-agenda-axis {
  width:50px;
  padding:0 4px;
  vertical-align:middle;
  text-align:right;
  white-space:nowrap;
  font-weight:normal;
}
.fc-agenda .fc-week-number { font-weight:bold; }
.fc-agenda .fc-day-content { padding:2px 2px 1px; }
.fc-agenda-days .fc-agenda-axis { border-right-width:1px; }
.fc-agenda-days .fc-col0 { border-left-width:0; }
.fc-agenda-allday th { border-width:0 1px; }
.fc-agenda-allday .fc-day-content {
  min-height:34px;
  _height:34px;
}
.fc-agenda-divider-inner {
  height:2px;
  overflow:hidden;
}
.fc-widget-header .fc-agenda-divider-inner { background:#eee; }
.fc-agenda-slots th { border-width:1px 1px 0; }
.fc-agenda-slots td {
  border-width:1px 0 0;
  background:none;
}
.fc-agenda-slots td div { height:20px; }
.fc-agenda-slots tr.fc-slot0 th, .fc-agenda-slots tr.fc-slot0 td { border-top-width:0; }
.fc-agenda-slots tr.fc-minor th, .fc-agenda-slots tr.fc-minor td { border-top-style:dotted; }
.fc-agenda-slots tr.fc-minor th.ui-widget-header { *border-top-style:solid; }
.fc-event-vert { border-width:0 1px; }
.fc-event-vert.fc-event-start {
  border-top-width:1px;
  border-top-left-radius:3px;
  border-top-right-radius:3px;
}
.fc-event-vert.fc-event-end {
  border-bottom-width:1px;
  border-bottom-left-radius:3px;
  border-bottom-right-radius:3px;
}
.fc-event-vert .fc-event-time {
  white-space:nowrap;
  font-size:10px;
}
.fc-event-vert .fc-event-inner {
  position:relative;
  z-index:2;
}
.fc-event-vert .fc-event-bg {
  position:absolute;
  z-index:1;
  top:0;
  left:0;
  width:100%;
  height:100%;
  background:#fff;
  opacity:.25;
  filter:alpha(opacity=25);
}
.fc .ui-draggable-dragging .fc-event-bg, .fc-select-helper .fc-event-bg { display:none \9; }
.fc-event-vert .ui-resizable-s {
  bottom:0 !important;
  width:100% !important;
  height:8px !important;
  overflow:hidden !important;
  line-height:8px !important;
  font-size:11px !important;
  font-family:monospace;
  text-align:center;
  cursor:s-resize;
}
.fc-agenda .ui-resizable-resizing { _overflow:hidden; }
.rp ul {
  margin:0 !important;
  padding-left:20px !important;
}
.rp p { margin:15px 0 3px 0 !important; }
.rp p:first-child { margin:0 0 3px 0 !important; }
.rp-fieldset fieldset div { border:1px solid transparent; }
.rp-fieldsetdiv#rp div { background-color:#F2F6F7; }
.periodic-reporting article, .nomination-file {
  max-height:300px;
  overflow-y:auto;
  overflow-wrap:break-word;
}
.jumbotron { padding:14px; }
.jumbotron p {
  font-size:14px;
  margin-bottom:0;
}
.jumbotron ul { margin-bottom:0; }
.jumbotron h2 {
  border-bottom:1px solid gray;
  margin-top:0;
}
.x-loader {
  width:100%;
  height:100%;
  border:1px solid lightgray;
  padding:5px;
  margin-bottom:10px;
}
.xx-box-d { border:3px dashed gray; }
.drop-container {
  opacity:0.5;
  height:70%;
  position:relative;
}
.drop-container:before {
  content:'+';
  height:100%;
  width:100%;
  color:#06a1b5;
  position:absolute;
  top:3%;
  left:36%;
  font-size:200px;
  opacity:0.2;
}
.drop-target {
  height:100%;
  width:100%;
  opacity:0.5;
}
.valid-zone { background-color:red; }
.workshopItem {
  color:black;
  font-weight:bold;
  font-size:12px;
  margin:2px;
  padding:5px;
}
.scrollbar-rail {
  height:505px;
  overflow:scroll;
}
.choosen td {
  color:#06a1b5;
  font-weight:bold;
  background-color:#B2D6F2 !important;
}
.wrapper_drop-container p {
  color:#06a1b5;
  font-weight:bold;
}
.wrapper_drop-container p small {
  font-weight:normal;
  color:#212121;
}
.wrapper_drop-container .text-danger {
  color:red;
  font-size:22px;
  cursor:pointer;
  top:10%;
}
.wrapper_drop-container .drop-container { height:250px; }
.wrapper_drop-container .drop-container .drop-target {
  height:100%;
  overflow:auto;
}
.wrapper_workflow-duration {
  margin:20px 0 10px;
  font-weight:bold;
}
.wrapper_workflow-duration label { font-weight:bold; }
.wrapper_workflow-duration .label-duration {
  font-size:1.3em;
  color:#115A9E;
}
.wrapper_workflow-lang { margin:40px 0 20px; }
.table_design + .scrollbar-rail { margin-top:-20px; }
.fancybox-title-outside-wrap {
  color:black;
  text-align:left;
  padding-left:10px;
}
#window-feedback h1 { font-size:1.3em; }
.decision-container .decision-label {
  font-weight:bold;
  float:left;
}
.decision-container .decision-list {
  padding:0;
  overflow:auto;
}
