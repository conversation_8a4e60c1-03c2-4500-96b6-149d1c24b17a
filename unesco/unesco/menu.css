/* Dropdown menu */

#menu {
    position: absolute;
    /*
    left: 10px;
    top: 10px;
    */
    display: inline-block;
    z-index: 50;
}

#menu-button {
    background-color: rgba(0,0,0,.5);
    color: white;
    border: none;
    cursor: pointer;
    text-align: center;
    font-weight: 600;
    height: 50px;
    width: 50px;
    font-size: 40px;
    padding: 0;
    border-radius: 50%;
}

#menu-button:after {
    content: '+';
    padding-top: 5px;
}

#menu:hover #menu-button {
    padding-bottom: 8px;
}

#menu:hover #menu-button:after {
    content: '-';
}

#menu:hover #menu-button {
    background-color: rgba(0,0,0,.75);
}

#menu ul {
    display: none;
    position: absolute;
    font-family: 'Oswald', sans-serif;
    text-transform: uppercase;
    font-weight: 400;
    letter-spacing: 0.02em;
    font-size: 16px;
}

#menu ul li {
    color: white;
    padding: 6px 16px;
    text-decoration: none;
    display: block;
    cursor: pointer;
    margin-top: 6px;
    background-color: rgba(0,0,0,.5);
    border-radius: 25px;
    white-space: nowrap;
}

#menu ul li a {
    text-decoration: none;
    color: white;
}

#menu ul li:hover {
    background-color: rgba(0,0,0,.75);
}

#menu:hover > ul {
    display: block;
}

#menu li > ul {
    display: none;
    position: absolute;
    left: 100%;
    top: -6px;
    padding-left: 6px;
}

#menu li:hover > ul {
    display: inline-block;
}


/* Vertical menu - overwrites what precedes */


#menu:hover > ul {
    display: inline-block;
    padding-left: 6px;
    margin-top: 0px;
}

/* Button offset */

.menu-pos-1 {
    top: 10px;
    left: 10px;
}

.menu-pos-2 {
    top: 70px;
    left: 10px;
}

.menu-pos-3 {
    top: 130px;
    left: 10px;
}

.menu-pos-4 {
    top: 190px;
    left: 10px;
}

.menu-pos-5 {
    top: 250px;
    left: 10px;
}

/* Single button */

.single-button {
    position: absolute;
    z-index: 50;
    background-color: rgba(0,0,0,.5);
    color: white;
    padding: 16px;
    border: none;
    cursor: pointer;
    text-align: center;
    font-weight: 600;
    height: 50px;
    width: 50px;
    font-size: 25px;
    padding: 0;
    border-radius: 50%;
}

.single-button:hover {
    background-color: rgba(0,0,0,.75);
}

/* SDG Wheel */

#sdg-wheel {
    position: absolute;
    top: 10px;
    right: 10px;
    height:110px;
    width: 110px;
    background-image: url(../../../sdg-single/img/wheel_transparent.png);
    background-size: contain;
    cursor: pointer;
    z-index: 50;
}