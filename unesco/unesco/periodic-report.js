$(document).ready(function () {

    var showTab = function (type, href) { 

		var elt = $(type+" "+href);
        elt.fadeIn();

        var targetLi = $("li:has(a[href='"+href+"'])");

		targetLi.siblings().removeClass("tabItemFocus").addClass("tabItem");
		targetLi.removeClass("tabItem").addClass("tabItemFocus");
        $('.formPage'+href).siblings().css('display', 'none');
    };

    /**
     * if there is an existing anchor in the url , show corresponding tab
     * else show default tab
     */
    var jumpToWantedTab = function () {
        var hash = window.location.hash;
        var anchor = $('article.rp-fieldset a[href="'+hash+'"]');

        if (anchor.length > 0) {
            if (hash.indexOf('pr') >= 0) {
                showTab('.div-rp', hash);
                $(document).scrollTo(anchor,2000);
            }
            else if (hash.indexOf('usl') >= 0) {
                showTab('.div-usl', hash);
                $(document).scrollTo(anchor,2000);
            }
        }
    };

    /**
     * show default (second) tab of all reports
     */
    var showDefaultTabs = function (defNum) {
        var groups = $('.div-usl');
        for (var i = 0; i < groups.length; i++) {
            var fieldsets = $(groups[i]).find('.formPage');
            for (var j=0; j < fieldsets.length; j++) {
                $(fieldsets[j]).css('display', j==defNum ? 'block' : 'none');
                
            }
        }
    };

    showDefaultTabs(1);
    jumpToWantedTab();
    initFieldset();

    if ($('#formBtnPrevious').length) {
        $('#formBtnPrevious').click(function(event) {
            formMoveToPage('previous', event)
        });
    }

    if ($('#formBtnNext').length) {
        $('#formBtnNext').click(function(event) {
            formMoveToPage('next', event)
        });
    }

    if ($('.tabItemFocus')) {
        if ($('.tabItemFocus').hasClass('click_intro')) {
            getPageAsynchrone($('.tabItemFocus a'));
        }
    }

    $('#lists-tabs li.click_intro a').click(function(event) {
        getPageAsynchrone($(this));
    });

    initCheckboxAndComment();
    initBtnNavigation();
    afficheBtnNavigation();
    afficheProgressBar();
    resizeProgressBar();
});

$( window ).resize(function() {
    resizeProgressBar();
    window.setTimeout('resizeProgressBar()', 300);
});

function resizeProgressBar() {
    $( ".irs--big" ).each(function(el) {
        var temp = $(this).find('.irs-grid');
        var divlexcical = $(this).next().next();
        divlexcical.css('width', temp.width());
        divlexcical.css('left', temp.css('left'));
    });
}

function getPageAsynchrone(obj) {
    var hrefVar = obj.attr('href');
    if (hrefVar) {
        if ($(hrefVar)) {
            $(hrefVar).html('<img src="/design-img/ring.gif" />');

            var url = top.location.href;
            var formData = new FormData();
            var $postInputs = $('.generated-form input[type=hidden]:not([name^=formTreeID]):enabled');
            $postInputs.each(function (idx) {
                if (this.type != 'submit') {
                    formData.append(this.name, this.value);
                }
            });
            formData.append('ajaxDisplayPage', 'intro');

            $.ajax({
                url: url,
                type: 'POST',
                contentType: false,
                processData: false,
                data: formData
            }).done(function(response) {
                $(hrefVar).html(response);

                if (callBackScroll != null) {
                    var elEnCours = $("a[name='" + callBackScroll);
                    if (elEnCours.length) {
                        elEnCours = elEnCours[0];
                        window.setTimeout(function() {
                            scrollToObj(elEnCours);
                        }, '500');
                    }


                    callBackScroll = null;
                }
            }).always(function(response) {

            });
        }
    }
}

function afficheProgressBar() {
    if ($(".generated-form .pbar").length > 0) {
        $(".generated-form .pbar").ionRangeSlider({
            type: "single",
            min: 0,
            max: 100,
            step: 5,
            grid: false,
            skin: "big",
            grid_snap: true,
            block: false,
            from_fixed: false,  // fix position of FROM handle
            to_fixed: false,     // fix position of TO handle
            onStart: function (data) {
                // fired then range slider is ready
                coloriseSlider(data);
            },
            onChange: function (data) {
                coloriseSlider(data);
            },
            onUpdate: function (data) {
                coloriseSlider(data);
            }
        });

        $(".generated-form .pbarrdly").ionRangeSlider({
            type: "single",
            min: 0,
            max: 100,
            step: 5,
            grid: false,
            skin: "big",
            grid_snap: true,
            block: true,
            from_fixed: false,  // fix position of FROM handle
            to_fixed: false,     // fix position of TO handle
            onStart: function (data) {
                // fired then range slider is ready
                coloriseSlider(data);
            },
            onChange: function (data) {
                coloriseSlider(data);
            },
            onUpdate: function (data) {
                coloriseSlider(data);
            }
        });
    }
}

function coloriseSlider(data) {
    var percent = parseInt(data.from);

    var slider = $(data.slider[0]);
    slider = slider.find('.irs-bar');

    var lexical = slider.parent();
    lexical = lexical.next().next();

    var divNotation = '';
    var labelIndicator = '';
    var cls = '';
    var element = lexical.find('.indicator');
    element.each(function(el) {
        var percentTmp = parseInt($(this).prop('style').left.replace('%', ''));
        var widthTmp = parseInt($(this).prop('style').width.replace('%', ''));

        if (percent >= percentTmp && percent < (widthTmp + percentTmp)) {
            cls = $(this).attr('class').replace('indicator', '');
            cls = cls.replace(/ /gi, '');
            labelIndicator = $(this).html();
            divNotation = $(this);
        }
    });
    if (cls == '' && percent == 100) {
        cls = 'satisfied';
        labelIndicator = lblSatisfied;
    }

    slider.removeClass('irs-bar-not_satisfied');
    slider.removeClass('irs-bar-minimally');
    slider.removeClass('irs-bar-partially');
    slider.removeClass('irs-bar-largely');
    slider.removeClass('irs-bar-satisfied');
    slider.addClass('irs-bar-' + cls);

    try {
        var cible = lexical.parent().parent().prev();
        if (cible) {
            var cibleLegend = cible.find('legend');
            if (cibleLegend && cibleLegend.length > 0) {
                cible = cibleLegend.find('div');
                if (cible.length == 0) {
                   $(cibleLegend).append('<div style="display: inline-block;padding-left:5px"><div class="indicator indicatorresume ' + cls + '">' + labelIndicator + '</div></div>');
                } else {
                    $(cible).html('<div class="indicator indicatorresume ' + cls + '">' + labelIndicator + '</div>');
                }
            }
            /*cible = cible.find('.subblock');
            if (cible && cible.length > 0) {
                cible = cible[0];
                $(cible).html(labelIndicator);
            }*/
        }
    } catch(err) {
        console.log(err);
    }
}

function initBtnNavigation() {
    $('.tab-style').on( "click", function() {
        if (pageAutoSave) {
            if (timeoutGeneratedForm) {
                clearTimeout(timeoutGeneratedForm);
            }
            autoSave('autosaved', null, pageAutoSave);
        }
        afficheBtnNavigation();
        window.setTimeout('resizeProgressBar()', 300);
    });
}

function afficheBtnNavigation() {
    $('#formBtnNext').hide();
    $('#formBtnPrevious').hide();

    var cible = $('.tabItemFocus');
    if (cible && cible.next()) {
        if (cible.next().prop("tagName") == 'LI' && cible.next().hasClass("tabItem")) {
            $('#formBtnNext').show();
        }
    }
    if (cible && cible.prev()) {
        if (cible.prev().prop("tagName") == 'LI' && cible.prev().hasClass("tabItem")) {
            $('#formBtnPrevious').show();
        }
    }
}

function formMoveToPage(sens, e) {
    e.preventDefault();

    try {
        var cible = $('.tabItemFocus');
        if (sens == 'next') {
            if (cible && cible.next()) {
                cible = cible.next();
            }
        } else {
            if (cible && cible.prev()) {
                cible = cible.prev();
            }
        }
        if (cible) {
            if (cible.prop("tagName") == 'LI' && cible.hasClass("tabItem")) {
                cible.find('a:first-child').click();
                $('body,html').animate({
                        scrollTop: $('#form').offset().top + 'px',
                    }, 700
                );
            }
        }
    } catch(err) {
        console.log(err);
    }
}

function condionnalTest(champ, valeur) {
    var valInput = null;
    if (champ[0]) {
        switch (champ[0].type) {
            case 'radio':
            case 'checkbox':
                for (i = 0; i < champ.length; i++) {
                    if (champ[i].checked) {
                        if (valeur.toLowerCase() == champ[i].value.toLowerCase()) {
                            return true;
                        }
                    }
                }
                break;
            default:
                valInput = champ[0].value;
                break;
        }
    }
    if (valInput != null) {
        if (valInput.toLowerCase() == valeur.toLowerCase()) {
            return true;
        }
    }
    return false;
}
function condionnalTestResponded(champ, valeur) {
    var valInput = null;
    if (champ[0]) {
        switch (champ[0].type) {
            case 'radio':
            case 'checkbox':
                for (i = 0; i < champ.length; i++) {
                    if (champ[i].checked && valeur) {
                        return true;
                    }
                }
                break;
            default:
                valInput = champ[0].value;
                break;
        }
    }
    if (valInput != null) {
        if ((valInput.replace(/ /gi, '') != '') && valeur) {
            return true;
        }
    }
    if (!valeur) {
        return true;
    }
    return false;
}

function addInIndicator(obj, refID) {
    obj[refID] = [];
}
function addInIndicatorNotation(obj, refID, note, labelNote, idOption) {
    obj[refID].push({"note": note, "labelNote": labelNote, "idOption": idOption});
}
function addInFactor(obj, refID, val, weight) {
    obj[refID] = {"value": val, "weight": weight};
}
function addInFactorForNotation(obj, refID, val, weight) {
    if (!obj[refID]) obj[refID] = [];
    obj[refID].push({"value": val, "weight": weight});
}

var sysFieldFactorReference = new Object();
function addFactorReference(objID, factorID, weightMax) {
    if (!sysFieldFactorReference[objID]) sysFieldFactorReference[objID] = [];
    sysFieldFactorReference[objID].push({"factorID": factorID, "weightMax": weightMax});
}

function calculIndicator(keyID, type_appel) {
    var noteTotale = 0;
    if (type_appel == null) type_appel = '';

    try {
        if (typeof(sysFieldFactor) != 'undefined') {
            if (typeof(sysFieldFactor[keyID]) != 'undefined') {
                //on reset les compteur de points par factor
                for (var i in sysFieldFactor[keyID]) {
                    if ((i + '').indexOf('_detail_notations') == -1) {
                        if (typeof(sysFieldFactorReference[i]) != 'undefined') {
                            for (var jObj in sysFieldFactorReference[i]) {
                                //console.log(sysFieldFactorReference[i][jObj]);

                                if ($('#factor-note-' + sysFieldFactorReference[i][jObj]['factorID'])) {
                                    $('#factor-note-' + sysFieldFactorReference[i][jObj]['factorID']).html(0);
                                }
                            }
                        }
                    }
                }

                for (var i in sysFieldFactor[keyID]) {
                    if ((i + '').indexOf('_detail_notations') == -1) {
                        objFactor = $("input[name='formTreeID[question-" + i + "]']");
                        if (!objFactor[0]) objFactor = $("input[name='formTreeID[question-" + i + "][]']");
                        valInput = 0;

                        if (sysFieldFactor[keyID][i + '_detail_notations']) {
                            for (var jObj in objFactor) {
                                if (objFactor[jObj].checked) {
                                    for (var jNote in sysFieldFactor[keyID][i + '_detail_notations']) {
                                        if (objFactor[jObj].value == sysFieldFactor[keyID][i + '_detail_notations'][jNote]['value']) {
                                            valInput += parseFloat(sysFieldFactor[keyID][i + '_detail_notations'][jNote]['weight']);
                                        }
                                    }
                                }
                            }
                            valInput = Math.min(valInput, parseFloat(sysFieldFactor[keyID][i]["weight"]));
                            noteTotale += valInput;
                        } else {
                            valToTest = sysFieldFactor[keyID][i]["value"];
                            if (condionnalTest(objFactor, valToTest)) {
                                noteTotale += parseFloat(sysFieldFactor[keyID][i]["weight"]);
                                valInput = parseFloat(sysFieldFactor[keyID][i]["weight"]);
                            }
                        }
                        if (typeof(sysFieldFactorReference[i]) != 'undefined') {
                            for (var jObj in sysFieldFactorReference[i]) {
                                factorId = sysFieldFactorReference[i][jObj]['factorID'];
                                weightMax = sysFieldFactorReference[i][jObj]['weightMax'];
                                if ($('#factor-note-' + factorId)) {
                                    tmpNote = parseFloat($('#factor-note-' + factorId).html());
                                    if (isNaN(tmpNote)) tmpNote = 0;
                                    tmpNote += parseFloat(valInput);


                                    $('#factor-note-' +factorId).html(tmpNote + '/' + weightMax);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (type_appel == 'notation_factor') {
            return true;
        }

        if (typeof(sysFieldIndicator) != 'undefined') {
            if (typeof(sysFieldIndicator[keyID]) != 'undefined') {
                for (var i in sysFieldIndicator[keyID]) {
                    objIndicator = $("input[name='formTreeID[question-" + i + "]']");
                    if (!objIndicator[0]) objIndicator = $("input[name='formTreeID[question-" + i + "][]']");

                    //progressbar
                    if (objIndicator && objIndicator[0]) {
                        if (noteTotale > 100) noteTotale = 100;
                        //objIndicator[0].value = noteTotale;
                        objIndicator.data("ionRangeSlider").update( {from: noteTotale} );
                    } else {
                        //select
                        objIndicator = $("select[name='formTreeID[question-" + i + "]']");
                        if (!objIndicator[0]) objIndicator = $("select[name='formTreeID[question-" + i + "][]']");

                        if (objIndicator && objIndicator[0]) {
                            for (var j in sysFieldIndicator[keyID][i]) {
                                noteTmp = sysFieldIndicator[keyID][i][j]['note'];
                                noteTmp = parseFloat(noteTmp);

                                if (noteTotale <= noteTmp) {
                                    objIndicator[0].value = sysFieldIndicator[keyID][i][j]['idOption'];
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    } catch(error) {
        console.log(error);
    }
}

function resetInputVal(fieldset) {
    $inputs = $(fieldset).find('input, select, textarea');
    $inputs.each(function (idx) {
        if (this.type == 'text' || this.type == 'textarea') {
            this.value = ''
        } else if (this.type == 'checkbox' || this.type == 'radio') {
            this.checked = false;
        } else if (this.type == 'select' || this.type == 'select-one') {
            this.selectedIndex = 0;
        }
    });
}

function initFieldset() {
    $('.depth1>legend, .depth2>legend').bind( "click", function() {
        obj = $(this).parent().find('div.subblock:first')
        if (obj) {
            if (obj.css('display') == 'none') {
                obj.css('display', 'block');
            } else {
                obj.css('display', 'none');
            }
        }
    });
}

function initCheckboxAndComment() {
    $('.checkAndComment').each(function(idx) {
        if ($(this).is(':checked')) {
            var id = $(this).attr('id');
            id = id.replace(/fq/gi, 'fqCom');
            if ($('#' + id).length > 0) {  // Use .length instead of .size()
                $('#' + id).show();
            }
        }
    });

    $('.checkAndComment').change(function() {
        var id = $(this).attr('id');
        id = id.replace(/fq/gi, 'fqCom');
        if ($('#' + id).length > 0) {
            if ($(this).is(':checked')) {
                $('#' + id).find('input, select, textarea').each(function(idx) { this.value = this.value.replace('~NO~',''); });
                $('#' + id).show();
            } else {
                $('#' + id).hide();
                $('#' + id).find('input, select, textarea').each(function(idx) { this.value = this.value + '~NO~'; });
            }
        }
    });
}

var callBackScroll = null;
var tabNavigation = new Array();
function formGoTo(evt, linkFormTreeID, linkIdParent) {
    if (linkIdParent == null) {
        linkIdParent = '';
    }
    var ongletToSave = '';
    var elEnCours = $("a[name='" + linkFormTreeID);
    var elEnCoursIni = '';
    if (elEnCours.length) {
        elEnCours = elEnCours[0];
        //console.log(elEnCours);
        if (elEnCours) {
            elEnCoursIni = elEnCours;
            elEnCours = $(elEnCours).parent();
            while(elEnCours && elEnCours.length) {
                if (elEnCours.hasClass('formPage')) {
                    break;
                }
                elEnCours = elEnCours.parent();
            }
            if (elEnCours && elEnCours.hasClass('formPage')) {
                ongletToSave = elEnCours;
            }
        }
    }

    if (ongletToSave) {
        var obj = $('.tabItem a[href="#' + ongletToSave.attr('id') + '"]');
        if (obj.length == 0) {
            var obj = $('.tabItemFocus a[href="#' + ongletToSave.attr('id') + '"]');
        }

        if (obj.length) {
            if (!linkIdParent) {
                //on essai de recuperer le parent
                var elTmp = $(evt.target);
                elTmp.parent();
                while(elTmp && elTmp.length) {
                    if (elTmp.prop("tagName") && elTmp.prop("tagName").toLowerCase() == 'fieldset') {
                        if (elTmp.hasClass('depth2')) {
                            break;
                        }
                    }
                    elTmp = elTmp.parent();
                }
                if(elTmp && elTmp.length) {
                    while(elTmp && elTmp.length) {
                        if (elTmp.prop("tagName") && elTmp.prop("tagName").toLowerCase() == 'a') {
                            if (elTmp.prop('name').indexOf('#link') != -1) {
                                linkIdParent = elTmp.prop('name');
                                break;
                            }
                        }
                        elTmp = elTmp.prev();
                    }
                }
            }
            if (linkIdParent) {
                tabNavigation.push(linkIdParent);

                var indiceTmp = 1;
                if (location.hash.substr(-2) == '#1') {
                    indiceTmp++;
                }
                var urlTmp = urlIni.replace(location.hash, '') + '#' + indiceTmp;
                //on revient à l'url d'origine pour ne pas sortir de notre page
                window.history.pushState('forward', null, urlTmp);
            }


            obj[0].click();
            if ($(obj[0]).parent().hasClass('click_intro')) {
                callBackScroll = linkFormTreeID;
            } else {
                window.setTimeout(function() {
                    scrollToObj(elEnCoursIni);
                }, '500');
            }
        }
    }
}

function scrollToObj(obj) {
    if (obj) {
        //$(document).scrollTo(obj, 750);
        $('html, body').animate({ scrollTop: $(obj).offset().top}, 750);
    }
}

function summaryShow(objSpan, parentID) {
    var img = $(objSpan).find("img");
    if (img.length) {
        if (img[0].src.indexOf('close') != -1) {
            $(".summaryParent" + parentID).show();
            img[0].src = img[0].src.replace('close', 'open');
        } else {
            $(".summaryParent" + parentID).hide();
            img[0].src = img[0].src.replace('open', 'close');
        }
    } else {
        $(".summaryParent" + parentID).show();
    }
}

$(document).ready(function(){
    var $pendingForm = $('.generated-form');
    if ($pendingForm.length) {
        $(window).on('hashchange', function () {
            /*console.log( 'location.hash: ' + location.hash );
            console.log(tabNavigation);*/
            if (tabNavigation.length > 0) {
                var urlTmp = location.href;

                /*console.log(urlTmp);
                console.log(urlIni);*/
                //if (urlTmp == urlIni) {
                var lastElement = tabNavigation[tabNavigation.length - 1];
                tabNavigation.pop();
                formGoTo(event, lastElement);

                urlTmp = urlIni.replace(location.hash, '') + '#' + indiceIni;
                //on revient à l'url d'origine pour ne pas sortir de notre page
                //window.history.pushState('forward', null, urlTmp);
                //}

            }
        });
    }

});

var urlIni = '';
var indiceIni = 1;
jQuery(document).ready(function($) {
    var $pendingForm = $('.generated-form');
    if ($pendingForm.length) {
        if (window.history && window.history.pushState) {
            urlIni = location.href;

            if (location.hash.substr(-2) == '#' + indiceIni) {
                indiceIni++;
            }
            var urlTmp = urlIni.replace(location.hash, '') + '#' + indiceIni;

            window.history.pushState('forward', null, urlTmp);
        }
    }
});